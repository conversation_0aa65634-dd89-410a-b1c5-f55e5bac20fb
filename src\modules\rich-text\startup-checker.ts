/**
 * Rich Text Tabs 启动检查器
 * 在应用启动时自动检查并创建缺失的预设配置
 */

// D1Database 类型现在在全局作用域中可用
import type { EntityType, LanguageCode } from './schema';

interface PresetConfigData {
  entity_type: EntityType;
  language_code: LanguageCode;
  key: string;
  label: string;
  placeholder: string;
  icon: string;
  sort_order: number;
}

const PRESET_CONFIGS: PresetConfigData[] = [
  // Event 预设配置
  {
    entity_type: 'event',
    language_code: 'en',
    key: 'introduction',
    label: 'Introduction',
    placeholder: 'Enter event introduction...',
    icon: 'info',
    sort_order: 0,
  },
  {
    entity_type: 'event',
    language_code: 'zh',
    key: 'introduction',
    label: '介绍',
    placeholder: '输入活动介绍...',
    icon: 'info',
    sort_order: 0,
  },
  {
    entity_type: 'event',
    language_code: 'ja',
    key: 'introduction',
    label: '紹介',
    placeholder: 'イベント紹介を入力...',
    icon: 'info',
    sort_order: 0,
  },

  // Venue 预设配置
  {
    entity_type: 'venue',
    language_code: 'en',
    key: 'overview',
    label: 'Overview',
    placeholder: 'Enter venue overview...',
    icon: 'map-pin',
    sort_order: 0,
  },
  {
    entity_type: 'venue',
    language_code: 'zh',
    key: 'overview',
    label: '概览',
    placeholder: '输入场馆概览...',
    icon: 'map-pin',
    sort_order: 0,
  },
  {
    entity_type: 'venue',
    language_code: 'ja',
    key: 'overview',
    label: '概要',
    placeholder: '会場概要を入力...',
    icon: 'map-pin',
    sort_order: 0,
  },
];

function generatePresetId(
  entityType: string,
  languageCode: string,
  key: string
): string {
  return `preset-${entityType}-${languageCode}-${key}`;
}

/**
 * 检查并创建缺失的预设配置
 * 这个函数是幂等的，可以安全地重复调用
 */
export async function ensurePresetConfigs(db: D1Database): Promise<{
  checked: number;
  created: number;
  skipped: number;
}> {
  let createdCount = 0;
  let skippedCount = 0;

  try {
    const now = new Date().toISOString();

    for (const config of PRESET_CONFIGS) {
      // 检查是否已存在
      const existingStmt = db.prepare(`
        SELECT id FROM content_type_configs 
        WHERE entity_type = ? AND language_code = ? AND key = ? AND deleted_at IS NULL
      `);

      const existing = await existingStmt
        .bind(config.entity_type, config.language_code, config.key)
        .first();

      if (existing) {
        skippedCount++;
        continue;
      }

      // 创建新的预设配置
      const id = generatePresetId(
        config.entity_type,
        config.language_code,
        config.key
      );
      const insertStmt = db.prepare(`
        INSERT INTO content_type_configs (
          id, entity_type, language_code, key, label, placeholder, icon,
          sort_order, is_active, is_preset, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      await insertStmt
        .bind(
          id,
          config.entity_type,
          config.language_code,
          config.key,
          config.label,
          config.placeholder,
          config.icon,
          config.sort_order,
          1, // is_active = true
          1, // is_preset = true
          now,
          now
        )
        .run();

      createdCount++;
      console.log(
        `[RichTextTabs] 创建预设配置: ${config.entity_type}/${config.language_code}/${config.key}`
      );
    }

    if (createdCount > 0) {
      console.log(
        `[RichTextTabs] 预设配置初始化完成: 创建 ${createdCount} 条，跳过 ${skippedCount} 条`
      );
    }

    return {
      checked: PRESET_CONFIGS.length,
      created: createdCount,
      skipped: skippedCount,
    };
  } catch (error) {
    console.error('[RichTextTabs] 预设配置检查失败:', error);
    // 不抛出错误，避免影响应用启动
    return {
      checked: PRESET_CONFIGS.length,
      created: 0,
      skipped: 0,
    };
  }
}

/**
 * 检查预设配置的完整性
 * 返回缺失的配置列表
 */
export async function checkPresetConfigsIntegrity(db: D1Database): Promise<{
  isComplete: boolean;
  missing: PresetConfigData[];
  existing: number;
}> {
  try {
    const stmt = db.prepare(`
      SELECT entity_type, language_code, key 
      FROM content_type_configs 
      WHERE is_preset = 1 AND deleted_at IS NULL
    `);

    const result = await stmt.all();
    const existingConfigs = result.results as Array<{
      entity_type: string;
      language_code: string;
      key: string;
    }>;

    const missing = PRESET_CONFIGS.filter(
      (expected) =>
        !existingConfigs.some(
          (existing) =>
            existing.entity_type === expected.entity_type &&
            existing.language_code === expected.language_code &&
            existing.key === expected.key
        )
    );

    return {
      isComplete: missing.length === 0,
      missing,
      existing: existingConfigs.length,
    };
  } catch (error) {
    console.error('[RichTextTabs] 预设配置完整性检查失败:', error);
    return {
      isComplete: false,
      missing: PRESET_CONFIGS,
      existing: 0,
    };
  }
}
