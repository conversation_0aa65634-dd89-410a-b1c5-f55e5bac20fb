// D1Database 和 R2Bucket 类型现在在全局作用域中可用
// 简单的 HTML 清理函数，适用于 Cloudflare Workers 环境
import { v4 as uuidv4 } from 'uuid';
import { createContentRepository, type ContentRepository } from './repository';
import type {
  RichTextContent,
  CreateContentRequest,
  UpdateContentRequest,
  EntityType,
} from './schema';

// 定义缺失的类型
type ContentType = string;
type ContentResponse = Record<string, string>;
type BatchContentRequest = Record<string, string>;

/**
 * 简单的 HTML 清理函数
 * 移除脚本标签和危险属性，保留基本的富文本格式
 */
function sanitizeHTML(html: string): string {
  if (!html) return '';

  // 移除脚本标签和其他危险标签
  let cleaned = html
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<iframe[^>]*>[\s\S]*?<\/iframe>/gi, '')
    .replace(/<object[^>]*>[\s\S]*?<\/object>/gi, '')
    .replace(/<embed[^>]*>/gi, '')
    .replace(/<form[^>]*>[\s\S]*?<\/form>/gi, '')
    .replace(/<input[^>]*>/gi, '')
    .replace(/<textarea[^>]*>[\s\S]*?<\/textarea>/gi, '')
    .replace(/<select[^>]*>[\s\S]*?<\/select>/gi, '');

  // 移除危险的事件处理器属性
  cleaned = cleaned.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');

  // 移除 javascript: 协议
  cleaned = cleaned.replace(/javascript:/gi, '');

  return cleaned.trim();
}

export class ContentService {
  private repository: ContentRepository;

  constructor(db: D1Database) {
    this.repository = createContentRepository(db);
  }

  /**
   * 获取实体的所有内容
   */
  async getEntityContent(
    entityType: EntityType,
    entityId: string
  ): Promise<ContentResponse> {
    const contents = await this.repository.findByEntity(entityType, entityId);

    const response: ContentResponse = {};
    for (const content of contents) {
      response[content.content_type as keyof ContentResponse] = content.content;
    }

    return response;
  }

  /**
   * 获取实体的特定类型内容
   */
  async getEntityContentByType(
    entityType: EntityType,
    entityId: string,
    contentType: ContentType
  ): Promise<string | null> {
    const content = await this.repository.findByEntityAndType(
      entityType,
      entityId,
      contentType
    );
    return content?.content || null;
  }

  /**
   * 创建内容
   */
  async createContent(data: CreateContentRequest): Promise<RichTextContent> {
    // 清理HTML内容
    const cleanContent = sanitizeHTML(data.content);

    return await this.repository.create({
      ...data,
      content: cleanContent,
    });
  }

  /**
   * 更新内容
   */
  async updateContent(id: string, data: UpdateContentRequest): Promise<void> {
    // 清理HTML内容
    const cleanContent = sanitizeHTML(data.content);

    await this.repository.update(id, {
      content: cleanContent,
    });
  }

  /**
   * 创建或更新内容（upsert）
   */
  async upsertContent(
    entityType: EntityType,
    entityId: string,
    languageCode: string,
    contentType: ContentType,
    content: string
  ): Promise<RichTextContent> {
    // 清理HTML内容
    const cleanContent = sanitizeHTML(content);

    return await this.repository.upsert(
      entityType,
      entityId,
      languageCode,
      contentType,
      cleanContent
    );
  }

  /**
   * 批量更新实体的所有内容
   */
  async batchUpdateContent(
    entityType: EntityType,
    entityId: string,
    languageCode: string,
    data: BatchContentRequest
  ): Promise<ContentResponse> {
    const response: ContentResponse = {};

    // 遍历所有内容类型，进行批量更新
    for (const [contentType, content] of Object.entries(data)) {
      if (content && typeof content === 'string' && content.trim()) {
        const result = await this.upsertContent(
          entityType,
          entityId,
          languageCode,
          contentType as ContentType,
          content
        );
        response[contentType as keyof ContentResponse] = result.content;
      }
    }

    return response;
  }

  /**
   * 删除内容
   */
  async deleteContent(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * 删除实体的所有内容
   */
  async deleteEntityContent(
    entityType: EntityType,
    entityId: string
  ): Promise<void> {
    await this.repository.deleteByEntity(entityType, entityId);
  }

  /**
   * 获取内容列表（用于管理）
   */
  async getContentList(
    entityType: EntityType,
    entityId: string
  ): Promise<RichTextContent[]> {
    return await this.repository.findByEntity(entityType, entityId);
  }

  /**
   * 富文本编辑器图片上传
   */
  async uploadImage(
    file: globalThis.File,
    r2: R2Bucket
  ): Promise<{ url: string }> {
    // 复用images模块的验证逻辑
    this.validateImageFile(file);

    // 生成文件路径
    const timestamp = Date.now();
    const fileExtension = this.getFileExtension(file.name);
    const fileName = `${timestamp}_${uuidv4()}${fileExtension}`;
    const filePath = `/images/content/${fileName}`;

    // 上传到R2
    await this.uploadToR2(filePath, file, r2);

    return { url: filePath };
  }

  /**
   * 验证图片文件（复用images模块逻辑）
   */
  private validateImageFile(file: globalThis.File): void {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/svg+xml',
    ];
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.svg'];

    // 检查 MIME 类型
    if (!allowedTypes.includes(file.type)) {
      const error = new Error(
        `不支持的文件类型: ${file.type}。支持的类型: ${allowedTypes.join(', ')}`
      );
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件扩展名
    const fileName = file.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some((ext) =>
      fileName.endsWith(ext)
    );

    if (!hasValidExtension) {
      const error = new Error(
        `不支持的文件扩展名。支持的扩展名: ${allowedExtensions.join(', ')}`
      );
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件大小
    if (file.size > maxSize) {
      const error = new Error('文件大小超过限制（5MB）');
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件大小最小值
    if (file.size < 100) {
      const error = new Error('文件大小过小，可能不是有效的图片文件');
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件名长度
    if (file.name.length > 255) {
      const error = new Error('文件名过长（最大255字符）');
      (error as any).isValidationError = true;
      throw error;
    }

    // 检查文件名是否包含非法字符
    // eslint-disable-next-line no-control-regex
    const invalidChars = /[<>:"/\\|?*\u0000-\u001f]/;
    if (invalidChars.test(file.name)) {
      const error = new Error('文件名包含非法字符');
      (error as any).isValidationError = true;
      throw error;
    }
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : '';
  }

  /**
   * 上传文件到R2（复用images模块逻辑）
   */
  private async uploadToR2(
    path: string,
    file: globalThis.File,
    r2: R2Bucket
  ): Promise<void> {
    // 移除开头的斜杠，R2 key 不应该以斜杠开头
    const key = path.startsWith('/') ? path.slice(1) : path;

    // 使用 arrayBuffer() 而不是 stream() 来避免类型问题
    const fileBuffer = await file.arrayBuffer();

    await r2.put(key, fileBuffer, {
      httpMetadata: {
        contentType: file.type,
      },
    });
  }
}

// 工厂函数
export function createContentService(db: D1Database): ContentService {
  return new ContentService(db);
}
