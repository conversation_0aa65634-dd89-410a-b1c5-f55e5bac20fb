---
sidebar_position: 2
title: 数据库设计
description: Ayafeed API 数据库架构和设计说明
---

# 数据库设计

本文档详细介绍 Ayafeed API 的数据库架构设计和最佳实践。

## 🗄️ 数据库概览

Ayafeed API 使用 **Cloudflare D1** 作为主数据库，这是一个基于 SQLite 的分布式数据库服务。

### 技术特性

- **基于 SQLite**: 成熟稳定的 SQL 数据库引擎
- **全球分布**: 数据自动复制到全球边缘节点
- **ACID 事务**: 完整的事务支持
- **SQL 兼容**: 标准 SQL 语法支持

## 📊 数据库架构

### 核心表结构

```mermaid
erDiagram
    users ||--o{ bookmarks : creates
    users ||--o{ logs : generates
    circles ||--o{ appearances : participates
    events ||--o{ appearances : hosts
    artists ||--o{ appearances : creates

    users {
        string id PK
        string username
        string email
        string password_hash
        string role
        string locale
        datetime created_at
        datetime updated_at
    }

    circles {
        string id PK
        string name
        string name_ja
        string name_en
        text description
        text description_ja
        text description_en
        string website
        string twitter
        datetime created_at
        datetime updated_at
    }

    events {
        string id PK
        string name
        string name_ja
        string name_en
        text description
        text description_ja
        text description_en
        date start_date
        date end_date
        string venue
        string venue_ja
        string venue_en
        datetime created_at
        datetime updated_at
    }

    artists {
        string id PK
        string name
        string name_ja
        string name_en
        text bio
        text bio_ja
        text bio_en
        string website
        string twitter
        datetime created_at
        datetime updated_at
    }

    appearances {
        string id PK
        string circle_id FK
        string event_id FK
        string artist_id FK
        string booth_number
        text works
        text works_ja
        text works_en
        datetime created_at
        datetime updated_at
    }

    bookmarks {
        string id PK
        string user_id FK
        string target_type
        string target_id
        datetime created_at
    }

    logs {
        string id PK
        string user_id FK
        string action
        string resource
        string resource_id
        json metadata
        string ip_address
        string user_agent
        datetime created_at
    }
```

## 🔧 表设计详解

### 1. 用户表 (users)

存储用户账户信息和认证数据。

```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user',
    locale TEXT NOT NULL DEFAULT 'en',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
```

### 2. 社团表 (circles)

存储同人社团的基本信息，支持多语言。

```sql
CREATE TABLE circles (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ja TEXT,
    name_en TEXT,
    description TEXT,
    description_ja TEXT,
    description_en TEXT,
    website TEXT,
    twitter TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_circles_name ON circles(name);
CREATE INDEX idx_circles_created_at ON circles(created_at);
```

### 3. 事件表 (events)

存储展会和活动信息。

```sql
CREATE TABLE events (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    name_ja TEXT,
    name_en TEXT,
    description TEXT,
    description_ja TEXT,
    description_en TEXT,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    venue TEXT,
    venue_ja TEXT,
    venue_en TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_events_start_date ON events(start_date);
CREATE INDEX idx_events_end_date ON events(end_date);
CREATE INDEX idx_events_name ON events(name);
```

## 🔍 查询优化

### 索引策略

1. **主键索引**: 所有表都使用 UUID 作为主键
2. **外键索引**: 为所有外键字段创建索引
3. **查询索引**: 为常用查询字段创建复合索引
4. **时间索引**: 为时间字段创建索引支持范围查询

### 常用查询模式

```sql
-- 1. 按语言查询社团
SELECT
    id,
    CASE
        WHEN ? = 'ja' AND name_ja IS NOT NULL THEN name_ja
        WHEN ? = 'en' AND name_en IS NOT NULL THEN name_en
        ELSE name
    END as name
FROM circles
WHERE name LIKE ? OR name_ja LIKE ? OR name_en LIKE ?;

-- 2. 查询用户收藏
SELECT c.*, b.created_at as bookmarked_at
FROM circles c
JOIN bookmarks b ON c.id = b.target_id
WHERE b.user_id = ? AND b.target_type = 'circle'
ORDER BY b.created_at DESC;

-- 3. 查询展会参展信息
SELECT
    c.name as circle_name,
    a.name as artist_name,
    ap.booth_number,
    ap.works
FROM appearances ap
JOIN circles c ON ap.circle_id = c.id
JOIN artists a ON ap.artist_id = a.id
WHERE ap.event_id = ?
ORDER BY ap.booth_number;
```

## 🚀 性能优化

### 1. 查询优化

- 使用适当的索引
- 避免 N+1 查询问题
- 使用 LIMIT 限制结果集大小
- 合理使用 JOIN 和子查询

### 2. 数据分页

```sql
-- 基于偏移量的分页
SELECT * FROM circles
ORDER BY created_at DESC
LIMIT ? OFFSET ?;

-- 基于游标的分页（推荐）
SELECT * FROM circles
WHERE created_at < ?
ORDER BY created_at DESC
LIMIT ?;
```

### 3. 缓存策略

- 热点数据缓存到 Cloudflare KV
- 查询结果缓存 5-15 分钟
- 按语言分别缓存多语言内容

## 🔒 数据安全

### 1. 数据验证

- 使用 Zod 进行输入验证
- 参数化查询防止 SQL 注入
- 数据类型严格检查

### 2. 访问控制

- 基于角色的权限控制
- 行级安全策略
- 敏感数据加密存储

### 3. 审计日志

```sql
-- 记录所有数据变更
INSERT INTO logs (user_id, action, resource, resource_id, metadata)
VALUES (?, 'UPDATE', 'circles', ?, ?);
```

## 📈 监控和维护

### 1. 性能监控

- 查询执行时间监控
- 慢查询日志分析
- 数据库连接池监控

### 2. 数据备份

- 自动备份到 Cloudflare R2
- 定期备份验证
- 灾难恢复计划

### 3. 数据清理

```sql
-- 清理过期日志（保留 30 天）
DELETE FROM logs
WHERE created_at < datetime('now', '-30 days');

-- 清理无效收藏
DELETE FROM bookmarks
WHERE target_type = 'circle'
AND target_id NOT IN (SELECT id FROM circles);
```

## 🔄 迁移管理

### 迁移文件结构

```
db/
├── schema.sql          # 完整数据库架构
├── migrations/         # 增量迁移文件
│   ├── 001_initial.sql
│   ├── 002_add_i18n.sql
│   └── 003_add_indexes.sql
└── seeds/             # 种子数据
    ├── 000_base.sql
    └── 001_admin.sql
```

### 迁移最佳实践

1. **向后兼容**: 新迁移不应破坏现有功能
2. **原子操作**: 每个迁移应该是原子的
3. **可回滚**: 提供回滚脚本
4. **测试验证**: 在测试环境充分验证

---

**下一步**: [监控告警](./monitoring.md) | [故障排查](./troubleshooting.md)
