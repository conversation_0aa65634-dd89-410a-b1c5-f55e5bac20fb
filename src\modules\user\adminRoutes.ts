import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import * as userController from './controller';
import { userSchema, userCreateRequest, userUpdateRequest } from './schema';
import {
  successResponse,
  paginatedResult,
  errorResponse,
} from '@/utils/schemas';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const routes = new OpenAPIHono<HonoApp>();

const postUserRoute = createRoute({
  method: 'post',
  path: '/',
  summary: '创建用户',
  tags: ['Admin.Users'],
  request: {
    body: { content: { 'application/json': { schema: userCreateRequest } } },
  },
  responses: {
    201: {
      description: '用户创建成功',
      content: { 'application/json': { schema: successResponse } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
    422: { description: 'Validation Error' },
  },
});

const putUserRoute = createRoute({
  method: 'put',
  path: '/{id}',
  summary: '更新用户',
  tags: ['Admin.Users'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
    body: { content: { 'application/json': { schema: userUpdateRequest } } },
  },
  responses: {
    200: {
      description: '用户信息已更新',
      content: { 'application/json': { schema: successResponse } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
    422: { description: 'Validation Error' },
  },
});

const deleteUserRoute = createRoute({
  method: 'delete',
  path: '/{id}',
  summary: '删除用户',
  tags: ['Admin.Users'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '用户已删除',
      content: {
        'application/json': { schema: successResponse.pick({ message: true }) },
      },
    },
  },
});

const getUsersRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '用户列表',
  tags: ['Admin.Users'],
  responses: {
    200: {
      description: '用户列表',
      content: { 'application/json': { schema: paginatedResult(userSchema) } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

const getUserRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '用户详情',
  tags: ['Admin.Users'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '用户详情',
      content: { 'application/json': { schema: userSchema } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

registerOpenApiRoute(routes, getUsersRoute, userController.listUsers);
registerOpenApiRoute(routes, getUserRoute, userController.getUser);
registerOpenApiRoute(routes, postUserRoute, userController.createUser);
registerOpenApiRoute(routes, putUserRoute, userController.updateUser);
registerOpenApiRoute(routes, deleteUserRoute, userController.deleteUser);

export { routes };
