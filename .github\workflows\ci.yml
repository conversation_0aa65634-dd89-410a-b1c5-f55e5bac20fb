name: CI

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate OpenAPI & Types
        run: pnpm run gen:api

      - name: Check contract diff
        run: |
          git diff --exit-code docs-site/static/openapi.json src/api-types.d.ts

      - name: Lint & Type Check
        run: |
          pnpm lint
          pnpm type-check

      - name: Run Tests with coverage
        run: pnpm test --coverage

      - name: Build (Dry Run)
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        run: pnpm run build

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: 80a105dc-ce3a-4818-a20c-dc88d93ac59f
          flags: unit-tests
          fail_ci_if_error: true
