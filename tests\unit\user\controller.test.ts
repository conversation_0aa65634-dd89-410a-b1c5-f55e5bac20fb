import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  listUsers,
  createUser,
  getUser,
  updateUser,
  deleteUser,
} from '@/modules/user/controller';
import * as userService from '@/modules/user/service';
import { jsonError, jsonSuccess, validationError } from '@/utils/errorResponse';
import { getDB } from '@/infrastructure';
import { recordLog } from '@/utils/auditLog';

// Mock dependencies
vi.mock('@/modules/user/service');
vi.mock('@/utils/errorResponse');
vi.mock('@/infrastructure');
vi.mock('@/utils/auditLog');

// Mock context
const createMockContext = (overrides: any = {}) => ({
  req: {
    json: vi.fn(),
    param: vi.fn(),
  },
  get: vi.fn((key: string) => {
    if (key === 'cache') return null;
    if (key === 'logger') return null;
    return null;
  }),
  json: vi.fn(),
  ...overrides,
});

describe('user/controller', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getDB as any).mockReturnValue({});
    (jsonSuccess as any).mockReturnValue({ status: 200 });
    (jsonError as any).mockReturnValue({ status: 404 });
    (validationError as any).mockReturnValue({ status: 400 });
    (recordLog as any).mockResolvedValue(undefined);
  });

  describe('listUsers', () => {
    it('should list users successfully', async () => {
      const mockUsers = [
        { id: 'user-1', username: 'user1', role: 'viewer' },
        { id: 'user-2', username: 'user2', role: 'admin' },
      ];
      const mockContext = createMockContext();
      (userService.listUsers as any).mockResolvedValue(mockUsers);

      await listUsers(mockContext as any);

      expect(userService.listUsers).toHaveBeenCalledWith({}, null, null);
      expect(mockContext.json).toHaveBeenCalledWith(mockUsers);
    });

    it('should pass cache and logger when available', async () => {
      const mockCache = { get: vi.fn(), set: vi.fn() };
      const mockLogger = { debug: vi.fn(), info: vi.fn() };
      const mockUsers = [];
      const mockContext = createMockContext();
      mockContext.get.mockImplementation((key: string) => {
        if (key === 'cache') return mockCache;
        if (key === 'logger') return mockLogger;
        return null;
      });
      (userService.listUsers as any).mockResolvedValue(mockUsers);

      await listUsers(mockContext as any);

      expect(userService.listUsers).toHaveBeenCalledWith(
        {},
        mockCache,
        mockLogger
      );
    });
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      const mockUserData = {
        username: 'newuser',
        password: 'password123',
        role: 'viewer',
      };
      const mockCreatedUser = {
        id: 'user-1',
        username: 'newuser',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockUserData);
      (userService.createUser as any).mockResolvedValue(mockCreatedUser);

      await createUser(mockContext as any);

      expect(userService.createUser).toHaveBeenCalledWith({}, mockUserData);
      expect(recordLog).toHaveBeenCalledWith(mockContext, {
        action: 'CREATE_USER',
        targetType: 'user',
        targetId: 'user-1',
      });
      expect(jsonSuccess).toHaveBeenCalledWith(
        mockContext,
        '用户创建成功',
        mockCreatedUser,
        201
      );
    });

    it('should return validation error when username is missing', async () => {
      const mockUserData = {
        password: 'password123',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockUserData);

      await createUser(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        username: '必填字段',
      });
      expect(userService.createUser).not.toHaveBeenCalled();
    });

    it('should return validation error when password is missing', async () => {
      const mockUserData = {
        username: 'newuser',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockUserData);

      await createUser(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        password: '必填字段',
      });
      expect(userService.createUser).not.toHaveBeenCalled();
    });

    it('should return validation error when both username and password are missing', async () => {
      const mockUserData = {
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockUserData);

      await createUser(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        username: '必填字段',
        password: '必填字段',
      });
      expect(userService.createUser).not.toHaveBeenCalled();
    });

    it('should handle UNIQUE constraint error', async () => {
      const mockUserData = {
        username: 'existinguser',
        password: 'password123',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockUserData);

      const uniqueError = new Error('UNIQUE constraint failed: users.username');
      (userService.createUser as any).mockRejectedValue(uniqueError);

      await createUser(mockContext as any);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        10003,
        '唯一键冲突',
        409
      );
      expect(recordLog).not.toHaveBeenCalled();
    });

    it('should re-throw non-UNIQUE errors', async () => {
      const mockUserData = {
        username: 'newuser',
        password: 'password123',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockUserData);

      const otherError = new Error('Some other database error');
      (userService.createUser as any).mockRejectedValue(otherError);

      await expect(createUser(mockContext as any)).rejects.toThrow(
        'Some other database error'
      );
      expect(recordLog).not.toHaveBeenCalled();
    });

    it('should handle non-Error exceptions', async () => {
      const mockUserData = {
        username: 'newuser',
        password: 'password123',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockUserData);

      const nonErrorException = 'String error';
      (userService.createUser as any).mockRejectedValue(nonErrorException);

      await expect(createUser(mockContext as any)).rejects.toBe('String error');
    });
  });

  describe('getUser', () => {
    it('should return user when found', async () => {
      const mockUser = { id: 'user-1', username: 'testuser', role: 'viewer' };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      (userService.getUser as any).mockResolvedValue(mockUser);

      await getUser(mockContext as any);

      expect(userService.getUser).toHaveBeenCalledWith({}, 'user-1');
      expect(mockContext.json).toHaveBeenCalledWith(mockUser);
    });

    it('should return 404 when user not found', async () => {
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('nonexistent-id');
      (userService.getUser as any).mockResolvedValue(null);

      await getUser(mockContext as any);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        10002,
        '资源不存在',
        404
      );
    });
  });

  describe('updateUser', () => {
    it('should update user successfully', async () => {
      const mockUpdateData = { username: 'updateduser', role: 'admin' };
      const mockUpdatedUser = {
        id: 'user-1',
        username: 'updateduser',
        role: 'admin',
      };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);
      (userService.updateUser as any).mockResolvedValue(mockUpdatedUser);

      await updateUser(mockContext as any);

      expect(userService.updateUser).toHaveBeenCalledWith(
        {},
        'user-1',
        mockUpdateData
      );
      expect(recordLog).toHaveBeenCalledWith(mockContext, {
        action: 'UPDATE_USER',
        targetType: 'user',
        targetId: 'user-1',
      });
      expect(jsonSuccess).toHaveBeenCalledWith(
        mockContext,
        '用户信息已更新',
        mockUpdatedUser
      );
    });

    it('should update user with only username', async () => {
      const mockUpdateData = { username: 'updateduser' };
      const mockUpdatedUser = {
        id: 'user-1',
        username: 'updateduser',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);
      (userService.updateUser as any).mockResolvedValue(mockUpdatedUser);

      await updateUser(mockContext as any);

      expect(userService.updateUser).toHaveBeenCalledWith(
        {},
        'user-1',
        mockUpdateData
      );
    });

    it('should update user with only role', async () => {
      const mockUpdateData = { role: 'admin' };
      const mockUpdatedUser = {
        id: 'user-1',
        username: 'testuser',
        role: 'admin',
      };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);
      (userService.updateUser as any).mockResolvedValue(mockUpdatedUser);

      await updateUser(mockContext as any);

      expect(userService.updateUser).toHaveBeenCalledWith(
        {},
        'user-1',
        mockUpdateData
      );
    });

    it('should update user with only password', async () => {
      const mockUpdateData = { password: 'newpassword123' };
      const mockUpdatedUser = {
        id: 'user-1',
        username: 'testuser',
        role: 'viewer',
      };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);
      (userService.updateUser as any).mockResolvedValue(mockUpdatedUser);

      await updateUser(mockContext as any);

      expect(userService.updateUser).toHaveBeenCalledWith(
        {},
        'user-1',
        mockUpdateData
      );
    });

    it('should return validation error when no valid fields provided', async () => {
      const mockUpdateData = {}; // No valid fields
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);

      await updateUser(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        message: '未提供有效字段',
      });
      expect(userService.updateUser).not.toHaveBeenCalled();
    });

    it('should return validation error when all fields are undefined', async () => {
      const mockUpdateData = {
        username: undefined,
        role: undefined,
        password: undefined,
        otherField: 'value', // This field is ignored
      };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);

      await updateUser(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        message: '未提供有效字段',
      });
      expect(userService.updateUser).not.toHaveBeenCalled();
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('user-1');
      (userService.deleteUser as any).mockResolvedValue(undefined);

      await deleteUser(mockContext as any);

      expect(userService.deleteUser).toHaveBeenCalledWith({}, 'user-1');
      expect(recordLog).toHaveBeenCalledWith(mockContext, {
        action: 'DELETE_USER',
        targetType: 'user',
        targetId: 'user-1',
      });
      expect(jsonSuccess).toHaveBeenCalledWith(
        mockContext,
        '用户已删除',
        undefined,
        204
      );
    });
  });
});
