import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import {
  contentTypeConfigSchema,
  richTextContentSchema,
  entityTabsResponse,
  configListResponse,
  operationResultResponse,
  createConfigRequest,
  updateConfigRequest,
  createContentRequest,
  reorderConfigsRequest,
  batchUpdateStatusRequest,
  softDeleteRequest,
  EntityTypeEnum,
  EntityType,
  LanguageCodeEnum,
} from './schema';
import { RichTextTabsController } from './controller-new';
import { checkPresetConfigsIntegrity } from './startup-checker';
import {
  repairRichTextContents,
  checkRichTextContentIntegrity,
} from './content-initializer';
import { suggestKeys } from './key-generator';
import { registerOpenApiRoute } from '@/utils/openapiHelper';
import { jsonSuccess, jsonError } from '@/utils/errorResponse';
import type { HonoApp } from '@/types';

const richTextTabsRoutes = new OpenAPIHono<HonoApp>();

// ========================================
// Rich Text Tabs API v3.0.0
// 多语言富文本标签页管理系统
// 路由前缀: /rich-text-tabs
// ========================================

// ========================================
// 配置管理路由
// ========================================

// 获取活跃配置
const getActiveConfigsRoute = createRoute({
  method: 'get',
  path: '/configs/{entityType}/{languageCode}',
  summary: '获取实体的活跃标签页配置',
  description: '获取指定实体类型和语言的所有活跃标签页配置',
  tags: ['Rich Text Tabs - Config'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({ example: 'event' }),
      languageCode: LanguageCodeEnum.openapi({ example: 'en' }),
    }),
  },
  responses: {
    200: {
      description: '获取成功',
      content: {
        'application/json': {
          schema: configListResponse,
        },
      },
    },
    400: { description: '请求参数错误' },
    500: { description: '服务器错误' },
  },
});

// 获取所有配置（管理界面）
const getAllConfigsRoute = createRoute({
  method: 'get',
  path: '/configs/{entityType}/{languageCode}/all',
  summary: '获取实体的所有标签页配置',
  description:
    '获取指定实体类型和语言的所有标签页配置，包括已删除的（管理界面用）',
  tags: ['Rich Text Tabs - Config'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({ example: 'event' }),
      languageCode: LanguageCodeEnum.openapi({ example: 'en' }),
    }),
    query: z.object({
      includeDeleted: z.string().optional().openapi({ example: 'false' }),
    }),
  },
  responses: {
    200: {
      description: '获取成功',
      content: {
        'application/json': {
          schema: configListResponse,
        },
      },
    },
    400: { description: '请求参数错误' },
    500: { description: '服务器错误' },
  },
});

// 创建配置
const createConfigRoute = createRoute({
  method: 'post',
  path: '/configs',
  summary: '创建新的标签页配置',
  description: '为指定实体类型和语言创建新的标签页配置。Key 由后端自动生成',
  tags: ['Rich Text Tabs - Config'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: createConfigRequest,
        },
      },
    },
  },
  responses: {
    201: {
      description: '创建成功',
      content: {
        'application/json': {
          schema: contentTypeConfigSchema,
        },
      },
    },
    400: { description: '请求参数错误或验证失败' },
    500: { description: '服务器错误' },
  },
});

// 更新配置
const updateConfigRoute = createRoute({
  method: 'put',
  path: '/configs/{id}',
  summary: '更新标签页配置',
  description: '更新指定的标签页配置',
  tags: ['Rich Text Tabs - Config'],
  request: {
    params: z.object({
      id: z.string().openapi({ example: 'config-uuid-123' }),
    }),
    body: {
      content: {
        'application/json': {
          schema: updateConfigRequest,
        },
      },
    },
  },
  responses: {
    200: {
      description: '更新成功',
      content: {
        'application/json': {
          schema: contentTypeConfigSchema,
        },
      },
    },
    400: { description: '请求参数错误或验证失败' },
    404: { description: '配置不存在' },
    500: { description: '服务器错误' },
  },
});

// 软删除配置
const deleteConfigRoute = createRoute({
  method: 'delete',
  path: '/configs/{id}',
  summary: '删除标签页配置',
  description: '软删除指定的标签页配置（预设配置不能删除）',
  tags: ['Rich Text Tabs - Config'],
  request: {
    params: z.object({
      id: z.string().openapi({ example: 'config-uuid-123' }),
    }),
    body: {
      content: {
        'application/json': {
          schema: softDeleteRequest,
        },
      },
    },
  },
  responses: {
    200: {
      description: '删除成功',
      content: {
        'application/json': {
          schema: operationResultResponse,
        },
      },
    },
    400: { description: '请求参数错误或预设配置不能删除' },
    404: { description: '配置不存在' },
    500: { description: '服务器错误' },
  },
});

// 恢复配置
const restoreConfigRoute = createRoute({
  method: 'post',
  path: '/configs/{id}/restore',
  summary: '恢复已删除的配置',
  description: '恢复指定的已删除标签页配置',
  tags: ['Rich Text Tabs - Config'],
  request: {
    params: z.object({
      id: z.string().openapi({ example: 'config-uuid-123' }),
    }),
  },
  responses: {
    200: {
      description: '恢复成功',
      content: {
        'application/json': {
          schema: operationResultResponse,
        },
      },
    },
    404: { description: '配置不存在或未删除' },
    500: { description: '服务器错误' },
  },
});

// 重新排序配置
const reorderConfigsRoute = createRoute({
  method: 'post',
  path: '/configs/reorder',
  summary: '重新排序标签页配置',
  description: '批量更新标签页配置的排序',
  tags: ['Rich Text Tabs - Config'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: reorderConfigsRequest,
        },
      },
    },
  },
  responses: {
    200: {
      description: '排序更新成功',
      content: {
        'application/json': {
          schema: operationResultResponse,
        },
      },
    },
    400: { description: '请求参数错误' },
    500: { description: '服务器错误' },
  },
});

// 批量更新状态
const batchUpdateStatusRoute = createRoute({
  method: 'post',
  path: '/configs/batch-status',
  summary: '批量更新配置状态',
  description: '批量启用或禁用标签页配置',
  tags: ['Rich Text Tabs - Config'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: batchUpdateStatusRequest,
        },
      },
    },
  },
  responses: {
    200: {
      description: '状态更新成功',
      content: {
        'application/json': {
          schema: operationResultResponse,
        },
      },
    },
    400: { description: '请求参数错误或预设配置不能禁用' },
    500: { description: '服务器错误' },
  },
});

// ========================================
// 内容管理路由
// ========================================

// 获取实体标签页数据
const getEntityTabsRoute = createRoute({
  method: 'get',
  path: '/tabs/{entityType}/{entityId}/{languageCode}',
  summary: '获取实体的完整标签页数据',
  description: '获取指定实体的标签页配置和内容数据',
  tags: ['Rich Text Tabs - Content'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({ example: 'event' }),
      entityId: z.string().openapi({ example: 'reitaisai-22' }),
      languageCode: LanguageCodeEnum.openapi({ example: 'en' }),
    }),
    query: z.object({
      includeInactive: z.string().optional().openapi({ example: 'false' }),
    }),
  },
  responses: {
    200: {
      description: '获取成功',
      content: {
        'application/json': {
          schema: entityTabsResponse,
        },
      },
    },
    400: { description: '请求参数错误' },
    500: { description: '服务器错误' },
  },
});

// 创建或更新内容
const upsertContentRoute = createRoute({
  method: 'post',
  path: '/content',
  summary: '创建或更新富文本内容',
  description: '为指定实体、语言和内容类型创建或更新富文本内容',
  tags: ['Rich Text Tabs - Content'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: createContentRequest,
        },
      },
    },
  },
  responses: {
    201: {
      description: '保存成功',
      content: {
        'application/json': {
          schema: richTextContentSchema,
        },
      },
    },
    400: { description: '请求参数错误或验证失败' },
    500: { description: '服务器错误' },
  },
});

// 批量创建/更新内容
const batchUpsertContentsRoute = createRoute({
  method: 'post',
  path: '/content/batch',
  summary: '批量创建或更新内容',
  description: '批量为指定实体和语言创建或更新多个内容类型的富文本内容',
  tags: ['Rich Text Tabs - Content'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            entity_type: EntityTypeEnum,
            entity_id: z.string(),
            language_code: LanguageCodeEnum,
            contents: z.record(z.string()),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      description: '批量保存成功',
      content: {
        'application/json': {
          schema: z.array(richTextContentSchema),
        },
      },
    },
    400: { description: '请求参数错误' },
    500: { description: '服务器错误' },
  },
});

// ========================================
// 健康检查路由
// ========================================

// 健康检查：预设配置完整性
const healthCheckRoute = createRoute({
  method: 'get',
  path: '/health/preset-configs',
  summary: '检查预设配置完整性',
  description: '检查 Rich Text Tabs 预设配置是否完整',
  tags: ['Rich Text Tabs - Health'],
  responses: {
    200: {
      description: '检查完成',
      content: {
        'application/json': {
          schema: z.object({
            code: z.number(),
            message: z.string(),
            data: z.object({
              isComplete: z.boolean(),
              missing: z.array(z.string()),
              existing: z.number(),
              checked: z.number(),
            }),
          }),
        },
      },
    },
    500: { description: '服务器错误' },
  },
});

// 修复实体富文本内容
const repairEntityContentRoute = createRoute({
  method: 'post',
  path: '/repair/{entity_type}/{entity_id}',
  summary: '修复实体富文本内容',
  description: '检查并修复特定实体缺失的富文本内容记录',
  tags: ['Rich Text Tabs - Repair'],
  request: {
    params: z.object({
      entity_type: EntityTypeEnum,
      entity_id: z.string().min(1),
    }),
  },
  responses: {
    200: {
      description: '修复完成',
      content: {
        'application/json': {
          schema: z.object({
            code: z.number(),
            message: z.string(),
            data: z.object({
              entity_type: z.string(),
              entity_id: z.string(),
              before: z.object({
                isComplete: z.boolean(),
                expectedCount: z.number(),
                actualCount: z.number(),
                missingCount: z.number(),
              }),
              after: z.object({
                repaired: z.number(),
                errors: z.array(z.string()),
              }),
            }),
          }),
        },
      },
    },
    400: { description: '参数错误' },
    500: { description: '服务器错误' },
  },
});

// Key 建议接口
const suggestKeysRoute = createRoute({
  method: 'get',
  path: '/configs/suggest-keys',
  summary: '获取 Key 建议',
  description: '根据标签名称和实体类型获取推荐的 key 值',
  tags: ['Rich Text Tabs - Config'],
  request: {
    query: z.object({
      label: z.string().min(1).openapi({ example: '活动介绍' }),
      entity_type: EntityTypeEnum.openapi({ example: 'event' }),
    }),
  },
  responses: {
    200: {
      description: '获取成功',
      content: {
        'application/json': {
          schema: z.object({
            code: z.number(),
            message: z.string(),
            data: z.object({
              suggestions: z.array(z.string()),
              recommended: z.string(),
            }),
          }),
        },
      },
    },
    400: { description: '参数错误' },
  },
});

// ========================================
// 注册路由
// ========================================

// 配置管理路由
registerOpenApiRoute(richTextTabsRoutes, getActiveConfigsRoute, (c) =>
  RichTextTabsController.getActiveConfigs(c)
);
registerOpenApiRoute(richTextTabsRoutes, getAllConfigsRoute, (c) =>
  RichTextTabsController.getAllConfigs(c)
);
registerOpenApiRoute(richTextTabsRoutes, createConfigRoute, (c) =>
  RichTextTabsController.createConfig(c)
);
registerOpenApiRoute(richTextTabsRoutes, updateConfigRoute, (c) =>
  RichTextTabsController.updateConfig(c)
);
registerOpenApiRoute(richTextTabsRoutes, deleteConfigRoute, (c) =>
  RichTextTabsController.deleteConfig(c)
);
registerOpenApiRoute(richTextTabsRoutes, restoreConfigRoute, (c) =>
  RichTextTabsController.restoreConfig(c)
);
registerOpenApiRoute(richTextTabsRoutes, reorderConfigsRoute, (c) =>
  RichTextTabsController.reorderConfigs(c)
);
registerOpenApiRoute(richTextTabsRoutes, batchUpdateStatusRoute, (c) =>
  RichTextTabsController.batchUpdateStatus(c)
);

// Key 建议路由
registerOpenApiRoute(richTextTabsRoutes, suggestKeysRoute, async (c) => {
  try {
    const label = c.req.query('label');
    const entity_type = c.req.query('entity_type') as EntityType;

    if (!label || !entity_type) {
      return jsonError(c, 40001, '缺少必要参数 label 或 entity_type', 400);
    }

    const suggestions = suggestKeys(label, entity_type);
    const recommended = suggestions[0] || 'custom_tab';

    return jsonSuccess(c, 'Key 建议获取成功', {
      suggestions,
      recommended,
    });
  } catch (error) {
    console.error('Key 建议获取失败:', error);
    return jsonError(c, 50003, 'Key 建议获取失败', 500);
  }
});

// 内容管理路由
registerOpenApiRoute(richTextTabsRoutes, getEntityTabsRoute, (c) =>
  RichTextTabsController.getEntityTabs(c)
);
registerOpenApiRoute(richTextTabsRoutes, upsertContentRoute, (c) =>
  RichTextTabsController.upsertContent(c)
);
registerOpenApiRoute(richTextTabsRoutes, batchUpsertContentsRoute, (c) =>
  RichTextTabsController.batchUpsertContents(c)
);

// 健康检查路由
registerOpenApiRoute(richTextTabsRoutes, healthCheckRoute, async (c) => {
  try {
    const db = c.env.DB;
    const result = await checkPresetConfigsIntegrity(db);

    const missingDescriptions = result.missing.map(
      (config) => `${config.entity_type}/${config.language_code}/${config.key}`
    );

    return jsonSuccess(c, '预设配置检查完成', {
      isComplete: result.isComplete,
      missing: missingDescriptions,
      existing: result.existing,
      checked: result.missing.length + result.existing,
    });
  } catch (error) {
    console.error('预设配置检查失败:', error);
    return jsonError(c, 50001, '预设配置检查失败', 500);
  }
});

// 修复实体富文本内容路由
registerOpenApiRoute(
  richTextTabsRoutes,
  repairEntityContentRoute,
  async (c) => {
    try {
      const entity_type = c.req.param('entity_type') as EntityType;
      const entity_id = c.req.param('entity_id');

      if (!entity_id) {
        return jsonError(c, 40001, 'entity_id 参数缺失', 400);
      }
      const db = c.env.DB;

      // 检查修复前的状态
      const beforeIntegrity = await checkRichTextContentIntegrity(
        db,
        entity_type,
        entity_id
      );

      // 执行修复
      const repairResult = await repairRichTextContents(
        db,
        entity_type,
        entity_id
      );

      return jsonSuccess(c, '实体富文本内容修复完成', {
        entity_type,
        entity_id,
        before: {
          isComplete: beforeIntegrity.isComplete,
          expectedCount: beforeIntegrity.expectedCount,
          actualCount: beforeIntegrity.actualCount,
          missingCount: beforeIntegrity.missing.length,
        },
        after: {
          repaired: repairResult.repaired,
          errors: repairResult.errors,
        },
      });
    } catch (error) {
      console.error('实体富文本内容修复失败:', error);
      return jsonError(c, 50002, '实体富文本内容修复失败', 500);
    }
  }
);

export default richTextTabsRoutes;
