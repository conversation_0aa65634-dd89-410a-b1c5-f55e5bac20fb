// D1Database 类型现在在全局作用域中可用

import type {
  UserRepository,
  CreateUserData,
  UpdateUserData,
} from '../../modules/user/repository';
import { User } from '../../modules/user/schema';

export class D1UserRepository implements UserRepository {
  constructor(private readonly db: D1Database) {}

  async list(): Promise<User[]> {
    const { results } = await this.db
      .prepare('SELECT id, username, role FROM user ORDER BY username ASC')
      .all();
    return results as User[];
  }

  async findById(id: string): Promise<User | null> {
    const user = await this.db
      .prepare('SELECT id, username, role FROM user WHERE id = ?')
      .bind(id)
      .first();
    return (user as User) ?? null;
  }

  async create(data: CreateUserData): Promise<User> {
    const now = Date.now();
    await this.db.batch([
      this.db
        .prepare(
          'INSERT INTO user (id, name, email, username, role, email_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
        )
        .bind(
          data.id,
          data.username,
          `${data.username}@temp.local`,
          data.username,
          data.role,
          0,
          now,
          now
        ),
      this.db
        .prepare(
          'INSERT INTO account (id, account_id, provider_id, user_id, password, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)'
        )
        .bind(
          crypto.randomUUID(),
          data.username,
          'credential',
          data.id,
          data.hashedPassword,
          now,
          now
        ),
    ]);

    return (await this.findById(data.id)) as User;
  }

  async update(id: string, changes: UpdateUserData): Promise<User | null> {
    const sets: string[] = [];
    const params: (string | undefined)[] = [];

    if (changes.username !== undefined) {
      sets.push('username = ?');
      params.push(changes.username);
    }
    if (changes.role !== undefined) {
      sets.push('role = ?');
      params.push(changes.role);
    }

    if (sets.length) {
      sets.push('updated_at = ?');
      params.push(Date.now().toString());
      params.push(id);
      await this.db
        .prepare(`UPDATE user SET ${sets.join(', ')} WHERE id = ?`)
        .bind(...params)
        .run();
    }

    return this.findById(id);
  }

  async updatePassword(id: string, hashedPassword: string): Promise<void> {
    await this.db
      .prepare(
        'UPDATE account SET password = ?, updated_at = ? WHERE user_id = ? AND provider_id = ?'
      )
      .bind(hashedPassword, Date.now(), id, 'credential')
      .run();
  }

  async delete(id: string): Promise<void> {
    await this.db.batch([
      this.db.prepare('DELETE FROM session WHERE user_id = ?').bind(id),
      this.db.prepare('DELETE FROM account WHERE user_id = ?').bind(id),
      this.db.prepare('DELETE FROM user WHERE id = ?').bind(id),
    ]);
  }
}
