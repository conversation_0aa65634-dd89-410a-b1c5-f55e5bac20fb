import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import { feedResponseSchema } from './schema';
import * as feedService from './service';
import { getDB } from '@/infrastructure';
import type { <PERSON>ache, Logger } from '@/infrastructure';
import type { Locale } from '@/middlewares/locale';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const pubFeed = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI ----------
const getFeedRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '获取 Feed 流',
  tags: ['Feed'],
  request: {
    query: z.object({
      page: z.string().optional().openapi({
        description: '页码',
        example: '1',
        default: '1',
      }),
      limit: z.string().optional().openapi({
        description: '每页数量',
        example: '20',
        default: '20',
      }),
      type: z.enum(['all', 'events', 'circles']).optional().openapi({
        description: '内容类型',
        example: 'all',
        default: 'all',
      }),
    }),
  },
  responses: {
    200: {
      description: 'Feed 流数据',
      content: {
        'application/json': { schema: feedResponseSchema },
      },
    },
    400: {
      description: '请求参数错误',
    },
  },
});

// ---------- Handlers ----------
async function getFeedHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');
  const locale = (c.get('locale') as Locale) || 'en';

  const searchParams = new URL(c.req.url).searchParams;
  const page = Math.max(Number(searchParams.get('page') || '1'), 1);
  const limit = Math.min(
    Math.max(Number(searchParams.get('limit') || '20'), 1),
    100
  );
  const type =
    (searchParams.get('type') as 'all' | 'events' | 'circles') || 'all';

  try {
    const { items, total } = await feedService.getFeedData(
      db,
      page,
      limit,
      type,
      locale,
      cache,
      logger
    );

    return c.json({
      success: true,
      data: items,
      locale,
      timestamp: new Date().toISOString(),
      meta: {
        total,
        page,
        limit,
        hasMore: items.length === limit && page * limit < total,
      },
    });
  } catch (error) {
    logger?.error?.('getFeedHandler error', {
      error,
      page,
      limit,
      type,
      locale,
    });
    return jsonError(c, 50002, 'Feed fetch failed', 500);
  }
}

// ---------- Register ----------
registerOpenApiRoute(pubFeed, getFeedRoute, getFeedHandler);

export { pubFeed, pubFeed as routes };
