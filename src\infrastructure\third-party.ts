export interface RequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  url: string;
  headers?: Record<string, string>;
  body?: unknown;
  /** 请求超时时间（毫秒），0 = 不超时 */
  timeoutMs?: number;
}

export interface ThirdPartyClient {
  /**
   * 执行对第三方服务的请求，并返回解析后的数据。
   * @example const data = await client.request({ method: 'GET', url: 'https://api.example.com' })
   */
  request<T = unknown>(options: RequestOptions): Promise<T>;
}

/**
 * 基于全局 fetch 的默认实现。
 * 在 Cloudflare Workers 环境内可直接使用。
 */
export class FetchClient implements ThirdPartyClient {
  async request<T = unknown>(options: RequestOptions): Promise<T> {
    const { method, url, headers, body, timeoutMs = 0 } = options;

    const controller = timeoutMs > 0 ? new AbortController() : undefined;
    const id =
      timeoutMs > 0
        ? setTimeout(() => controller?.abort(), timeoutMs)
        : undefined;

    try {
      const resp = await fetch(url, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller?.signal,
      });

      if (!resp.ok) {
        throw new Error(`ThirdPartyClient: ${method} ${url} -> ${resp.status}`);
      }
      const text = await resp.text();
      try {
        return JSON.parse(text) as T;
      } catch {
        // 非 JSON 响应，则返回原始文本
        return text as unknown as T;
      }
    } finally {
      if (id) clearTimeout(id);
    }
  }
}
