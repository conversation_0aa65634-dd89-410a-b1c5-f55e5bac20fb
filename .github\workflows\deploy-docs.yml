name: Deploy API Docs to gh-pages

on:
  push:
    branches: [main]
    paths:
      - 'openapi.json'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10
          run_install: false

      - name: Build API docs (Redocly CLI)
        run: |
          mkdir -p site
          pnpm --package=@redocly/cli dlx redocly build-docs openapi.json --output=site/index.html

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_branch: gh-pages
          publish_dir: ./site
          keep_files: true
          commit_message: 'chore(docs): update API docs site'
