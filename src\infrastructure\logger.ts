export interface Logger {
  /** 常规信息 */
  info(message: string, meta?: Record<string, unknown>): Promise<void> | void;
  /** 警告信息：通常用于可恢复错误 */
  warn(message: string, meta?: Record<string, unknown>): Promise<void> | void;
  /** 错误信息：业务或系统异常 */
  error(message: string, meta?: Record<string, unknown>): Promise<void> | void;
  /** 调试信息：仅在开发环境输出 */
  debug?(message: string, meta?: Record<string, unknown>): Promise<void> | void;
}

/**
 * 默认基于 console 的 Logger，实现 Logger 接口。
 * 仅适用于开发 / 单元测试环境。
 */
export class ConsoleLogger implements Logger {
  private format(
    level: string,
    message: string,
    meta?: Record<string, unknown>
  ) {
    const ts = new Date().toISOString();
    return `[${ts}] [${level}] ${message}${meta ? ' ' + JSON.stringify(meta) : ''}`;
  }

  info(message: string, meta?: Record<string, unknown>) {
    console.info(this.format('INFO', message, meta));
  }

  warn(message: string, meta?: Record<string, unknown>) {
    console.warn(this.format('WARN', message, meta));
  }

  error(message: string, meta?: Record<string, unknown>) {
    console.error(this.format('ERROR', message, meta));
  }

  debug(message: string, meta?: Record<string, unknown>) {
    console.debug(this.format('DEBUG', message, meta));
  }
}
