import { z } from '@hono/zod-openapi';

// 基础 Circle 对象
export const circleSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  name: z.string().min(0).openapi({ example: '東方愛好会' }),
  urls: z.string().nullable().optional().openapi({
    example: '{"author":"<PERSON>","twitter_url":"https://twitter.com/example"}',
  }),
  created_at: z
    .string()
    .optional()
    .openapi({ example: '2024-01-01T00:00:00Z' }),
  updated_at: z
    .string()
    .optional()
    .openapi({ example: '2024-01-01T00:00:00Z' }),
});

export type Circle = z.infer<typeof circleSchema>;

// 创建请求体
export const circleCreateRequest = z.object({
  name: z.string().openapi({ example: '東方愛好会' }),
  author: z.string().optional().openapi({ example: 'Alice' }),
  twitter_url: z
    .string()
    .optional()
    .openapi({ example: 'https://twitter.com/example' }),
  pixiv_url: z
    .string()
    .optional()
    .openapi({ example: 'https://pixiv.net/users/123' }),
  web_url: z.string().optional().openapi({ example: 'https://example.com' }),
});

export type CircleCreateInput = z.infer<typeof circleCreateRequest>;

// 更新请求体
export const circleUpdateRequest = z.object({
  name: z.string().optional(),
  author: z.string().optional(),
  twitter_url: z.string().optional(),
  pixiv_url: z.string().optional(),
  web_url: z.string().optional(),
});

export type CircleUpdateInput = z.infer<typeof circleUpdateRequest>;
