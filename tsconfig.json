{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "esModuleInterop": true, "skipLibCheck": true, "noEmit": true, "lib": ["esnext"], "types": ["./worker-configuration.d.ts", "@types/node"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/modules/*": ["src/modules/*"]}, "allowJs": true, "resolveJsonModule": true, "isolatedModules": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.src.json"}, {"path": "./scripts/tsconfig.scripts.json"}, {"path": "./tsconfig.tests.json"}]}