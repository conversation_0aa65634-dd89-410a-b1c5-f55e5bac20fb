import { execSync } from 'child_process';

/**
 * 想要批量执行的 pnpm 命令列表
 * 在这里添加您需要执行的命令
 */
const commands: string[] = [
  'pnpm wrangler d1 execute ayafeed-dev --file=db/schema.sql', // 执行 schema.sql
  'pnpm wrangler d1 execute ayafeed-dev --file=db/seeds/000_base.sql', // 执行 base.sql
  'pnpm wrangler d1 execute ayafeed-dev --file=db/seeds/001_admin.sql', // 执行 admin.sql
  'pnpm wrangler d1 execute ayafeed-dev --file=data/import_reitaisai-22.sql', // 执行 reitaisai-22.sql
  // 'pnpm run build',
  // 'pnpm run test',
];

/**
 * 执行单个命令的函数
 * @param command 要执行的命令字符串
 */
function runCommand(command: string) {
  console.log(`\n🚀 Executing: ${command}`);
  try {
    // 使用 execSync 同步执行命令
    // stdio: 'inherit' 会将子进程的输出直接打印到当前控制台
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ Success: ${command}`);
  } catch (error) {
    console.error(`❌ Failed to execute command: ${command}`);
    // 如果一个命令失败，则抛出错误，终止整个脚本
    throw error;
  }
}

/**
 * 主函数，用于按顺序执行所有命令
 */
function main() {
  console.log('🔥 Starting batch pnpm script...');
  for (const command of commands) {
    runCommand(command);
  }
  console.log('\n🎉 All commands executed successfully!');
}

// 执行主函数
main();
