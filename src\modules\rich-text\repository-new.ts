import { v4 as uuidv4 } from 'uuid';
import type {
  ContentTypeConfig,
  RichTextContent,
  EntityType,
  LanguageCode,
  CreateConfigRequest,
  UpdateConfigRequest,
  CreateContentRequest,
  UpdateContentRequest,
} from './schema';

/**
 * 内容类型配置 Repository
 * 处理标签页配置的数据访问逻辑
 */
export interface ContentTypeConfigRepository {
  getActiveConfigs(
    entityType: EntityType,
    languageCode: LanguageCode
  ): Promise<ContentTypeConfig[]>;
  getAllConfigs(
    entityType: EntityType,
    languageCode: LanguageCode,
    includeDeleted?: boolean
  ): Promise<ContentTypeConfig[]>;
  getConfigById(id: string): Promise<ContentTypeConfig | null>;
  getConfigByKey(
    entityType: EntityType,
    languageCode: LanguageCode,
    key: string
  ): Promise<ContentTypeConfig | null>;
  createConfig(
    data: CreateConfigRequest & { key: string }
  ): Promise<ContentTypeConfig>;
  updateConfig(
    id: string,
    data: UpdateConfigRequest
  ): Promise<ContentTypeConfig>;
  softDeleteConfig(id: string, deletedBy: string): Promise<boolean>;
  restoreConfig(id: string): Promise<boolean>;
  reorderConfigs(
    entityType: EntityType,
    languageCode: LanguageCode,
    orders: Array<{ id: string; sort_order: number }>
  ): Promise<void>;
  batchUpdateStatus(ids: string[], isActive: boolean): Promise<number>;
}

export function createContentTypeConfigRepository(
  db: D1Database
): ContentTypeConfigRepository {
  return {
    async getActiveConfigs(
      entityType: EntityType,
      languageCode: LanguageCode
    ): Promise<ContentTypeConfig[]> {
      const stmt = db.prepare(`
        SELECT * FROM content_type_configs
        WHERE entity_type = ?
          AND language_code = ?
          AND is_active = 1
          AND deleted_at IS NULL
        ORDER BY sort_order ASC, created_at ASC
      `);

      const result = await stmt.bind(entityType, languageCode).all();
      return result.results as ContentTypeConfig[];
    },

    async getAllConfigs(
      entityType: EntityType,
      languageCode: LanguageCode,
      includeDeleted: boolean = false
    ): Promise<ContentTypeConfig[]> {
      let whereClause = 'WHERE entity_type = ? AND language_code = ?';
      if (!includeDeleted) {
        whereClause += ' AND deleted_at IS NULL';
      }

      const stmt = db.prepare(`
        SELECT * FROM content_type_configs 
        ${whereClause}
        ORDER BY sort_order ASC, created_at ASC
      `);

      const result = await stmt.bind(entityType, languageCode).all();
      return result.results as ContentTypeConfig[];
    },

    async getConfigById(id: string): Promise<ContentTypeConfig | null> {
      const stmt = db.prepare(`
        SELECT * FROM content_type_configs 
        WHERE id = ? AND deleted_at IS NULL
      `);

      const result = await stmt.bind(id).first();
      return result as ContentTypeConfig | null;
    },

    async getConfigByKey(
      entityType: EntityType,
      languageCode: LanguageCode,
      key: string
    ): Promise<ContentTypeConfig | null> {
      const stmt = db.prepare(`
        SELECT * FROM content_type_configs
        WHERE entity_type = ?
          AND language_code = ?
          AND key = ?
          AND deleted_at IS NULL
      `);

      const result = await stmt.bind(entityType, languageCode, key).first();
      return result as ContentTypeConfig | null;
    },

    async createConfig(
      data: CreateConfigRequest & { key: string }
    ): Promise<ContentTypeConfig> {
      const id = uuidv4();
      const now = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO content_type_configs (
          id, entity_type, language_code, key, label, placeholder, icon,
          sort_order, is_active, is_preset, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      await stmt
        .bind(
          id,
          data.entity_type,
          data.language_code,
          data.key,
          data.label,
          data.placeholder || null,
          data.icon || null,
          data.sort_order || 0,
          data.is_active !== false, // 默认为 true
          data.is_preset || false,
          now,
          now
        )
        .run();

      const result = await this.getConfigById(id);
      if (!result) {
        throw new Error('Failed to create config');
      }

      return result;
    },

    async updateConfig(
      id: string,
      data: UpdateConfigRequest
    ): Promise<ContentTypeConfig> {
      const now = new Date().toISOString();

      // 构建动态更新语句
      const updateFields: string[] = [];
      const values: any[] = [];

      if (data.label !== undefined) {
        updateFields.push('label = ?');
        values.push(data.label);
      }
      if (data.placeholder !== undefined) {
        updateFields.push('placeholder = ?');
        values.push(data.placeholder);
      }
      if (data.icon !== undefined) {
        updateFields.push('icon = ?');
        values.push(data.icon);
      }
      if (data.sort_order !== undefined) {
        updateFields.push('sort_order = ?');
        values.push(data.sort_order);
      }
      if (data.is_active !== undefined) {
        updateFields.push('is_active = ?');
        values.push(data.is_active);
      }

      updateFields.push('updated_at = ?');
      values.push(now);
      values.push(id);

      const stmt = db.prepare(`
        UPDATE content_type_configs 
        SET ${updateFields.join(', ')}
        WHERE id = ? AND deleted_at IS NULL
      `);

      const result = await stmt.bind(...values).run();
      if (result.meta.changes === 0) {
        throw new Error('Config not found or already deleted');
      }

      const updated = await this.getConfigById(id);
      if (!updated) {
        throw new Error('Failed to update config');
      }

      return updated;
    },

    async softDeleteConfig(id: string, deletedBy: string): Promise<boolean> {
      const now = new Date().toISOString();

      // 检查是否为预设配置
      const config = await this.getConfigById(id);
      if (!config) {
        return false;
      }

      if (config.is_preset) {
        throw new Error('Cannot delete preset configuration');
      }

      const stmt = db.prepare(`
        UPDATE content_type_configs 
        SET deleted_at = ?, deleted_by = ?, updated_at = ?
        WHERE id = ? AND deleted_at IS NULL
      `);

      const result = await stmt.bind(now, deletedBy, now, id).run();
      return result.meta.changes > 0;
    },

    async restoreConfig(id: string): Promise<boolean> {
      const now = new Date().toISOString();

      const stmt = db.prepare(`
        UPDATE content_type_configs 
        SET deleted_at = NULL, deleted_by = NULL, updated_at = ?
        WHERE id = ? AND deleted_at IS NOT NULL
      `);

      const result = await stmt.bind(now, id).run();
      return result.meta.changes > 0;
    },

    async reorderConfigs(
      entityType: EntityType,
      languageCode: LanguageCode,
      orders: Array<{ id: string; sort_order: number }>
    ): Promise<void> {
      const now = new Date().toISOString();

      // 使用事务批量更新排序
      for (const order of orders) {
        const stmt = db.prepare(`
          UPDATE content_type_configs 
          SET sort_order = ?, updated_at = ?
          WHERE id = ? 
            AND entity_type = ? 
            AND language_code = ? 
            AND deleted_at IS NULL
        `);

        await stmt
          .bind(order.sort_order, now, order.id, entityType, languageCode)
          .run();
      }
    },

    async batchUpdateStatus(ids: string[], isActive: boolean): Promise<number> {
      const now = new Date().toISOString();
      let totalChanges = 0;

      for (const id of ids) {
        const stmt = db.prepare(`
          UPDATE content_type_configs 
          SET is_active = ?, updated_at = ?
          WHERE id = ? AND deleted_at IS NULL
        `);

        const result = await stmt.bind(isActive, now, id).run();
        totalChanges += result.meta.changes;
      }

      return totalChanges;
    },
  };
}

/**
 * 富文本内容 Repository
 * 处理实际内容的数据访问逻辑
 */
export interface RichTextContentRepository {
  getContentsByEntity(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode
  ): Promise<RichTextContent[]>;
  getContent(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType: string
  ): Promise<RichTextContent | null>;
  createContent(data: CreateContentRequest): Promise<RichTextContent>;
  updateContent(
    id: string,
    data: UpdateContentRequest
  ): Promise<RichTextContent>;
  upsertContent(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType: string,
    content: string
  ): Promise<RichTextContent>;
  deleteContent(id: string): Promise<boolean>;
  deleteContentsByEntity(
    entityType: EntityType,
    entityId: string
  ): Promise<number>;
  getRecentlyUpdatedContents(limit?: number): Promise<RichTextContent[]>;
}

export function createRichTextContentRepository(
  db: D1Database
): RichTextContentRepository {
  return {
    async getContentsByEntity(
      entityType: EntityType,
      entityId: string,
      languageCode: LanguageCode
    ): Promise<RichTextContent[]> {
      const stmt = db.prepare(`
        SELECT * FROM rich_text_contents 
        WHERE entity_type = ? AND entity_id = ? AND language_code = ?
        ORDER BY content_type
      `);

      const result = await stmt.bind(entityType, entityId, languageCode).all();
      return result.results as RichTextContent[];
    },

    async getContent(
      entityType: EntityType,
      entityId: string,
      languageCode: LanguageCode,
      contentType: string
    ): Promise<RichTextContent | null> {
      const stmt = db.prepare(`
        SELECT * FROM rich_text_contents 
        WHERE entity_type = ? 
          AND entity_id = ? 
          AND language_code = ? 
          AND content_type = ?
      `);

      const result = await stmt
        .bind(entityType, entityId, languageCode, contentType)
        .first();
      return result as RichTextContent | null;
    },

    async createContent(data: CreateContentRequest): Promise<RichTextContent> {
      const id = uuidv4();
      const now = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO rich_text_contents (
          id, entity_type, entity_id, language_code, content_type, content, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      await stmt
        .bind(
          id,
          data.entity_type,
          data.entity_id,
          data.language_code,
          data.content_type,
          data.content,
          now,
          now
        )
        .run();

      return {
        id,
        ...data,
        created_at: now,
        updated_at: now,
      };
    },

    async updateContent(
      id: string,
      data: UpdateContentRequest
    ): Promise<RichTextContent> {
      const now = new Date().toISOString();

      const stmt = db.prepare(`
        UPDATE rich_text_contents 
        SET content = ?, updated_at = ?
        WHERE id = ?
      `);

      const result = await stmt.bind(data.content, now, id).run();
      if (result.meta.changes === 0) {
        throw new Error('Content not found');
      }

      // 返回更新后的内容
      const updated = await db
        .prepare('SELECT * FROM rich_text_contents WHERE id = ?')
        .bind(id)
        .first();
      if (!updated) {
        throw new Error('Failed to retrieve updated content');
      }

      return updated as RichTextContent;
    },

    async upsertContent(
      entityType: EntityType,
      entityId: string,
      languageCode: LanguageCode,
      contentType: string,
      content: string
    ): Promise<RichTextContent> {
      const existing = await this.getContent(
        entityType,
        entityId,
        languageCode,
        contentType
      );

      if (existing) {
        return await this.updateContent(existing.id, { content });
      } else {
        return await this.createContent({
          entity_type: entityType,
          entity_id: entityId,
          language_code: languageCode,
          content_type: contentType,
          content,
        });
      }
    },

    async deleteContent(id: string): Promise<boolean> {
      const stmt = db.prepare('DELETE FROM rich_text_contents WHERE id = ?');
      const result = await stmt.bind(id).run();
      return result.meta.changes > 0;
    },

    async deleteContentsByEntity(
      entityType: EntityType,
      entityId: string
    ): Promise<number> {
      const stmt = db.prepare(`
        DELETE FROM rich_text_contents 
        WHERE entity_type = ? AND entity_id = ?
      `);
      const result = await stmt.bind(entityType, entityId).run();
      return result.meta.changes;
    },

    async getRecentlyUpdatedContents(
      limit: number = 10
    ): Promise<RichTextContent[]> {
      const stmt = db.prepare(`
        SELECT * FROM rich_text_contents 
        ORDER BY updated_at DESC 
        LIMIT ?
      `);

      const result = await stmt.bind(limit).all();
      return result.results as RichTextContent[];
    },
  };
}
