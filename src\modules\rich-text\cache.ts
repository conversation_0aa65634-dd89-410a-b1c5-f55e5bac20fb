// KVNamespace 类型现在在全局作用域中可用
import type {
  EntityType,
  LanguageCode,
  ContentTypeConfig,
  RichTextContent,
  EntityTabsResponse,
} from './schema';

/**
 * 富文本标签页缓存管理器
 * 提供多层缓存策略和智能失效机制
 */
export class RichTextTabsCache {
  private readonly kv: KVNamespace;
  private readonly memoryCache = new Map<
    string,
    { data: any; expires: number }
  >();
  private readonly MEMORY_TTL = 5 * 60 * 1000; // 5分钟内存缓存
  private readonly KV_TTL = 30 * 60; // 30分钟 KV 缓存

  constructor(kv: KVNamespace) {
    this.kv = kv;
  }

  // ========================================
  // 配置缓存
  // ========================================

  /**
   * 获取活跃配置缓存
   */
  async getActiveConfigs(
    entityType: EntityType,
    languageCode: LanguageCode
  ): Promise<ContentTypeConfig[] | null> {
    const key = this.getActiveConfigsKey(entityType, languageCode);
    return this.get<ContentTypeConfig[]>(key);
  }

  /**
   * 设置活跃配置缓存
   */
  async setActiveConfigs(
    entityType: EntityType,
    languageCode: LanguageCode,
    configs: ContentTypeConfig[]
  ): Promise<void> {
    const key = this.getActiveConfigsKey(entityType, languageCode);
    await this.set(key, configs);
  }

  /**
   * 获取所有配置缓存
   */
  async getAllConfigs(
    entityType: EntityType,
    languageCode: LanguageCode,
    includeDeleted: boolean = false
  ): Promise<ContentTypeConfig[] | null> {
    const key = this.getAllConfigsKey(entityType, languageCode, includeDeleted);
    return this.get<ContentTypeConfig[]>(key);
  }

  /**
   * 设置所有配置缓存
   */
  async setAllConfigs(
    entityType: EntityType,
    languageCode: LanguageCode,
    configs: ContentTypeConfig[],
    includeDeleted: boolean = false
  ): Promise<void> {
    const key = this.getAllConfigsKey(entityType, languageCode, includeDeleted);
    await this.set(key, configs);
  }

  // ========================================
  // 内容缓存
  // ========================================

  /**
   * 获取实体标签页数据缓存
   */
  async getEntityTabs(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    includeInactive: boolean = false
  ): Promise<EntityTabsResponse | null> {
    const key = this.getEntityTabsKey(
      entityType,
      entityId,
      languageCode,
      includeInactive
    );
    return this.get<EntityTabsResponse>(key);
  }

  /**
   * 设置实体标签页数据缓存
   */
  async setEntityTabs(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    data: EntityTabsResponse,
    includeInactive: boolean = false
  ): Promise<void> {
    const key = this.getEntityTabsKey(
      entityType,
      entityId,
      languageCode,
      includeInactive
    );
    await this.set(key, data);
  }

  /**
   * 获取单个内容缓存
   */
  async getContent(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType: string
  ): Promise<RichTextContent | null> {
    const key = this.getContentKey(
      entityType,
      entityId,
      languageCode,
      contentType
    );
    return this.get<RichTextContent>(key);
  }

  /**
   * 设置单个内容缓存
   */
  async setContent(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType: string,
    content: RichTextContent
  ): Promise<void> {
    const key = this.getContentKey(
      entityType,
      entityId,
      languageCode,
      contentType
    );
    await this.set(key, content);
  }

  // ========================================
  // 缓存失效
  // ========================================

  /**
   * 配置变更时的缓存失效
   */
  async invalidateConfigCache(
    entityType: EntityType,
    languageCode: LanguageCode
  ): Promise<void> {
    const patterns = [
      this.getActiveConfigsKey(entityType, languageCode),
      this.getAllConfigsKey(entityType, languageCode, false),
      this.getAllConfigsKey(entityType, languageCode, true),
    ];

    await Promise.all([
      ...patterns.map((key) => this.delete(key)),
      this.invalidateEntityTabsForEntityType(entityType, languageCode),
    ]);
  }

  /**
   * 内容变更时的缓存失效
   */
  async invalidateContentCache(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType?: string
  ): Promise<void> {
    const promises: Promise<void>[] = [];

    if (contentType) {
      // 失效特定内容缓存
      const contentKey = this.getContentKey(
        entityType,
        entityId,
        languageCode,
        contentType
      );
      promises.push(this.delete(contentKey));
    }

    // 失效实体标签页缓存
    promises.push(
      this.delete(
        this.getEntityTabsKey(entityType, entityId, languageCode, false)
      ),
      this.delete(
        this.getEntityTabsKey(entityType, entityId, languageCode, true)
      )
    );

    await Promise.all(promises);
  }

  /**
   * 实体删除时的缓存失效
   */
  async invalidateEntityCache(
    entityType: EntityType,
    entityId: string
  ): Promise<void> {
    const languages: LanguageCode[] = ['en', 'zh', 'ja'];
    const promises: Promise<void>[] = [];

    for (const lang of languages) {
      promises.push(
        this.delete(this.getEntityTabsKey(entityType, entityId, lang, false)),
        this.delete(this.getEntityTabsKey(entityType, entityId, lang, true))
      );
    }

    await Promise.all(promises);
  }

  /**
   * 批量失效实体类型相关缓存
   */
  private async invalidateEntityTabsForEntityType(
    entityType: EntityType,
    languageCode: LanguageCode
  ): Promise<void> {
    // 注意：这里只能失效内存缓存中的相关数据
    // KV 缓存需要通过具体的 key 来失效
    const prefix = `tabs:${entityType}:`;

    for (const [key] of this.memoryCache) {
      if (key.startsWith(prefix) && key.includes(`:${languageCode}:`)) {
        this.memoryCache.delete(key);
      }
    }
  }

  // ========================================
  // 缓存键值生成
  // ========================================

  private getActiveConfigsKey(
    entityType: EntityType,
    languageCode: LanguageCode
  ): string {
    return `configs:active:${entityType}:${languageCode}`;
  }

  private getAllConfigsKey(
    entityType: EntityType,
    languageCode: LanguageCode,
    includeDeleted: boolean
  ): string {
    return `configs:all:${entityType}:${languageCode}:${includeDeleted}`;
  }

  private getEntityTabsKey(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    includeInactive: boolean
  ): string {
    return `tabs:${entityType}:${entityId}:${languageCode}:${includeInactive}`;
  }

  private getContentKey(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType: string
  ): string {
    return `content:${entityType}:${entityId}:${languageCode}:${contentType}`;
  }

  // ========================================
  // 底层缓存操作
  // ========================================

  /**
   * 获取缓存数据（内存 -> KV）
   */
  private async get<T>(key: string): Promise<T | null> {
    // 1. 尝试内存缓存
    const memoryItem = this.memoryCache.get(key);
    if (memoryItem && memoryItem.expires > Date.now()) {
      return memoryItem.data as T;
    }

    // 2. 尝试 KV 缓存
    try {
      const kvValue = await this.kv.get(key, 'json');
      if (kvValue) {
        // 更新内存缓存
        this.memoryCache.set(key, {
          data: kvValue,
          expires: Date.now() + this.MEMORY_TTL,
        });
        return kvValue as T;
      }
    } catch (error) {
      console.warn(`KV 缓存读取失败 [${key}]:`, error);
    }

    return null;
  }

  /**
   * 设置缓存数据（内存 + KV）
   */
  private async set<T>(key: string, value: T): Promise<void> {
    // 1. 设置内存缓存
    this.memoryCache.set(key, {
      data: value,
      expires: Date.now() + this.MEMORY_TTL,
    });

    // 2. 设置 KV 缓存
    try {
      await this.kv.put(key, JSON.stringify(value), {
        expirationTtl: this.KV_TTL,
      });
    } catch (error) {
      console.warn(`KV 缓存写入失败 [${key}]:`, error);
    }
  }

  /**
   * 删除缓存数据（内存 + KV）
   */
  private async delete(key: string): Promise<void> {
    // 1. 删除内存缓存
    this.memoryCache.delete(key);

    // 2. 删除 KV 缓存
    try {
      await this.kv.delete(key);
    } catch (error) {
      console.warn(`KV 缓存删除失败 [${key}]:`, error);
    }
  }

  /**
   * 清理过期的内存缓存
   */
  cleanupMemoryCache(): void {
    const now = Date.now();
    for (const [key, item] of this.memoryCache) {
      if (item.expires <= now) {
        this.memoryCache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    memorySize: number;
    memoryKeys: string[];
  } {
    return {
      memorySize: this.memoryCache.size,
      memoryKeys: Array.from(this.memoryCache.keys()),
    };
  }
}
