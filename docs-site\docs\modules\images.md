# 图片模块 (Images Module)

## 概述

图片模块提供图片上传、存储、查询和管理功能，基于 Cloudflare R2 存储服务，支持多种图片变体和批量操作。

## 功能特性

- ✅ 图片上传到 Cloudflare R2 存储
- ✅ 多种图片变体 (original, large, medium, small, thumbnail)
- ✅ 图片分类管理 (event, circle, user 等)
- ✅ 批量图片查询
- ✅ 图片元数据管理
- ✅ 权限控制 (管理员上传，公开查询)

## API 端点

### 公开接口

#### GET /images/{category}/{resourceId}

**功能**: 获取指定资源的图片列表

**路径参数**:

- `category` - 图片分类 (event, circle, user 等)
- `resourceId` - 资源ID

**查询参数**:

- `variant` (可选) - 图片变体 (original, large, medium, small, thumbnail)
- `imageType` (可选) - 图片类型 (poster, avatar, banner 等)

**响应示例**:

```json
{
  "success": true,
  "data": [
    {
      "id": "img_123",
      "url": "https://r2.example.com/images/event/123/poster_large.jpg",
      "variant": "large",
      "imageType": "poster",
      "resourceType": "event",
      "resourceId": "123",
      "metadata": {
        "width": 1200,
        "height": 800,
        "size": 245760,
        "format": "jpeg"
      },
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### GET /images/{id}

**功能**: 获取单个图片的详细信息

**路径参数**:

- `id` - 图片ID

**响应示例**:

```json
{
  "success": true,
  "data": {
    "id": "img_123",
    "url": "https://r2.example.com/images/event/123/poster_large.jpg",
    "variant": "large",
    "imageType": "poster",
    "resourceType": "event",
    "resourceId": "123",
    "metadata": {
      "width": 1200,
      "height": 800,
      "size": 245760,
      "format": "jpeg",
      "originalName": "event_poster.jpg"
    },
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### GET /images/batch

**功能**: 批量查询多个资源的图片

**查询参数**:

- `events` - 事件ID列表，逗号分隔 (如: event1,event2,event3)
- `circles` - 社团ID列表，逗号分隔
- `variant` (可选) - 图片变体过滤
- `imageType` (可选) - 图片类型过滤

**响应示例**:

```json
{
  "success": true,
  "data": {
    "event1": {
      "id": "img_123",
      "url": "https://r2.example.com/images/event/event1/poster_medium.jpg",
      "variant": "medium",
      "imageType": "poster"
    },
    "event2": {
      "id": "img_124",
      "url": "https://r2.example.com/images/event/event2/poster_medium.jpg",
      "variant": "medium",
      "imageType": "poster"
    },
    "event3": null
  }
}
```

### 管理接口

#### POST /admin/images/upload

**功能**: 上传图片文件

**权限**: admin 或 editor 角色

**请求体**: multipart/form-data

- `file` - 图片文件 (必需)
- `resourceType` - 资源类型 (必需，如: event, circle, user)
- `resourceId` - 资源ID (必需)
- `imageType` - 图片类型 (必需，如: poster, avatar, banner)
- `variant` - 图片变体 (可选，默认: original)

**响应示例**:

```json
{
  "success": true,
  "data": {
    "id": "img_125",
    "url": "https://r2.example.com/images/event/123/poster_original.jpg",
    "variant": "original",
    "imageType": "poster",
    "resourceType": "event",
    "resourceId": "123",
    "metadata": {
      "width": 1920,
      "height": 1080,
      "size": 512000,
      "format": "jpeg",
      "originalName": "poster.jpg"
    }
  }
}
```

#### DELETE /admin/images

**功能**: 批量删除图片

**权限**: admin 或 editor 角色

**请求体**:

```json
{
  "imageIds": ["img_123", "img_124", "img_125"]
}
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "deleted": ["img_123", "img_124"],
    "failed": ["img_125"],
    "errors": {
      "img_125": "Image not found"
    }
  }
}
```

## 数据模型

### Image Schema

```typescript
interface Image {
  id: string; // 图片唯一标识
  url: string; // 图片访问URL
  variant: ImageVariant; // 图片变体
  imageType: string; // 图片类型
  resourceType: string; // 关联资源类型
  resourceId: string; // 关联资源ID
  metadata: ImageMetadata; // 图片元数据
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
}

interface ImageMetadata {
  width: number; // 图片宽度
  height: number; // 图片高度
  size: number; // 文件大小(字节)
  format: string; // 图片格式
  originalName?: string; // 原始文件名
}

type ImageVariant = 'original' | 'large' | 'medium' | 'small' | 'thumbnail';
```

## 使用示例

### 前端集成示例

```typescript
// 获取事件海报图片
const eventImages = await fetch(
  '/images/event/123?imageType=poster&variant=medium'
);

// 批量获取多个事件的图片
const batchImages = await fetch(
  '/images/batch?events=event1,event2,event3&variant=medium'
);

// 上传图片
const formData = new FormData();
formData.append('file', file);
formData.append('resourceType', 'event');
formData.append('resourceId', '123');
formData.append('imageType', 'poster');

const uploadResult = await fetch('/admin/images/upload', {
  method: 'POST',
  headers: {
    Authorization: `Bearer ${token}`,
  },
  body: formData,
});
```

## 性能优化

### 数据库索引

- `idx_images_resource_lookup`: 优化按资源类型、资源ID、图片类型、变体查询
- `idx_images_resource_variant`: 优化批量查询性能
- `idx_images_group_variant`: 优化按组查询

### 缓存策略

- 图片URL使用CDN缓存
- 图片元数据使用应用层缓存
- 批量查询结果缓存

## 错误处理

### 常见错误码

- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 图片不存在
- `413` - 文件过大
- `415` - 不支持的文件类型
- `500` - 服务器内部错误

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "FILE_TOO_LARGE",
    "message": "File size exceeds 10MB limit",
    "details": {
      "maxSize": 10485760,
      "actualSize": 15728640
    }
  }
}
```

## 测试用例

### 批量查询测试

```bash
# 基础批量查询
GET /images/batch?events=event1,event2,event3

# 带变体过滤的批量查询
GET /images/batch?events=event1,event2,event3&variant=medium

# 带图片类型过滤的批量查询
GET /images/batch?events=event1,event2,event3&imageType=poster&variant=medium
```

### 性能测试

```bash
# 大批量查询测试 (50个事件)
GET /images/batch?events=event1,event2,...,event50&variant=medium

# 响应时间应 < 200ms
# 内存使用应 < 50MB
```

### 上传测试

```bash
# 图片上传测试
curl -X POST /admin/images/upload \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@test-image.jpg" \
  -F "resourceType=event" \
  -F "resourceId=test-event-1" \
  -F "imageType=poster" \
  -F "variant=original"
```

### 数据库索引验证

验证以下索引的性能：

- `idx_images_resource_lookup`: 资源查询优化
- `idx_images_resource_variant`: 批量查询优化
- `idx_images_group_variant`: 按组查询优化

## 相关文档

- [Cloudflare R2 配置](../operations/r2-configuration.md)
- [图片存储架构](../architecture/image-storage.md)
- [前端图片集成](../frontend/common-examples.md#图片处理)
- [测试指南](../development/testing.md)
