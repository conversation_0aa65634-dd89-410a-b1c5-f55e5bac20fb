---
sidebar_position: 1
title: 部署指南
description: Ayafeed API 部署和运维指南
---

# 部署指南

本文档介绍如何将 Ayafeed API 部署到生产环境。

## 🚀 部署概览

Ayafeed API 基于 Cloudflare Workers 平台，支持多环境部署：

- **开发环境** (`dev`): 本地开发和测试
- **预发布环境** (`staging`): 功能验证和集成测试
- **生产环境** (`production`): 正式对外服务

## 📋 部署前准备

### 1. 环境要求

- Node.js 18+
- pnpm 8+
- Cloudflare 账号
- Wrangler CLI 工具

### 2. 配置文件

确保以下配置文件已正确设置：

```bash
# Cloudflare Workers 配置
wrangler.jsonc          # 生产环境配置
wrangler.staging.jsonc  # 预发布环境配置
wrangler.dev.jsonc      # 开发环境配置
```

### 3. 环境变量

在 Cloudflare Dashboard 中设置以下环境变量：

```bash
# 必需变量
JWT_SECRET=your-jwt-secret
ADMIN_PASSWORD=your-admin-password

# 可选变量
LOG_LEVEL=info
CACHE_TTL=3600
```

## 🔧 部署步骤

### 1. 准备代码

```bash
# 克隆仓库
git clone https://github.com/ayafeed/ayafeed-api.git
cd ayafeed-api

# 安装依赖
pnpm install

# 构建项目
pnpm build
```

### 2. 数据库初始化

```bash
# 创建 D1 数据库
wrangler d1 create ayafeed-production

# 执行数据库迁移
wrangler d1 execute ayafeed-production --file=db/schema.sql

# 导入基础数据
wrangler d1 execute ayafeed-production --file=db/seeds/000_base.sql
wrangler d1 execute ayafeed-production --file=db/seeds/001_admin.sql
```

### 3. 部署应用

```bash
# 部署到生产环境
pnpm deploy

# 或指定环境部署
wrangler deploy --env production
```

### 4. 验证部署

```bash
# 检查服务状态
curl https://your-domain.com/health

# 验证 API 响应
curl https://your-domain.com/api/v1/circles
```

## 🌍 多环境管理

### 开发环境

```bash
# 启动本地开发
pnpm dev

# 使用本地数据库
wrangler d1 execute ayafeed-dev --local --file=db/schema.sql
```

### 预发布环境

```bash
# 部署到预发布
wrangler deploy --env staging

# 运行集成测试
pnpm test:integration --env staging
```

### 生产环境

```bash
# 部署到生产
wrangler deploy --env production

# 健康检查
pnpm health-check --env production
```

## 📊 监控和告警

### 1. Cloudflare Analytics

在 Cloudflare Dashboard 中查看：

- 请求量和响应时间
- 错误率和状态码分布
- 地理位置分布
- 缓存命中率

### 2. 自定义监控

```typescript
// 在代码中添加监控指标
export async function handleRequest(request: Request): Promise<Response> {
  const start = Date.now();

  try {
    const response = await processRequest(request);

    // 记录成功指标
    await logMetric('request.success', {
      duration: Date.now() - start,
      status: response.status,
    });

    return response;
  } catch (error) {
    // 记录错误指标
    await logMetric('request.error', {
      duration: Date.now() - start,
      error: error.message,
    });

    throw error;
  }
}
```

### 3. 告警设置

在 Cloudflare 中设置告警规则：

- 错误率超过 5%
- 响应时间超过 1000ms
- 请求量异常波动

## 🔄 CI/CD 流程

### GitHub Actions 配置

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: pnpm install

      - name: Run tests
        run: pnpm test

      - name: Deploy to Cloudflare
        run: pnpm deploy
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
```

## 🛠️ 故障排查

### 常见问题

1. **部署失败**

   ```bash
   # 检查配置文件
   wrangler whoami
   wrangler kv:namespace list
   ```

2. **数据库连接错误**

   ```bash
   # 验证数据库配置
   wrangler d1 list
   wrangler d1 info ayafeed-production
   ```

3. **环境变量问题**
   ```bash
   # 检查环境变量
   wrangler secret list
   ```

### 日志查看

```bash
# 查看实时日志
wrangler tail

# 查看特定时间段日志
wrangler tail --since 1h
```

## 📈 性能优化

### 1. 缓存策略

- 静态资源缓存：24小时
- API 响应缓存：5分钟
- 数据库查询缓存：1小时

### 2. 数据库优化

- 添加适当索引
- 优化查询语句
- 定期清理过期数据

### 3. 代码优化

- 减少冷启动时间
- 优化依赖包大小
- 使用 Tree Shaking

## 🔒 安全配置

### 1. HTTPS 强制

```javascript
// 在 Worker 中强制 HTTPS
if (request.url.startsWith('http://')) {
  return Response.redirect(request.url.replace('http://', 'https://'), 301);
}
```

### 2. CORS 配置

```javascript
const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://your-frontend.com',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Locale',
};
```

### 3. 速率限制

```javascript
// 使用 KV 存储实现速率限制
const rateLimiter = new RateLimiter({
  keyGenerator: (request) => getClientIP(request),
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100个请求
});
```

---

**下一步**: [数据库设计](./database.md) | [监控告警](./monitoring.md)
