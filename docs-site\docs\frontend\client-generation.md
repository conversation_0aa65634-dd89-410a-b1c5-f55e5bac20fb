# API客户端生成指南

> 🎯 **目标**: 生成类型安全的API客户端，享受完整的TypeScript支持

## 🛠️ 生成方式对比

| 方式                     | 优点                 | 缺点         | 推荐度     |
| ------------------------ | -------------------- | ------------ | ---------- |
| openapi-typescript-fetch | 轻量、类型安全、易用 | 功能相对简单 | ⭐⭐⭐⭐⭐ |
| openapi-generator        | 功能丰富、支持多语言 | 生成代码较重 | ⭐⭐⭐     |
| 手动封装                 | 完全可控             | 维护成本高   | ⭐⭐       |

## 🚀 推荐方案：openapi-typescript-fetch

### 安装依赖

```bash
npm install openapi-typescript-fetch
npm install -D openapi-typescript
```

### 生成类型定义

```bash
# 从本地文件生成
npx openapi-typescript docs-site/static/openapi.json -o src/types/api.ts

# 从远程URL生成
npx openapi-typescript https://api.ayafeed.com/openapi.json -o src/types/api.ts
```

### 创建API客户端

```typescript
// src/lib/api/client.ts
import { Fetcher } from 'openapi-typescript-fetch';
import type { paths } from '@/types/api';

// 创建客户端实例
const fetcher = Fetcher.for<paths>();

// 基础配置
fetcher.configure({
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787',
  init: {
    headers: {
      'Content-Type': 'application/json',
    },
  },
});

// 导出方法
export const api = {
  GET: fetcher.GET,
  POST: fetcher.POST,
  PUT: fetcher.PUT,
  DELETE: fetcher.DELETE,
  PATCH: fetcher.PATCH,
};

export default api;
```

## 🔧 高级配置

### 环境配置

```typescript
// src/lib/api/config.ts
interface ApiConfig {
  baseUrl: string;
  timeout: number;
  defaultLocale: string;
}

const config: ApiConfig = {
  baseUrl:
    process.env.NODE_ENV === 'production'
      ? 'https://api.ayafeed.com'
      : 'http://localhost:8787',
  timeout: 10000,
  defaultLocale: 'zh',
};

export default config;
```

### 请求拦截器

```typescript
// src/lib/api/interceptors.ts
import { api } from './client';
import config from './config';

// 请求拦截器
function setupRequestInterceptor() {
  const originalConfigure = api.configure;

  api.configure = (options) => {
    const token = localStorage.getItem('auth_token');
    const locale = localStorage.getItem('locale') || config.defaultLocale;

    const enhancedOptions = {
      ...options,
      init: {
        ...options.init,
        headers: {
          'Content-Type': 'application/json',
          'X-Locale': locale,
          ...(token && { Authorization: `Bearer ${token}` }),
          ...options.init?.headers,
        },
      },
    };

    return originalConfigure.call(api, enhancedOptions);
  };
}

// 响应拦截器
function setupResponseInterceptor() {
  const originalMethods = {
    GET: api.GET,
    POST: api.POST,
    PUT: api.PUT,
    DELETE: api.DELETE,
  };

  Object.keys(originalMethods).forEach((method) => {
    const originalMethod =
      originalMethods[method as keyof typeof originalMethods];

    api[method as keyof typeof api] = async (...args: any[]) => {
      try {
        const response = await originalMethod.apply(api, args);

        // 处理响应头中的语言信息
        const responseLocale = response.headers?.get('X-Response-Locale');
        if (responseLocale) {
          localStorage.setItem('server_locale', responseLocale);
        }

        return response;
      } catch (error: any) {
        // 统一错误处理
        if (error.status === 401) {
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }

        throw error;
      }
    };
  });
}

// 初始化拦截器
export function setupInterceptors() {
  setupRequestInterceptor();
  setupResponseInterceptor();
}
```

### 完整的客户端设置

```typescript
// src/lib/api/index.ts
import { Fetcher } from 'openapi-typescript-fetch';
import type { paths } from '@/types/api';
import config from './config';
import { setupInterceptors } from './interceptors';

// 创建客户端
const fetcher = Fetcher.for<paths>();

// 基础配置
fetcher.configure({
  baseUrl: config.baseUrl,
  init: {
    headers: {
      'Content-Type': 'application/json',
    },
  },
});

// 导出API方法
export const api = {
  GET: fetcher.GET,
  POST: fetcher.POST,
  PUT: fetcher.PUT,
  DELETE: fetcher.DELETE,
  PATCH: fetcher.PATCH,
  configure: fetcher.configure,
};

// 设置拦截器
setupInterceptors();

// 工具方法
export const apiUtils = {
  setToken: (token: string) => {
    localStorage.setItem('auth_token', token);
    api.configure({
      init: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    });
  },

  clearToken: () => {
    localStorage.removeItem('auth_token');
    api.configure({
      init: {
        headers: {
          Authorization: undefined,
        },
      },
    });
  },

  setLocale: (locale: string) => {
    localStorage.setItem('locale', locale);
    api.configure({
      init: {
        headers: {
          'X-Locale': locale,
        },
      },
    });
  },
};

export default api;
```

## 📝 使用示例

### 基础使用

```typescript
// src/services/events.ts
import { api } from '@/lib/api';

export const eventService = {
  // 获取事件列表
  async getEvents(params?: { page?: string; pageSize?: string }) {
    const { data } = await api.GET('/events', {
      params: { query: params },
    });
    return data;
  },

  // 获取事件详情
  async getEvent(id: string) {
    const { data } = await api.GET('/events/{id}', {
      params: { path: { id } },
    });
    return data;
  },

  // 创建事件（需要管理员权限）
  async createEvent(eventData: any) {
    const { data } = await api.POST('/admin/events', {
      body: eventData,
    });
    return data;
  },
};
```

### React Hook封装

```typescript
// src/hooks/useApi.ts
import { useState, useEffect } from 'react';
import { api } from '@/lib/api';

export function useEvents(params?: { page?: string; pageSize?: string }) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchEvents() {
      try {
        setLoading(true);
        const result = await api.GET('/events', {
          params: { query: params },
        });
        setData(result.data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    }

    fetchEvents();
  }, [params]);

  return { data, loading, error };
}
```

## 🔄 自动更新脚本

### package.json脚本

```json
{
  "scripts": {
    "api:generate": "openapi-typescript docs-site/static/openapi.json -o src/types/api.ts",
    "api:update": "curl -o openapi.json https://api.ayafeed.com/openapi.json && npm run api:generate",
    "api:watch": "chokidar 'docs-site/static/openapi.json' -c 'npm run api:generate'"
  }
}
```

### 自动化脚本

```bash
#!/bin/bash
# scripts/update-api-client.sh

echo "🔄 更新API客户端..."

# 下载最新的OpenAPI规范
curl -s -o openapi.json https://api.ayafeed.com/openapi.json

# 检查是否有变更
if ! cmp -s openapi.json src/types/api-spec.json; then
  echo "📝 检测到API变更，重新生成类型定义..."

  # 生成新的类型定义
  npx openapi-typescript openapi.json -o src/types/api.ts

  # 备份当前规范
  cp openapi.json src/types/api-spec.json

  echo "✅ API客户端更新完成！"
else
  echo "ℹ️ API无变更，跳过更新"
fi

rm openapi.json
```

## 🎯 最佳实践

### 1. 类型安全

```typescript
// ✅ 好的做法 - 完整类型支持
const { data } = await api.GET('/events/{id}', {
  params: { path: { id: '123' } },
});
// data 自动推断为正确的类型

// ❌ 避免的做法 - 失去类型安全
const response = await fetch('/api/events/123');
const data = await response.json(); // any 类型
```

### 2. 错误处理

```typescript
// ✅ 统一错误处理
try {
  const { data } = await api.GET('/events');
  return data;
} catch (error: any) {
  if (error.status === 404) {
    throw new Error('事件不存在');
  }
  throw error;
}
```

### 3. 缓存策略

```typescript
// ✅ 结合React Query使用
import { useQuery } from '@tanstack/react-query';

function useEvents() {
  return useQuery({
    queryKey: ['events'],
    queryFn: () => api.GET('/events').then((res) => res.data),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}
```

---

**下一步**: 查看 [认证集成指南](./authentication.md) 了解如何处理用户认证 🔐
