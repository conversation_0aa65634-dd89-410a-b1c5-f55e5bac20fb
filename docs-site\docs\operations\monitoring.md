---
sidebar_position: 3
title: 监控告警
description: Ayafeed API 监控和告警系统配置
---

# 监控告警

本文档介绍 Ayafeed API 的监控体系和告警配置。

## 📊 监控概览

Ayafeed API 采用多层次监控策略，确保系统稳定运行：

- **基础设施监控**: Cloudflare Analytics
- **应用监控**: 自定义指标和日志
- **业务监控**: 关键业务指标追踪
- **用户体验监控**: 性能和可用性监控

## 🔧 Cloudflare Analytics

### 1. 内置指标

Cloudflare Workers 提供丰富的内置监控指标：

```javascript
// 在 Worker 中记录自定义指标
export default {
  async fetch(request, env, ctx) {
    const start = Date.now();

    try {
      const response = await handleRequest(request, env);

      // 记录成功指标
      ctx.waitUntil(
        env.ANALYTICS.writeDataPoint({
          blobs: [request.url],
          doubles: [Date.now() - start],
          indexes: ['request_duration'],
        })
      );

      return response;
    } catch (error) {
      // 记录错误指标
      ctx.waitUntil(
        env.ANALYTICS.writeDataPoint({
          blobs: [error.message],
          doubles: [Date.now() - start],
          indexes: ['request_error'],
        })
      );

      throw error;
    }
  },
};
```

### 2. 关键指标

| 指标类型 | 指标名称      | 说明         | 阈值      |
| -------- | ------------- | ------------ | --------- |
| 可用性   | Success Rate  | 成功请求比例 | >99.9%    |
| 性能     | Response Time | 响应时间 P95 | &lt;200ms |
| 性能     | Response Time | 响应时间 P99 | &lt;500ms |
| 错误     | Error Rate    | 错误率       | &lt;0.1%  |
| 流量     | Requests/min  | 每分钟请求数 | 监控趋势  |

## 📈 自定义监控

### 1. 应用指标

```typescript
// 指标收集器
class MetricsCollector {
  constructor(private analytics: AnalyticsEngine) {}

  // 记录 API 调用
  async recordAPICall(
    endpoint: string,
    method: string,
    duration: number,
    status: number
  ) {
    await this.analytics.writeDataPoint({
      blobs: [endpoint, method],
      doubles: [duration, status],
      indexes: ['api_call'],
    });
  }

  // 记录数据库查询
  async recordDBQuery(query: string, duration: number, rows: number) {
    await this.analytics.writeDataPoint({
      blobs: [query],
      doubles: [duration, rows],
      indexes: ['db_query'],
    });
  }

  // 记录缓存命中
  async recordCacheHit(key: string, hit: boolean) {
    await this.analytics.writeDataPoint({
      blobs: [key],
      doubles: [hit ? 1 : 0],
      indexes: ['cache_hit'],
    });
  }
}
```

### 2. 业务指标

```typescript
// 业务指标追踪
class BusinessMetrics {
  constructor(private metrics: MetricsCollector) {}

  // 用户注册
  async recordUserRegistration(source: string) {
    await this.metrics.analytics.writeDataPoint({
      blobs: [source],
      doubles: [1],
      indexes: ['user_registration'],
    });
  }

  // 搜索查询
  async recordSearch(query: string, results: number, locale: string) {
    await this.metrics.analytics.writeDataPoint({
      blobs: [query, locale],
      doubles: [results],
      indexes: ['search_query'],
    });
  }

  // 收藏操作
  async recordBookmark(type: string, action: 'add' | 'remove') {
    await this.metrics.analytics.writeDataPoint({
      blobs: [type, action],
      doubles: [1],
      indexes: ['bookmark_action'],
    });
  }
}
```

## 🚨 告警配置

### 1. Cloudflare 告警

在 Cloudflare Dashboard 中配置告警规则：

```yaml
# 错误率告警
- name: 'High Error Rate'
  condition: 'error_rate > 5%'
  duration: '5 minutes'
  notification: 'email, slack'

# 响应时间告警
- name: 'Slow Response Time'
  condition: 'p95_response_time > 1000ms'
  duration: '10 minutes'
  notification: 'email'

# 流量异常告警
- name: 'Traffic Spike'
  condition: 'requests_per_minute > 1000'
  duration: '5 minutes'
  notification: 'slack'
```

### 2. 自定义告警

```typescript
// 告警管理器
class AlertManager {
  private thresholds = {
    errorRate: 0.05, // 5%
    responseTime: 1000, // 1000ms
    dbConnections: 100, // 100 connections
  };

  async checkMetrics() {
    const metrics = await this.getMetrics();

    // 检查错误率
    if (metrics.errorRate > this.thresholds.errorRate) {
      await this.sendAlert('high_error_rate', {
        current: metrics.errorRate,
        threshold: this.thresholds.errorRate,
      });
    }

    // 检查响应时间
    if (metrics.avgResponseTime > this.thresholds.responseTime) {
      await this.sendAlert('slow_response', {
        current: metrics.avgResponseTime,
        threshold: this.thresholds.responseTime,
      });
    }
  }

  private async sendAlert(type: string, data: any) {
    // 发送到 Slack
    await fetch(process.env.SLACK_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: `🚨 Alert: ${type}`,
        attachments: [
          {
            color: 'danger',
            fields: [
              { title: 'Current Value', value: data.current, short: true },
              { title: 'Threshold', value: data.threshold, short: true },
            ],
          },
        ],
      }),
    });
  }
}
```

## 📊 监控面板

### 1. Grafana 配置

```yaml
# grafana/dashboards/ayafeed-api.json
{
  'dashboard':
    {
      'title': 'Ayafeed API Monitoring',
      'panels':
        [
          {
            'title': 'Request Rate',
            'type': 'graph',
            'targets':
              [
                {
                  'expr': 'rate(http_requests_total[5m])',
                  'legendFormat': '{{method}} {{endpoint}}',
                },
              ],
          },
          {
            'title': 'Response Time',
            'type': 'graph',
            'targets':
              [
                {
                  'expr': 'histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))',
                  'legendFormat': '95th percentile',
                },
              ],
          },
          {
            'title': 'Error Rate',
            'type': 'singlestat',
            'targets':
              [
                {
                  'expr': 'rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])',
                  'legendFormat': 'Error Rate',
                },
              ],
          },
        ],
    },
}
```

### 2. 健康检查端点

```typescript
// 健康检查实现
export async function healthCheck(env: Env): Promise<Response> {
  const checks = {
    database: await checkDatabase(env.DB),
    cache: await checkCache(env.KV),
    external: await checkExternalServices(),
  };

  const allHealthy = Object.values(checks).every((check) => check.healthy);

  return new Response(
    JSON.stringify({
      status: allHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks,
    }),
    {
      status: allHealthy ? 200 : 503,
      headers: { 'Content-Type': 'application/json' },
    }
  );
}

async function checkDatabase(db: D1Database): Promise<HealthCheck> {
  try {
    const start = Date.now();
    await db.prepare('SELECT 1').first();
    const duration = Date.now() - start;

    return {
      healthy: duration &lt; 1000,
      duration,
      message: duration &lt; 1000 ? 'OK' : 'Slow response',
    };
  } catch (error) {
    return {
      healthy: false,
      error: error.message,
    };
  }
}
```

## 📱 移动端监控

### 1. 用户体验监控

```typescript
// 前端性能监控
class PerformanceMonitor {
  static trackPageLoad(page: string) {
    const navigation = performance.getEntriesByType(
      'navigation'
    )[0] as PerformanceNavigationTiming;

    // 发送性能数据到后端
    fetch('/api/metrics/performance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        page,
        loadTime: navigation.loadEventEnd - navigation.fetchStart,
        domContentLoaded:
          navigation.domContentLoadedEventEnd - navigation.fetchStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime,
        firstContentfulPaint: performance.getEntriesByName(
          'first-contentful-paint'
        )[0]?.startTime,
      }),
    });
  }

  static trackAPICall(endpoint: string, duration: number, status: number) {
    fetch('/api/metrics/api-call', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        endpoint,
        duration,
        status,
        timestamp: Date.now(),
      }),
    });
  }
}
```

## 🔍 日志分析

### 1. 结构化日志

```typescript
// 日志格式标准化
interface LogEntry {
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  context: {
    requestId: string;
    userId?: string;
    endpoint: string;
    method: string;
    userAgent: string;
    ip: string;
  };
  metadata?: Record<string, any>;
}

class Logger {
  static info(message: string, context: any, metadata?: any) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: 'info',
      message,
      context,
      metadata,
    };

    console.log(JSON.stringify(entry));
  }

  static error(message: string, error: Error, context: any) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: 'error',
      message,
      context,
      metadata: {
        error: error.message,
        stack: error.stack,
      },
    };

    console.error(JSON.stringify(entry));
  }
}
```

### 2. 日志聚合

```sql
-- 查询错误趋势
SELECT
  DATE(created_at) as date,
  COUNT(*) as error_count
FROM logs
WHERE action = 'ERROR'
  AND created_at >= datetime('now', '-7 days')
GROUP BY DATE(created_at)
ORDER BY date;

-- 查询慢查询
SELECT
  resource,
  AVG(CAST(JSON_EXTRACT(metadata, '$.duration') AS REAL)) as avg_duration,
  COUNT(*) as count
FROM logs
WHERE action = 'DB_QUERY'
  AND CAST(JSON_EXTRACT(metadata, '$.duration') AS REAL) > 1000
GROUP BY resource
ORDER BY avg_duration DESC;
```

## 🎯 SLA 监控

### 服务等级目标

| 指标       | 目标           | 测量方法          |
| ---------- | -------------- | ----------------- |
| 可用性     | 99.9%          | 成功请求 / 总请求 |
| 响应时间   | P95 &lt; 200ms | 95% 请求响应时间  |
| 错误率     | &lt; 0.1%      | 5xx 错误 / 总请求 |
| 数据一致性 | 100%           | 数据验证检查      |

### SLA 报告

```typescript
// SLA 计算
class SLACalculator {
  static async calculateUptime(
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    const totalMinutes =
      (endDate.getTime() - startDate.getTime()) / (1000 * 60);
    const downtime = await this.getDowntimeMinutes(startDate, endDate);

    return ((totalMinutes - downtime) / totalMinutes) * 100;
  }

  static async generateSLAReport(month: string): Promise<SLAReport> {
    const startDate = new Date(`${month}-01`);
    const endDate = new Date(
      startDate.getFullYear(),
      startDate.getMonth() + 1,
      0
    );

    return {
      period: month,
      uptime: await this.calculateUptime(startDate, endDate),
      avgResponseTime: await this.getAverageResponseTime(startDate, endDate),
      errorRate: await this.getErrorRate(startDate, endDate),
      incidents: await this.getIncidents(startDate, endDate),
    };
  }
}
```

---

**下一步**: [故障排查](./troubleshooting.md) | [性能优化](../development/performance.md)
