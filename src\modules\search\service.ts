import type { D1Database } from '@cloudflare/workers-types';

import type { SearchResultItem } from './schema';
import type { C<PERSON>, Logger } from '@/infrastructure';
import type { Locale } from '@/middlewares/locale';

/**
 * 搜索内容
 */
export async function searchContent(
  db: D1Database,
  query: string,
  type: 'all' | 'events' | 'circles',
  locale: Locale,
  cache?: Cache,
  logger?: Logger
): Promise<SearchResultItem[]> {
  if (!query || query.trim().length === 0) {
    return [];
  }

  const trimmedQuery = query.trim();

  // 构造缓存键
  const cacheKey = `search:${locale}:${type}:${trimmedQuery}`;

  if (cache) {
    const cached = await cache.get<SearchResultItem[]>(cacheKey);
    if (cached) {
      logger?.debug?.('searchContent: hit cache', { key: cacheKey });
      return cached;
    }
  }

  const results: SearchResultItem[] = [];
  const searchPattern = `%${trimmedQuery}%`;

  // 搜索事件
  if (type === 'all' || type === 'events') {
    const eventQuery = `
      SELECT
        'event' as type,
        id,
        name_${locale} as name,
        NULL as description,
        venue_name_${locale} as venue_name,
        date_${locale} as start_date,
        image_url,
        CASE
          WHEN name_${locale} LIKE ? THEN 1.0
          WHEN venue_name_${locale} LIKE ? THEN 0.6
          ELSE 0.4
        END as rank
      FROM events
      WHERE (
        name_${locale} LIKE ? OR
        venue_name_${locale} LIKE ?
      )
      AND name_${locale} IS NOT NULL
      ORDER BY rank DESC, date_sort DESC
      LIMIT 25
    `;

    const { results: eventResults } = await db
      .prepare(eventQuery)
      .bind(searchPattern, searchPattern, searchPattern, searchPattern)
      .all();

    results.push(...(eventResults as SearchResultItem[]));
  }

  // 搜索社团
  if (type === 'all' || type === 'circles') {
    const circleQuery = `
      SELECT 
        'circle' as type,
        id,
        name_${locale} as name,
        description_${locale} as description,
        NULL as venue_name,
        NULL as start_date,
        NULL as image_url,
        CASE 
          WHEN name_${locale} LIKE ? THEN 1.0
          WHEN description_${locale} LIKE ? THEN 0.8
          ELSE 0.4
        END as rank
      FROM circles 
      WHERE (
        name_${locale} LIKE ? OR 
        description_${locale} LIKE ?
      )
      AND name_${locale} IS NOT NULL
      ORDER BY rank DESC, name_${locale} ASC
      LIMIT 25
    `;

    const { results: circleResults } = await db
      .prepare(circleQuery)
      .bind(searchPattern, searchPattern, searchPattern, searchPattern)
      .all();

    results.push(...(circleResults as SearchResultItem[]));
  }

  // 按相关性排序
  results.sort((a, b) => b.rank - a.rank);

  // 限制总结果数
  const finalResults = results.slice(0, 50);

  if (cache) {
    // 缓存 5 分钟
    await cache.set(cacheKey, finalResults, 300);
    logger?.debug?.('searchContent: cached results', {
      key: cacheKey,
      count: finalResults.length,
    });
  }

  return finalResults;
}
