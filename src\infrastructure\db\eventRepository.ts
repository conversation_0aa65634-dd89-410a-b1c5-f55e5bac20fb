import type { D1Database } from '@cloudflare/workers-types';

// NOTE: 此文件由模块迁移生成，将原先位于 src/repositories/eventRepository.ts 的实现
// 移至基础设施层，供 Feature-First factory 注入使用。

import type { Event } from '../../modules/event/schema';
import {
  eventSchema,
  EventCreateInput,
  EventUpdateInput,
} from '../../modules/event/schema';

/**
 * 创建 Event 时额外需要的字段
 */
export interface NewEventData extends EventCreateInput {
  id: string;
}

/**
 * EventRepository 定义
 */
export interface EventRepository {
  findById(id: string): Promise<Event | null>;
  create(data: NewEventData): Promise<Event>;
  update(
    id: string,
    partial: EventUpdateInput & { date_sort?: number }
  ): Promise<void>;
  delete(id: string): Promise<void>;
}

/**
 * 基于 Cloudflare D1 的 EventRepository 实现
 */
export class D1EventRepository implements EventRepository {
  constructor(private readonly db: D1Database) {}

  async findById(id: string): Promise<Event | null> {
    const raw = await this.db
      .prepare('SELECT * FROM events WHERE id = ?')
      .bind(id)
      .first();
    if (!raw) return null;
    return eventSchema.parse(raw);
  }

  async create(data: NewEventData): Promise<Event> {
    await this.db
      .prepare(
        `INSERT INTO events (
          id,
          name_en, name_ja, name_zh,
          date_en, date_ja, date_zh,
          date_sort, image_url,
          venue_id,
          url
        ) VALUES (?,?,?,?,?,?,?,?,?,?,?)`
      )
      .bind(
        data.id,
        data.name_en,
        data.name_ja,
        data.name_zh,
        data.date_en,
        data.date_ja,
        data.date_zh,
        data.date_sort ?? null,
        data.image_url ?? null,
        data.venue_id,
        data.url ?? null
      )
      .run();

    // 重新查询，确保拿到数据库生成的 created_at/updated_at
    const created = await this.findById(data.id);
    if (!created) throw new Error('Failed to fetch created event');
    return created;
  }

  async update(id: string, partial: EventUpdateInput): Promise<void> {
    const keys = Object.keys(partial) as (keyof EventUpdateInput)[];
    if (!keys.length) return;
    const sets = keys.map((k) => `${String(k)} = ?`).join(', ');
    const values = keys.map((k) => (partial as any)[k]);

    await this.db
      .prepare(`UPDATE events SET ${sets} WHERE id = ?`)
      .bind(...values, id)
      .run();
  }

  async delete(id: string): Promise<void> {
    await this.db.prepare('DELETE FROM events WHERE id = ?').bind(id).run();
  }
}
