import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { D1Database } from '@cloudflare/workers-types';

import { D1ImageRepository } from '../repository';
import type { CreateImageData } from '../repository';

// Mock D1Database
const mockDB = {
  prepare: vi.fn(),
} as unknown as D1Database;

const mockStmt = {
  bind: vi.fn(),
  run: vi.fn(),
  first: vi.fn(),
  all: vi.fn(),
};

describe('D1ImageRepository', () => {
  let repository: D1ImageRepository;

  beforeEach(() => {
    vi.clearAllMocks();
    repository = new D1ImageRepository(mockDB);

    // Setup default mock chain
    (mockDB.prepare as any).mockReturnValue(mockStmt);
    (mockStmt.bind as any).mockReturnValue(mockStmt);
  });

  describe('create', () => {
    it('should create image record successfully', async () => {
      const imageData: CreateImageData = {
        groupId: 'test-group-id',
        resourceType: 'event',
        resourceId: 'test-event-id',
        imageType: 'poster',
        variant: 'thumb',
        filePath: '/images/events/test-event-id/poster_thumb.jpg',
        fileSize: 1024,
        width: 200,
        height: 300,
        format: 'jpeg',
      };

      const mockCreatedImage = {
        id: 'test-image-id',
        group_id: 'test-group-id',
        resource_type: 'event',
        resource_id: 'test-event-id',
        image_type: 'poster',
        variant: 'thumb',
        file_path: '/images/events/test-event-id/poster_thumb.jpg',
        file_size: 1024,
        width: 200,
        height: 300,
        format: 'jpeg',
        created_at: '2025-01-30T00:00:00Z',
        updated_at: '2025-01-30T00:00:00Z',
      };

      mockStmt.run.mockResolvedValue({ success: true });
      mockStmt.first.mockResolvedValue(mockCreatedImage);

      const result = await repository.create(imageData);

      expect(result).toEqual(mockCreatedImage);
      expect(mockDB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO images')
      );
      expect(mockStmt.bind).toHaveBeenCalledWith(
        expect.any(String), // UUID
        imageData.groupId,
        imageData.resourceType,
        imageData.resourceId,
        imageData.imageType,
        imageData.variant,
        imageData.filePath,
        imageData.fileSize,
        imageData.width,
        imageData.height,
        imageData.format,
        expect.any(String), // created_at
        expect.any(String) // updated_at
      );
    });
  });

  describe('findById', () => {
    it('should find image by id', async () => {
      const mockImage = {
        id: 'test-image-id',
        group_id: 'test-group-id',
        variant: 'thumb',
      };

      mockStmt.first.mockResolvedValue(mockImage);

      const result = await repository.findById('test-image-id');

      expect(result).toEqual(mockImage);
      expect(mockDB.prepare).toHaveBeenCalledWith(
        'SELECT * FROM images WHERE id = ?'
      );
      expect(mockStmt.bind).toHaveBeenCalledWith('test-image-id');
    });

    it('should return null when image not found', async () => {
      mockStmt.first.mockResolvedValue(null);

      const result = await repository.findById('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('findByGroupId', () => {
    it('should find images by group id', async () => {
      const mockImages = [
        { id: '1', group_id: 'test-group-id', variant: 'thumb' },
        { id: '2', group_id: 'test-group-id', variant: 'medium' },
      ];

      mockStmt.all.mockResolvedValue({ results: mockImages });

      const result = await repository.findByGroupId('test-group-id');

      expect(result).toEqual(mockImages);
      expect(mockDB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('WHERE group_id = ?')
      );
      expect(mockStmt.bind).toHaveBeenCalledWith('test-group-id');
    });
  });

  describe('findByResource', () => {
    it('should find images by resource with basic filters', async () => {
      const mockImages = [
        { id: '1', resource_type: 'event', resource_id: 'test-event-id' },
      ];

      mockStmt.all.mockResolvedValue({ results: mockImages });

      const result = await repository.findByResource('event', 'test-event-id');

      expect(result).toEqual(mockImages);
      expect(mockDB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('WHERE resource_type = ? AND resource_id = ?')
      );
      expect(mockStmt.bind).toHaveBeenCalledWith('event', 'test-event-id');
    });

    it('should find images with variant filter', async () => {
      const mockImages = [{ id: '1', variant: 'thumb' }];

      mockStmt.all.mockResolvedValue({ results: mockImages });

      const result = await repository.findByResource('event', 'test-event-id', {
        variant: 'thumb',
      });

      expect(result).toEqual(mockImages);
      expect(mockStmt.bind).toHaveBeenCalledWith(
        'event',
        'test-event-id',
        'thumb'
      );
    });

    it('should find images with pagination', async () => {
      const mockImages = [{ id: '1' }, { id: '2' }];

      mockStmt.all.mockResolvedValue({ results: mockImages });

      const result = await repository.findByResource('event', 'test-event-id', {
        limit: 10,
        offset: 20,
      });

      expect(result).toEqual(mockImages);
      expect(mockStmt.bind).toHaveBeenCalledWith(
        'event',
        'test-event-id',
        10,
        20
      );
    });
  });

  describe('findBatchByEvents', () => {
    it('should find images for multiple events', async () => {
      const mockImages = [
        {
          id: '1',
          resource_type: 'event',
          resource_id: 'event1',
          image_type: 'poster',
          variant: 'medium',
        },
        {
          id: '2',
          resource_type: 'event',
          resource_id: 'event2',
          image_type: 'poster',
          variant: 'medium',
        },
      ];

      mockStmt.all.mockResolvedValue({ results: mockImages });

      const result = await repository.findBatchByEvents([
        'event1',
        'event2',
        'event3',
      ]);

      expect(result).toEqual({
        event1: mockImages[0],
        event2: mockImages[1],
        event3: null,
      });

      expect(mockDB.prepare).toHaveBeenCalledWith(
        expect.stringContaining("WHERE resource_type = 'event'")
      );
      expect(mockDB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('AND resource_id IN (?,?,?)')
      );
      expect(mockStmt.bind).toHaveBeenCalledWith('event1', 'event2', 'event3');
    });

    it('should find images with variant filter', async () => {
      const mockImages = [
        {
          id: '1',
          resource_type: 'event',
          resource_id: 'event1',
          variant: 'large',
        },
      ];

      mockStmt.all.mockResolvedValue({ results: mockImages });

      const result = await repository.findBatchByEvents(['event1'], 'large');

      expect(result).toEqual({
        event1: mockImages[0],
      });

      expect(mockDB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('AND variant = ?')
      );
      expect(mockStmt.bind).toHaveBeenCalledWith('event1', 'large');
    });

    it('should find images with imageType filter', async () => {
      const mockImages = [
        {
          id: '1',
          resource_type: 'event',
          resource_id: 'event1',
          image_type: 'poster',
        },
      ];

      mockStmt.all.mockResolvedValue({ results: mockImages });

      const result = await repository.findBatchByEvents(
        ['event1'],
        undefined,
        'poster'
      );

      expect(result).toEqual({
        event1: mockImages[0],
      });

      expect(mockDB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('AND image_type = ?')
      );
      expect(mockStmt.bind).toHaveBeenCalledWith('event1', 'poster');
    });

    it('should return empty object for empty eventIds array', async () => {
      const result = await repository.findBatchByEvents([]);

      expect(result).toEqual({});
      expect(mockDB.prepare).not.toHaveBeenCalled();
    });
  });

  describe('deleteByPaths', () => {
    it('should delete images by paths', async () => {
      const paths = ['/path1.jpg', '/path2.jpg'];

      mockStmt.run.mockResolvedValue({ success: true });

      const result = await repository.deleteByPaths(paths);

      expect(result).toEqual(paths);
      expect(mockDB.prepare).toHaveBeenCalledWith(
        'DELETE FROM images WHERE file_path IN (?,?)'
      );
      expect(mockStmt.bind).toHaveBeenCalledWith(...paths);
    });

    it('should return empty array for empty paths', async () => {
      const result = await repository.deleteByPaths([]);

      expect(result).toEqual([]);
      expect(mockDB.prepare).not.toHaveBeenCalled();
    });
  });

  describe('deleteByGroupId', () => {
    it('should delete images by group id', async () => {
      mockStmt.run.mockResolvedValue({ changes: 3 });

      const result = await repository.deleteByGroupId('test-group-id');

      expect(result).toBe(3);
      expect(mockDB.prepare).toHaveBeenCalledWith(
        'DELETE FROM images WHERE group_id = ?'
      );
      expect(mockStmt.bind).toHaveBeenCalledWith('test-group-id');
    });

    it('should return 0 when no changes', async () => {
      mockStmt.run.mockResolvedValue({ changes: 0 });

      const result = await repository.deleteByGroupId('non-existent-group');

      expect(result).toBe(0);
    });
  });
});
