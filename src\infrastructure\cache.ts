export interface Cache {
  /** 根据 key 获取缓存值，若不存在或已过期则返回 null */
  get<T = unknown>(key: string): Promise<T | null>;
  /** 设置缓存，ttl 单位：秒；ttl<=0 表示永久 */
  set<T = unknown>(key: string, value: T, ttl?: number): Promise<void>;
  /** 删除缓存 */
  delete(key: string): Promise<void>;
}

interface InternalEntry {
  value: unknown;
  expiresAt: number | null;
}

/**
 * 简易内存缓存，仅供单实例开发 / 单元测试。
 */
export class MemoryCache implements Cache {
  private store = new Map<string, InternalEntry>();
  /** 记录各 key 对应的过期定时器，便于在覆盖或手动删除时清理 */
  private timers = new Map<string, ReturnType<typeof setTimeout>>();

  async get<T = unknown>(key: string): Promise<T | null> {
    const entry = this.store.get(key);
    if (!entry) return null;
    if (entry.expiresAt !== null && entry.expiresAt < Date.now()) {
      this.store.delete(key);
      return null;
    }
    return entry.value as T;
  }

  async set<T = unknown>(key: string, value: T, ttl = 0): Promise<void> {
    // 若已存在定时器，先清理
    const oldTimer = this.timers.get(key);
    if (oldTimer) {
      clearTimeout(oldTimer);
      this.timers.delete(key);
    }

    const expiresAt = ttl > 0 ? Date.now() + ttl * 1000 : null;
    this.store.set(key, { value, expiresAt });

    // 当 ttl>0 时启动自动过期逻辑，确保在 fake timers 场景下也能触发
    if (ttl > 0) {
      const timer = setTimeout(() => {
        this.store.delete(key);
        this.timers.delete(key);
      }, ttl * 1000);
      // vitest/jest fake timers 需要显式保存引用
      this.timers.set(key, timer);
    }
  }

  async delete(key: string): Promise<void> {
    this.store.delete(key);
    const timer = this.timers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(key);
    }
  }
}
