import type {
  EntityType,
  LanguageCode,
  CreateConfigRequest,
  // UpdateConfigRequest,
  CreateContentRequest,
  // UpdateContentRequest,
} from './schema';

/**
 * 业务规则验证器
 * 处理复杂的业务逻辑验证
 */
export class RichTextValidators {
  // ========================================
  // 配置验证规则
  // ========================================

  /**
   * 验证标签页键值的业务规则
   */
  static validateTabKey(key: string, entityType: EntityType): ValidationResult {
    const errors: string[] = [];

    // 基础格式验证
    if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
      errors.push('标签页键值只能包含字母、数字、下划线、连字符');
    }

    // 长度验证
    if (key.length < 2) {
      errors.push('标签页键值至少需要2个字符');
    }

    if (key.length > 50) {
      errors.push('标签页键值最多50个字符');
    }

    // 保留关键字检查
    const reservedKeys = [
      'admin',
      'api',
      'config',
      'system',
      'internal',
      'delete',
      'create',
      'update',
      'edit',
      'new',
    ];

    if (reservedKeys.includes(key.toLowerCase())) {
      errors.push(`"${key}" 是保留关键字，不能使用`);
    }

    // 实体特定的键值规则
    const entitySpecificRules = this.getEntitySpecificKeyRules(entityType);
    const entityErrors = entitySpecificRules.validate(key);
    errors.push(...entityErrors);

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证标签页标签名称
   */
  static validateTabLabel(
    label: string,
    languageCode: LanguageCode
  ): ValidationResult {
    const errors: string[] = [];

    // 基础验证
    if (!label || label.trim().length === 0) {
      errors.push('标签名称不能为空');
    }

    if (label.length > 100) {
      errors.push('标签名称最多100个字符');
    }

    // 语言特定验证
    const languageRules = this.getLanguageSpecificRules(languageCode);
    const languageErrors = languageRules.validateLabel(label);
    errors.push(...languageErrors);

    // 特殊字符检查
    if (/[<>"'&]/.test(label)) {
      errors.push('标签名称不能包含特殊字符 < > " \' &');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证排序值
   */
  static validateSortOrder(
    sortOrder: number,
    existingOrders: number[]
  ): ValidationResult {
    const errors: string[] = [];

    if (sortOrder < 0) {
      errors.push('排序值不能为负数');
    }

    if (!Number.isInteger(sortOrder)) {
      errors.push('排序值必须为整数');
    }

    // 检查是否与现有排序值冲突
    if (existingOrders.includes(sortOrder)) {
      errors.push(`排序值 ${sortOrder} 已被使用`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // ========================================
  // 内容验证规则
  // ========================================

  /**
   * 验证富文本内容
   */
  static validateRichTextContent(content: string): ValidationResult {
    const errors: string[] = [];

    // 长度验证
    if (content.length > 1000000) {
      // 1MB
      errors.push('内容长度不能超过1MB');
    }

    // JSON 格式验证（Tiptap 内容应该是有效的 JSON）
    try {
      const parsed = JSON.parse(content);

      // 验证 Tiptap 文档结构
      const tiptapValidation = this.validateTiptapDocument(parsed);
      if (!tiptapValidation.isValid) {
        errors.push(...tiptapValidation.errors);
      }
    } catch {
      errors.push('内容必须是有效的 JSON 格式');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证 Tiptap 文档结构
   */
  private static validateTiptapDocument(doc: any): ValidationResult {
    const errors: string[] = [];

    // 基础结构验证
    if (!doc || typeof doc !== 'object') {
      errors.push('文档必须是对象类型');
      return { isValid: false, errors };
    }

    if (doc.type !== 'doc') {
      errors.push('文档根节点类型必须是 "doc"');
    }

    if (!Array.isArray(doc.content)) {
      errors.push('文档必须包含 content 数组');
    }

    // 内容深度验证（防止过深嵌套）
    const maxDepth = 10;
    const depth = this.calculateDocumentDepth(doc);
    if (depth > maxDepth) {
      errors.push(`文档嵌套层级过深，最多允许 ${maxDepth} 层`);
    }

    // 节点数量验证（防止过大文档）
    const nodeCount = this.countDocumentNodes(doc);
    const maxNodes = 10000;
    if (nodeCount > maxNodes) {
      errors.push(`文档节点数量过多，最多允许 ${maxNodes} 个节点`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // ========================================
  // 业务规则验证
  // ========================================

  /**
   * 验证配置创建请求（强制后端生成 key 版本）
   */
  static async validateCreateConfig(
    data: CreateConfigRequest,
    existingConfigs: Array<{ key: string; sort_order: number }>
  ): Promise<ValidationResult> {
    const errors: string[] = [];

    // 注意：key 现在由后端自动生成，无需验证
    // 移除了 key 相关的验证逻辑

    // 标签名称验证
    const labelValidation = this.validateTabLabel(
      data.label,
      data.language_code
    );
    if (!labelValidation.isValid) {
      errors.push(...labelValidation.errors);
    }

    // 排序值验证
    if (data.sort_order !== undefined) {
      const existingOrders = existingConfigs.map((c) => c.sort_order);
      const sortValidation = this.validateSortOrder(
        data.sort_order,
        existingOrders
      );
      if (!sortValidation.isValid) {
        errors.push(...sortValidation.errors);
      }
    }

    // 图标验证
    if (data.icon) {
      const iconValidation = this.validateIcon(data.icon);
      if (!iconValidation.isValid) {
        errors.push(...iconValidation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证内容创建请求
   */
  static validateCreateContent(data: CreateContentRequest): ValidationResult {
    const errors: string[] = [];

    // 实体ID验证
    if (!data.entity_id || data.entity_id.trim().length === 0) {
      errors.push('实体ID不能为空');
    }

    // 内容类型验证
    if (!data.content_type || data.content_type.trim().length === 0) {
      errors.push('内容类型不能为空');
    }

    // 内容验证
    const contentValidation = this.validateRichTextContent(data.content);
    if (!contentValidation.isValid) {
      errors.push(...contentValidation.errors);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // ========================================
  // 辅助方法
  // ========================================

  /**
   * 获取实体特定的键值规则
   */
  private static getEntitySpecificKeyRules(entityType: EntityType) {
    const rules = {
      event: {
        validate: (key: string): string[] => {
          const errors: string[] = [];

          // Events 特定规则
          if (key.startsWith('venue_')) {
            errors.push('Events 实体的键值不能以 "venue_" 开头');
          }

          return errors;
        },
      },
      venue: {
        validate: (key: string): string[] => {
          const errors: string[] = [];

          // Venues 特定规则
          if (key.startsWith('event_')) {
            errors.push('Venues 实体的键值不能以 "event_" 开头');
          }

          return errors;
        },
      },
    };

    return rules[entityType];
  }

  /**
   * 获取语言特定的规则
   */
  private static getLanguageSpecificRules(languageCode: LanguageCode) {
    const rules = {
      en: {
        validateLabel: (label: string): string[] => {
          const errors: string[] = [];

          // 英文标签规则
          if (label.length > 0 && !/^[a-zA-Z0-9\s\-_()]+$/.test(label)) {
            errors.push(
              '英文标签只能包含字母、数字、空格、连字符、下划线和括号'
            );
          }

          return errors;
        },
      },
      zh: {
        validateLabel: (label: string): string[] => {
          const errors: string[] = [];

          // 中文标签规则
          if (label.length > 20) {
            errors.push('中文标签最多20个字符');
          }

          return errors;
        },
      },
      ja: {
        validateLabel: (label: string): string[] => {
          const errors: string[] = [];

          // 日文标签规则
          if (label.length > 20) {
            errors.push('日文标签最多20个字符');
          }

          return errors;
        },
      },
    };

    return rules[languageCode];
  }

  /**
   * 验证图标名称
   */
  private static validateIcon(icon: string): ValidationResult {
    const errors: string[] = [];

    // Lucide 图标名称格式验证
    if (!/^[a-z0-9-]+$/.test(icon)) {
      errors.push('图标名称只能包含小写字母、数字和连字符');
    }

    if (icon.length > 50) {
      errors.push('图标名称最多50个字符');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 计算文档嵌套深度
   */
  private static calculateDocumentDepth(
    node: any,
    currentDepth: number = 0
  ): number {
    if (!node || typeof node !== 'object') {
      return currentDepth;
    }

    let maxDepth = currentDepth;

    if (Array.isArray(node.content)) {
      for (const child of node.content) {
        const childDepth = this.calculateDocumentDepth(child, currentDepth + 1);
        maxDepth = Math.max(maxDepth, childDepth);
      }
    }

    return maxDepth;
  }

  /**
   * 计算文档节点数量
   */
  private static countDocumentNodes(node: any): number {
    if (!node || typeof node !== 'object') {
      return 0;
    }

    let count = 1; // 当前节点

    if (Array.isArray(node.content)) {
      for (const child of node.content) {
        count += this.countDocumentNodes(child);
      }
    }

    return count;
  }
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * 验证错误类
 */
export class ValidationError extends Error {
  constructor(
    public errors: string[],
    message: string = '验证失败'
  ) {
    super(`${message}: ${errors.join(', ')}`);
    this.name = 'ValidationError';
  }
}
