import type { MiddlewareHandler } from 'hono';
import { getCookie, setCookie } from 'hono/cookie';

/**
 * 支持的语言列表，与 ADR 0003 保持一致。
 */
export const SUPPORTED_LOCALES = ['en', 'ja', 'zh'] as const;
export type Locale = (typeof SUPPORTED_LOCALES)[number];

/** Cookie 名称 */
const COOKIE_NAME = 'locale';
/** Cookie 有效期（秒）：365 天 */
const ONE_YEAR_SECONDS = 60 * 60 * 24 * 365;

/**
 * 解析 Accept-Language，返回首个被支持的语言代码。
 */
function parseAcceptLanguage(hdr?: string | null): Locale | null {
  if (!hdr) return null;
  const header = hdr || '';
  const parts = header.split(',').map((p) => p.trim());
  for (const part of parts) {
    const [codeRaw] = part.split(';');
    const code = (codeRaw ?? '').toLowerCase();
    const short = code.split('-')[0];
    if (SUPPORTED_LOCALES.includes(short as Locale)) {
      return short as Locale;
    }
  }
  return null;
}

/**
 * localeMiddleware：在 Hono Context 中注入 `locale` 字段，并设置响应头 & Cookie。
 * 优先级：X-Locale > Cookie > Accept-Language > 默认 en
 */
export const localeMiddleware: MiddlewareHandler = async (c, next) => {
  let chosen: Locale | null = null;

  // 1) X-Locale 请求头（最高优先级）
  const xLocale = c.req.header('x-locale');
  if (xLocale && SUPPORTED_LOCALES.includes(xLocale as Locale)) {
    chosen = xLocale as Locale;
  }

  // 2) Cookie
  if (!chosen) {
    const cookieVal = getCookie(c, COOKIE_NAME);
    if (cookieVal && SUPPORTED_LOCALES.includes(cookieVal as Locale)) {
      chosen = cookieVal as Locale;
    }
  }

  // 3) Accept-Language
  if (!chosen) {
    const acceptLang = c.req.header('accept-language');
    const parsed = parseAcceptLanguage(acceptLang);
    if (parsed) chosen = parsed;
  }

  // 4) 默认值
  if (!chosen) chosen = 'en';

  // 注入 Context & 响应头
  c.set('locale', chosen);
  c.header('Content-Language', chosen);
  c.header('X-Response-Locale', chosen);
  c.header('Vary', 'Accept-Language, X-Locale');

  await next();

  // Cookie 同步（若与现有不同）- 在响应处理完成后设置，避免覆盖其他 cookies
  const cookieVal = getCookie(c, COOKIE_NAME);
  if (cookieVal !== chosen) {
    setCookie(c, COOKIE_NAME, chosen, {
      path: '/',
      maxAge: ONE_YEAR_SECONDS,
      sameSite: 'Lax',
    });
  }
};
