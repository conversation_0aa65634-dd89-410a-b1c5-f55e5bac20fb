﻿# 国际化指南（i18n Guide）

Ayafeed API 已在 v0.4.3 版本实现完整的多语言支持，包括多语言缓存、搜索和内容流功能。

## ✅ 已实现功能

- ✅ 多语言请求头检测（X-Locale、Accept-Language、Cookie）
- ✅ 多语言缓存隔离
- ✅ 搜索API多语言支持
- ✅ Feed API多语言支持
- ✅ 标准化响应格式包含locale字段
- ✅ 翻译表数据库设计

## 语言支持

当前支持的语言：

- `en` - English（默认）
- `zh` - 中文
- `ja` - 日本語

## 语言检测优先级

API按以下优先级检测语言：

1. **X-Locale 请求头**（最高优先级，推荐使用）
2. **Cookie** - `locale=zh` 格式
3. **Accept-Language 请求头** - 标准HTTP头部
4. **默认语言** - `en`

## 使用方法

### 前端请求示例

```typescript
// 推荐：使用 X-Locale 头部
const response = await fetch('/api/events', {
  headers: {
    'X-Locale': 'zh',
  },
});

// 或使用 Accept-Language
const response = await fetch('/api/events', {
  headers: {
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  },
});

// 或使用 Cookie
document.cookie = 'locale=ja; path=/';
const response = await fetch('/api/events');
```

### 响应格式

所有API响应都包含语言信息：

```json
{
  "success": true,
  "data": [...],
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### 响应头部

API会设置以下响应头部：

- `Content-Language: zh` - 实际使用的语言
- `X-Response-Locale: zh` - 响应语言
- `Vary: Accept-Language, X-Locale` - 缓存控制

## 数据层设计

```mermaid
erDiagram
  events {
    id TEXT
    name_en TEXT
    name_zh TEXT
    name_ja TEXT
    venue_name_en TEXT
    venue_name_zh TEXT
    venue_name_ja TEXT
  }
  circles {
    id TEXT
    name TEXT
  }
  circle_translations {
    circle_id TEXT FK
    locale TEXT
    name TEXT
    description TEXT
    tags TEXT
  }
  artists {
    id TEXT
    name TEXT
  }
  artist_translations {
    artist_id TEXT FK
    locale TEXT
    description TEXT
  }
  circles ||--o{ circle_translations : has
  artists ||--o{ artist_translations : has
```

## 缓存策略

### 多语言缓存隔离

所有缓存键都包含语言参数，确保不同语言的内容完全隔离：

```
events:zh:all
events:en:all
events:ja:all

search:zh:events:Comiket
search:en:events:Comiket

feed:zh:all:page:1:limit:20
feed:ja:circles:page:1:limit:20
```

### 缓存时间

- 搜索结果：5分钟
- Feed内容：5分钟
- 基础数据：5分钟

## 新增API端点

### 搜索API

```bash
GET /search?q=Comiket&type=events
X-Locale: zh
```

### Feed API

```bash
GET /feed?page=1&limit=20&type=all
X-Locale: ja
```

## 最佳实践

1. **推荐使用 X-Locale 头部**：比 Accept-Language 更简洁明确
2. **缓存友好**：相同语言的请求会命中缓存
3. **降级处理**：不支持的语言会自动降级到英语
4. **一致性**：所有API都遵循相同的语言检测逻辑

## 未来规划

- 🔄 完善翻译表内容管理界面
- 🔄 支持更多语言（韩语、法语等）
- 🔄 实现动态语言包加载
- 🔄 优化搜索的多语言相关性算法
