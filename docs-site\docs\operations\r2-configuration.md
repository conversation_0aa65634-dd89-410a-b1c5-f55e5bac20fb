---
sidebar_position: 3
title: R2 存储配置
description: Cloudflare R2 存储桶配置和管理指南
---

# R2 存储配置指南

本文档介绍如何配置和管理 Cloudflare R2 存储桶，用于图片存储和 CDN 服务。

## 📋 存储桶配置

### 环境对应关系

| 环境           | 存储桶名称               | 用途         | CDN 域名                     |
| -------------- | ------------------------ | ------------ | ---------------------------- |
| **开发环境**   | `ayafeed-public-assets`  | 本地开发测试 | `localhost:3000`             |
| **预发布环境** | `ayafeed-staging-assets` | 功能验证     | `staging-images.ayafeed.com` |
| **生产环境**   | `ayafeed-assets`         | 正式服务     | `images.ayafeed.com`         |

### 当前配置状态

```jsonc
// wrangler.jsonc
{
  "r2_buckets": [
    {
      "binding": "R2",
      "bucket_name": "ayafeed-public-assets", // 开发环境
    },
  ],
  "env": {
    "staging": {
      "r2_buckets": [
        {
          "binding": "R2",
          "bucket_name": "ayafeed-staging-assets",
        },
      ],
    },
    "production": {
      "r2_buckets": [
        {
          "binding": "R2",
          "bucket_name": "ayafeed-assets",
        },
      ],
    },
  },
}
```

## 🚀 部署前准备

### 1. 创建存储桶

```bash
# 创建开发环境存储桶（如果还没有）
wrangler r2 bucket create ayafeed-public-assets

# 创建预发布环境存储桶
wrangler r2 bucket create ayafeed-staging-assets

# 创建生产环境存储桶
wrangler r2 bucket create ayafeed-assets
```

### 2. 配置 CORS 策略

为每个存储桶配置 CORS，允许前端直接访问：

```bash
# 开发环境
wrangler r2 bucket cors put ayafeed-public-assets --file=cors-config.json

# 预发布环境
wrangler r2 bucket cors put ayafeed-staging-assets --file=cors-config.json

# 生产环境
wrangler r2 bucket cors put ayafeed-assets --file=cors-config.json
```

CORS 配置文件 `cors-config.json`：

```json
[
  {
    "AllowedOrigins": [
      "http://localhost:3000",
      "https://staging.ayafeed.com",
      "https://ayafeed.com"
    ],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3600
  }
]
```

### 3. 设置自定义域名

#### 开发环境

- 使用本地 Next.js 静态文件服务
- 路径：`http://localhost:3000/images/...`

#### 预发布环境

```bash
# 添加自定义域名
wrangler r2 bucket domain add ayafeed-staging-assets staging-images.ayafeed.com
```

#### 生产环境

```bash
# 添加自定义域名
wrangler r2 bucket domain add ayafeed-assets images.ayafeed.com
```

## 📁 存储路径规范

### 目录结构

```
/images/
├── events/
│   └── {event-id}/
│       ├── poster.jpg          # 原始海报
│       ├── poster_large.jpg    # 大尺寸变体
│       ├── poster_medium.jpg   # 中等尺寸变体
│       └── poster_thumb.jpg    # 缩略图变体
├── circles/
│   └── {circle-id}/
│       ├── logo.png           # 原始 Logo
│       ├── logo_large.png     # 大尺寸变体
│       ├── logo_medium.png    # 中等尺寸变体
│       └── logo_thumb.png     # 缩略图变体
└── venues/
    └── {venue-id}/
        ├── photo.jpg          # 原始照片
        ├── photo_large.jpg    # 大尺寸变体
        ├── photo_medium.jpg   # 中等尺寸变体
        └── photo_thumb.jpg    # 缩略图变体
```

### 路径生成规则

```typescript
// 实现位置：src/modules/images/service.ts
generateImagePath(
  resourceType: string,    // 'event', 'circle', 'venue'
  resourceId: string,      // 资源 UUID
  imageType: string,       // 'poster', 'logo', 'banner', 'gallery'
  variant: string,         // 'original', 'large', 'medium', 'thumb'
  format: string          // 'jpg', 'png', 'webp'
): string {
  const filename = variant === 'original'
    ? `${imageType}.${format}`
    : `${imageType}_${variant}.${format}`;

  return `/images/${resourceType}s/${resourceId}/${filename}`;
}
```

## 💰 成本预算

### 存储成本

- **存储费用**: $0.015/GB/月
- **预估存储量**: 10GB (第一年)
- **月存储成本**: ~$0.15

### 流量成本

- **出站流量**: $0.36/TB (前 10TB)
- **预估月流量**: 500GB
- **月流量成本**: ~$0.18

### **总预算**: ~$0.33/月 (约 ¥2.4/月)

相比传统 CDN 服务，成本节省 **99.6%**！

## 🔧 管理命令

### 查看存储桶信息

```bash
# 列出所有存储桶
wrangler r2 bucket list

# 查看存储桶详情
wrangler r2 bucket info ayafeed-assets
```

### 文件管理

```bash
# 上传文件
wrangler r2 object put ayafeed-assets/images/test.jpg --file=./test.jpg

# 下载文件
wrangler r2 object get ayafeed-assets/images/test.jpg --file=./downloaded.jpg

# 删除文件
wrangler r2 object delete ayafeed-assets/images/test.jpg

# 列出文件
wrangler r2 object list ayafeed-assets --prefix=images/
```

### 监控和统计

```bash
# 查看存储使用量
wrangler r2 bucket usage ayafeed-assets

# 查看流量统计
wrangler r2 bucket analytics ayafeed-assets
```

## 🔒 安全配置

### 访问控制

- **公开读取**: 允许匿名用户通过 CDN 访问图片
- **私有写入**: 只有后端 API 可以上传/删除文件
- **CORS 限制**: 只允许指定域名访问

### 防盗链保护

```bash
# 设置 Referer 限制（可选）
wrangler r2 bucket policy put ayafeed-assets --file=bucket-policy.json
```

## 📊 监控指标

### 关键指标

- **存储使用量**: 当前存储的文件总大小
- **请求次数**: GET/PUT/DELETE 操作统计
- **流量使用**: 出站流量统计
- **错误率**: 4xx/5xx 错误比例

### 告警设置

建议在 Cloudflare Dashboard 中设置以下告警：

- 月存储量超过 50GB
- 月流量超过 5TB
- 错误率超过 1%

## 🚨 故障排除

### 常见问题

1. **上传失败**
   - 检查存储桶名称是否正确
   - 验证 Workers 绑定配置
   - 确认文件大小未超过限制

2. **访问被拒绝**
   - 检查 CORS 配置
   - 验证自定义域名设置
   - 确认文件路径正确

3. **CDN 缓存问题**
   - 使用版本化文件名
   - 设置合适的缓存头
   - 必要时手动清除缓存

### 调试命令

```bash
# 测试存储桶连接
wrangler r2 bucket list

# 验证文件上传
curl -X PUT "https://images.ayafeed.com/test.txt" -d "test content"

# 检查文件访问
curl -I "https://images.ayafeed.com/images/test.jpg"
```

---

## 📝 下一步

1. **创建所需的存储桶**
2. **配置自定义域名和 SSL**
3. **设置监控和告警**
4. **测试图片上传和访问功能**

配置完成后，图片存储系统将提供高性能、低成本的全球 CDN 服务！
