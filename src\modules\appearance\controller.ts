import { Context } from 'hono';

import { getDB } from '@/infrastructure';
import { jsonError } from '@/utils/errorResponse';

/**
 * 参展记录查询
 */
export async function listAppearances(c: Context) {
  const db = getDB(c);
  const circle_id = c.req.query('circle_id');
  const event_id = c.req.query('event_id');

  const search = new URL(c.req.url).searchParams;
  const page = Math.max(Number(search.get('page') || '1'), 1);
  const pageSize = Math.max(Number(search.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  let baseSql = 'FROM appearances';
  const conditions: string[] = [];
  const params: (string | number)[] = [];

  if (circle_id) {
    conditions.push('circle_id = ?');
    params.push(circle_id);
  }
  if (event_id) {
    conditions.push('event_id = ?');
    params.push(event_id);
  }

  if (conditions.length) {
    baseSql += ' WHERE ' + conditions.join(' AND ');
  }

  // total
  const totalRes = await db
    .prepare(`SELECT COUNT(*) AS total ${baseSql}`)
    .bind(...params)
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  // data
  const { results: items } = await db
    .prepare(`SELECT * ${baseSql} ORDER BY created_at DESC LIMIT ? OFFSET ?`)
    .bind(...params, pageSize, offset)
    .all();

  // 若通过 event_id 查询，提示弃用
  if (event_id) {
    c.header('Deprecation', 'true');
  }

  return c.json({ items, total, page, pageSize });
}

/**
 * 参展记录详情
 */
export async function getAppearance(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const record = await db
    .prepare('SELECT * FROM appearances WHERE id = ?')
    .bind(id)
    .first();
  if (!record) return jsonError(c, 50001, 'Not Found', 404);
  return c.json(record);
}

export async function createAppearance(c: Context) {
  const db = getDB(c);
  const body = await c.req.json();
  const { id, circle_id, event_id, artist_id, booth_id, path } = body;
  if (!booth_id) return jsonError(c, 50002, 'booth_id is required', 400);
  await db
    .prepare(
      'INSERT INTO appearances (id, circle_id, event_id, artist_id, booth_id, path) VALUES (?, ?, ?, ?, ?, ?)'
    )
    .bind(id, circle_id, event_id, artist_id, booth_id, path)
    .run();
  return c.json({ success: true });
}

export async function deleteAppearance(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  await db.prepare('DELETE FROM appearances WHERE id = ?').bind(id).run();
  return c.json({ success: true });
}
