# Ayafeed API DDD 实践标准

> **适用版本：自 v0.6.0 起生效**
>
> 本文是 Ayafeed API 的 DDD（Domain-Driven Design）分层落地规范，旨在为后续模块迁移与新功能开发提供统一参考。

---

## 1. 总体目标

1. **高内聚 / 低耦合**：清晰划分职责，避免跨层依赖渗透。
2. **易扩展 / 可演进**：新增业务流程无需侵入领域模型；外部系统替换对核心代码“无感”。
3. **可测试 / 可维护**：每一层都能在隔离环境下编写单元或集成测试。

---

## 2. 分层模型一览

```mermaid
flowchart TD
  client((Client)) --> IF["Interface Layer\n(Controller / Adapter)"]
  IF --> APP["Application Layer\n(ApplicationService)"]
  APP --> DOM["Domain Layer\n(Entity / Aggregate / DomainService)"]
  DOM --> INFRA["Infrastructure Layer\n(DB / Cache / 3rd-Party via ACL)"]
  INFRA -.-> EXT["External Systems"]
```

- `Interface`：面向协议，承担 _IO 转换_。
- `Application`：面向用例，承担 _业务流程编排_。
- `Domain`：面向业务，承担 _核心业务规则_。
- `Infrastructure`：面向技术，承担 _持久化与外部依赖_。

> 当前仓库已存在的 `Controller / Service / Schema` 可分别映射到 **Interface / Application / DTO**，但需按下文规范逐步收敛。

---

## 3. Interface 层规范

| 维度 | 约束                                                                                    | 说明                                      |
| ---- | --------------------------------------------------------------------------------------- | ----------------------------------------- |
| 职责 | • 解析网络协议<br />• Session / 权限上下文<br />• 请求→**CQE** 映射<br />• 全局异常捕获 | 不处理业务逻辑，也不直接访问数据库        |
| 输入 | 原始请求（HTTP、RPC…）                                                                  | 解析后组装为 CQE 对象                     |
| 输出 | `Result<T>` 统一包装                                                                    | `T` 为 DTO；错误经 AOP 转为 ErrorResponse |
| 命名 | `***Controller.ts` / `***Adapter.ts`                                                    | 一业务/子域一接口类即可                   |

示例：

```ts
// src/controllers/circleController.ts (简化)
app.post('/circles', async (c) => {
  const cmd = schema.CircleCreateCommand.parse(await c.req.json());
  const dto = await circleAppService.create(cmd);
  return jsonSuccess(c, dto, 201);
});
```

---

## 4. Application 层规范

| 维度 | 约束                                                                       | 说明                                          |
| ---- | -------------------------------------------------------------------------- | --------------------------------------------- |
| 职责 | • 业务**流程编排**<br />• 调用 Domain Service / Repository<br />• 事务控制 | 不实现领域规则                                |
| 输入 | **Command / Query / Event（CQE）**                                         | 每个用例定义独立对象，防止语义混淆            |
| 输出 | **DTO**                                                                    | 通过 Assembler 转换；禁止返回 Entity / DO     |
| 异常 | 可直接抛出业务异常                                                         | 由 Interface 层统一捕获                       |
| 命名 | `***AppService.ts`                                                         | 方法以动词开头：`create* / update* / list* …` |

### 4.1 CQE 设计准则

1. **单一语义**：字段相同也不可跨用例复用。
2. **只做基础校验**：使用 `zod` + `@hono/zod-openapi`；复杂规则置于 Domain。
3. **禁止拼装 Map / Any**：确保类型透明、可维护。

### 4.2 DTO & Assembler

```text
src/assemblers/
  └── circleAssembler.ts  // Entity ↔ DTO ↔ QueryResult
```

Assembler 职责：

1. 隔离 `Entity` 与 `DTO` 字段差异；
2. 集中管理映射逻辑，方便单元测试。

---

## 5. Domain 层规范

在分层架构中，Domain 层负责承载 **核心业务规则**，由下列三类对象构成：

- **Entity / ValueObject / Aggregate**：封装业务不变量；
- **DomainService**：处理跨 Aggregate 的领域逻辑；
- **Repository**：负责持久化抽象，仅声明领域需要的数据访问方法。

### 5.1 Domain Primitive

- **定义**：封装 _不可再分_ 的业务基本类型（如 `Email`、`TelephoneAreaCode`），在构造时完成 _格式验证_ 与 _业务校验_，保证对象一旦创建即处于合法状态。
- **实现要点**：
  1. 使用 **不可变对象**；
  2. 仅暴露 _行为_ 而非裸字段；
  3. 避免与持久化框架耦合（可通过 `toValue()` 导出原始值）。

### 5.2 Entity / ValueObject / Aggregate 进阶

- **聚合边界**：以 _业务一致性_ 为核心，保证聚合内事务原子性；跨聚合通过 Domain Event 协调。
- **不变量**：在构造函数与 _行为方法_ 中维护，禁止外部直接修改内部状态。
- **工厂模式**：复杂聚合建议通过 `Factory` 创建，统一注入依赖并隐藏构造细节。

### 5.3 Domain Service 设计规范

- 仅在 **跨聚合 / 无合适主体** 的场景使用；
- 保持 _无状态_，依赖通过方法参数注入；
- 命名以动词短语开头，如 `calculateShippingFee`。

### 5.4 Repository 与持久化映射策略

- Repository 接口文件放置于 `src/modules/<feature>/repository.ts`（Feature-First 目录），与领域模型同层以保持高内聚；
- 每个具体实现类放置于 `src/infrastructure/db/<feature>Repository.ts`（或按数据源分子目录），遵守“接口/实现分离”；
- 仅暴露 **领域友好** 的方法，如 `findActiveByUserId`；
- 使用 **Specification** 或 Query Object 封装复杂查询；
- 基础设施层决定 ORM / SQL，实现与接口解耦。

#### 命名与目录速查表（Feature-First）

| 角色                        | 推荐文件 / 目录                             | 说明             |
| --------------------------- | ------------------------------------------- | ---------------- |
| Controller                  | `src/controllers/circleController.ts`       | Interface 层     |
| ApplicationService          | `src/modules/circle/circleAppService.ts`    | 用例编排         |
| Repository Interface        | `src/modules/circle/repository.ts`          | 与领域模型同目录 |
| Repository Implementation   | `src/infrastructure/db/circleRepository.ts` | 技术细节实现     |
| Entity / VO / DomainService | `src/domain/circle/*`                       | 领域层对象       |

### 5.5 Domain Event 及事件处理模式

- 领域模型内部通过 `DomainEvent.publish()` 抛出事件；
- Application 层订阅并编排外部集成（Outbox、消息总线）；
- 事件对象应保持 _最小必要信息_，避免泄露内部实现。

### 5.6 ACL（防腐层）最佳实践

- 对接第三方 SDK / DTO 时，**禁止**在领域层出现外部类型；
- 通过 `XXXAclService` 适配并转换，集中处理异常与重试逻辑；
- ACL 内部可复用 `Mapper`/`Assembler`，但禁止反向依赖领域模型。

---

## 6. Infrastructure 层

1. **数据持久化**：Prisma / Kysely / Drizzle，均通过 Repository 实现类暴露。
2. **第三方服务**：Cloudflare KV、Stripe… 均封装在 `src/infrastructure/third-party`。
3. **ACL**：转换外部 DTO → 内部模型，防止“破窗效应”。

---

## 7. 跨层协作与模式选型

企业级系统往往涉及多服务、异步消息与跨库事务，**跨层协作策略**决定了整体可扩展性与一致性成本。下列子章节给出常用模式及选型指南。

### 7.1 Orchestration vs Choreography

| 场景                 | 推荐模式                 | 说明                        |
| -------------------- | ------------------------ | --------------------------- |
| 单聚合、一致性要求强 | **编排 (Orchestration)** | ApplicationService 顺序调用 |
| 多微服务、事件驱动   | **编舞 (Choreography)**  | 通过发布领域事件解耦        |

### 7.2 Saga、Outbox、幂等保障

- **Saga**：通过本地事务 + 反向补偿保证跨服务一致性，推荐在分布式订单、支付等场景使用。
- **Outbox**：在同一事务内写消息表，异步投递至消息队列，解决 _双写不一致_ 问题。
- **幂等 Token**：Interface 层可注入一次性 token，或在事件处理中利用消息唯一键实现幂等。

### 7.3 事务策略（本地事务、两阶段、TCC）

- **本地事务**：聚合内 ACID，首选方案；
- **2PC**：强一致性但阻塞资源，谨慎使用；
- **TCC**：Try/Confirm/Cancel，适合高并发场景，需设计 _空回滚_ 与 _悬挂_ 处理。

### 7.4 测试金字塔在 DDD 下的落地

- **Unit Test（底座）**：Entity / VO / Service 100% 覆盖；
- **Integration Test（中层）**：Repository Stub、应用服务编排；
- **E2E（顶层）**：合同测试 & BFF/前端契约。

---

## 8. 迁移指南

1. **识别边界**：以 _子域_ 为单位，找出现有 Controller / Service。
2. **创建 CQE / DTO**：
   - 复制现有请求/响应 Schema → 拆分为 `*Command` `*Query` `*DTO`。
3. **重命名 Service**：将 `circleService.ts` → `circleAppService.ts`，仅保留流程，剥离业务规则。
4. **提炼领域模型**：将纯业务规则挪入 `src/domain/` 对应 Entity / DomainService。
5. **实现 Repository**：在 `src/modules/<feature>/repository.ts` 定义接口，在 `src/infrastructure/db/<feature>Repository.ts` 提供实现。
6. **Interface 层调整**：Controller 仅负责 CQE 组装与结果包装；移除业务代码。
7. **测试回归**：
   - 单元：Assember / DomainService / Repository Mock。
   - 集成：Controller + Application 全链路。
8. **文档更新**：每完成一模块迁移，更新 `docs/dev/layered-architecture.md` 迁移进度表。

---

## 9. 常见问题解答（FAQ）

| 问题                                  | 解答                                                                |
| ------------------------------------- | ------------------------------------------------------------------- |
| CQE 与 DTO 区别？                     | CQE = _输入_；DTO = _输出_。CQE 不可复用，DTO 可在多场景复用。      |
| Application 层能直接注入 ORM 实例吗？ | 否，必须通过 Repository 接口。                                      |
| Domain 对外抛异常合适吗？             | 可以抛出领域异常（继承 `DomainError`）；由 Application 捕获并转换。 |

---

## 10. 版本控制与生效策略

- 文档更新需通过 Pull Request；必须在 `CHANGELOG.md` 标注《DDD 规范》变化。
- 新增模块 **必须** 遵循本文；旧模块迁移可分阶段执行，但**不得**新增违背规范的代码。

---

> 如对规范有疑问，请在 PR 中 @maintainers 讨论。

# DDD 实践指南

## 模块化设计

### Schema 管理

每个模块（如 circle、event、user 等）都包含自己的 schema 定义，这些 schema 定义了该模块的数据结构和验证规则。schema 文件位于各自模块目录下的 `schema.ts`。

#### Schema 导入规则

为了保持模块的独立性和内聚性，我们有以下导入规则：

1. 模块内部：
   - 可以直接使用相对路径导入自己的 schema
   - 示例：`import { Circle } from './schema';`

2. 基础设施层：
   - 可以通过类型导入引用模块的 schema
   - 示例：`import type { Circle } from '../../modules/circle/schema';`

3. 跨模块引用：
   - 禁止直接引用其他模块的 schema
   - 如需共享类型，应在模块中显式导出

这些规则通过 ESLint 的 `no-restricted-imports` 规则强制执行。

### 模块结构

每个功能模块应包含以下文件：

- `schema.ts`: 数据结构定义和验证规则
- `repository.ts`: 数据访问接口和实现工厂
- `service.ts`: 业务逻辑实现
- `routes.ts`: API 路由定义
- `tests/`: 单元测试和集成测试

## 最佳实践

1. 保持模块独立性：
   - 每个模块应该是自包含的
   - 避免模块间的直接依赖
   - 通过显式的接口进行模块间通信

2. 类型安全：
   - 使用 TypeScript 和 Zod 保证类型安全
   - 在模块边界处进行数据验证
   - 利用 OpenAPI 注解提供 API 文档

3. 测试策略：
   - 为每个模块编写完整的测试套件
   - 包括单元测试和集成测试
   - 测试文件应与源文件保持相同的组织结构
