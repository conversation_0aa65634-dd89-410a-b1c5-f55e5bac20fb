import { Context } from 'hono';

import { getDB } from '@/infrastructure';
import { jsonError } from '@/utils/errorResponse';
import { jsonWithFields } from '@/utils/fieldFilter';

/**
 * 作者列表
 */
export async function listArtists(c: Context) {
  const db = getDB(c);
  const search = new URL(c.req.url).searchParams;
  const page = Math.max(Number(search.get('page') || '1'), 1);
  const pageSize = Math.max(Number(search.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  const totalRes = await db
    .prepare('SELECT COUNT(*) AS total FROM artists')
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  const { results: items } = await db
    .prepare('SELECT * FROM artists ORDER BY name ASC LIMIT ? OFFSET ?')
    .bind(pageSize, offset)
    .all();

  return jsonWithFields(c, { items, total, page, pageSize });
}

/**
 * 作者详情
 */
export async function getArtist(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const lang = c.req.query('lang') || 'ja';
  const artist = await db
    .prepare('SELECT * FROM artists WHERE id = ?')
    .bind(id)
    .first();
  if (!artist) return jsonError(c, 60002, '资源不存在', 404);
  const translation = await db
    .prepare(
      'SELECT description FROM artist_translations WHERE artist_id = ? AND locale = ?'
    )
    .bind(id, lang)
    .first();
  return jsonWithFields(c, {
    ...artist,
    description: translation?.description || null,
  });
}
