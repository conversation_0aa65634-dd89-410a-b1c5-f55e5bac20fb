// D1Database 类型现在在全局作用域中可用
import { v4 as uuidv4 } from 'uuid';

/**
 * 游标编码/解码工具
 * 使用 URL-safe 编码，避免 base64 兼容性问题
 */
function encodeCursor(createdAt: string): string {
  // 使用 URL 编码作为简单的游标实现
  return encodeURIComponent(JSON.stringify({ created_at: createdAt }));
}

function decodeCursor(cursor: string): string {
  try {
    const decoded = JSON.parse(decodeURIComponent(cursor));
    return decoded.created_at;
  } catch {
    throw new Error('Invalid cursor format');
  }
}

import type {
  BookmarkListQuery,
  BookmarkListItem,
  BookmarkListResponse,
  BookmarkStatusResponse,
  BookmarkStatsResponse,
  BookmarkBatchRequest,
  BookmarkBatchResponse,
} from './schema';

/**
 * Bookmark 数据访问仓库
 */
export interface BookmarkRepository {
  /**
   * 切换收藏状态，返回最新 isBookmarked 状态
   */
  toggle(userId: string, circleId: string): Promise<boolean>;

  /**
   * 获取用户收藏列表
   */
  getUserBookmarks(
    userId: string,
    query: BookmarkListQuery
  ): Promise<BookmarkListResponse>;

  /**
   * 检查收藏状态
   */
  getBookmarkStatus(
    userId: string,
    circleId: string
  ): Promise<BookmarkStatusResponse>;

  /**
   * 获取收藏统计
   */
  getBookmarkStats(
    userId: string,
    includeIds?: boolean
  ): Promise<BookmarkStatsResponse>;

  /**
   * 批量操作收藏
   */
  batchBookmarks(
    userId: string,
    request: BookmarkBatchRequest
  ): Promise<BookmarkBatchResponse>;
}

/**
 * 工厂：根据不同数据库返回实现
 */
export function createBookmarkRepository(db: D1Database): BookmarkRepository {
  return {
    async toggle(userId, circleId) {
      // 检查是否已存在记录
      const existing = await db
        .prepare('SELECT id FROM bookmarks WHERE user_id = ? AND circle_id = ?')
        .bind(userId, circleId)
        .first<{ id: string }>();

      if (existing) {
        // 已收藏 -> 取消
        await db
          .prepare('DELETE FROM bookmarks WHERE user_id = ? AND circle_id = ?')
          .bind(userId, circleId)
          .run();
        return false;
      }

      // 未收藏 -> 新增
      const id = uuidv4();
      await db
        .prepare(
          'INSERT INTO bookmarks (id, user_id, circle_id) VALUES (?, ?, ?)'
        )
        .bind(id, userId, circleId)
        .run();
      return true;
    },

    async getUserBookmarks(userId, query) {
      const { page, pageSize, search, sortBy, sortOrder, cursor } = query;

      // 决定使用游标分页还是传统分页
      const useCursorPaging = !!cursor;

      // 构建基础查询
      let baseQuery = `
        FROM bookmarks b
        JOIN circles c ON b.circle_id = c.id
        WHERE b.user_id = ?
      `;
      const params: (string | number)[] = [userId];

      // 添加搜索条件
      if (search) {
        baseQuery += ` AND c.name LIKE ?`;
        params.push(`%${search}%`);
      }

      // 添加游标条件
      if (useCursorPaging) {
        const cursorCreatedAt = decodeCursor(cursor);
        if (sortOrder === 'desc') {
          baseQuery += ` AND b.created_at < ?`;
        } else {
          baseQuery += ` AND b.created_at > ?`;
        }
        params.push(cursorCreatedAt);
      }

      // 构建排序条件
      let orderBy = 'ORDER BY ';
      if (sortBy === 'circle_name') {
        orderBy += `c.name ${sortOrder}`;
      } else {
        orderBy += `b.created_at ${sortOrder}`;
      }

      let total = 0;
      let totalPages = 0;

      // 传统分页需要计算总数
      if (!useCursorPaging) {
        const countQuery = `SELECT COUNT(*) as total ${baseQuery}`;
        const countResult = await db
          .prepare(countQuery)
          .bind(
            ...params.slice(
              0,
              useCursorPaging ? params.length - 1 : params.length
            )
          )
          .first<{ total: number }>();
        total = countResult?.total || 0;
        totalPages = Math.ceil(total / pageSize);
      }

      // 获取数据（游标分页时多查询一条用于判断是否还有更多数据）
      const limit = useCursorPaging ? pageSize + 1 : pageSize;
      const offset = useCursorPaging ? 0 : (page - 1) * pageSize;

      const dataQuery = `
        SELECT
          b.id,
          b.created_at,
          c.id as circle_id,
          c.name as circle_name,
          c.urls as circle_urls,
          c.created_at as circle_created_at,
          c.updated_at as circle_updated_at
        ${baseQuery}
        ${orderBy}
        LIMIT ?${useCursorPaging ? '' : ' OFFSET ?'}
      `;

      const queryParams = useCursorPaging
        ? [...params, limit]
        : [...params, limit, offset];

      const { results } = await db
        .prepare(dataQuery)
        .bind(...queryParams)
        .all<{
          id: string;
          created_at: string;
          circle_id: string;
          circle_name: string;
          circle_urls: string | null;
          circle_created_at: string;
          circle_updated_at: string;
        }>();

      // 处理游标分页的额外数据
      let hasMore = false;
      let nextCursor: string | null = null;

      if (useCursorPaging && results.length > pageSize) {
        hasMore = true;
        results.pop(); // 移除多查询的那一条
        // 使用最后一条记录的 created_at 作为下一页的游标
        const lastItem = results[results.length - 1];
        if (lastItem) {
          nextCursor = encodeCursor(lastItem.created_at);
        }
      } else if (useCursorPaging) {
        hasMore = false;
        nextCursor = null;
      }

      // 转换数据格式
      const items: BookmarkListItem[] = results.map((row) => ({
        id: row.id,
        created_at: row.created_at,
        circle: {
          id: row.circle_id,
          name: row.circle_name,
          urls: row.circle_urls,
          created_at: row.circle_created_at,
          updated_at: row.circle_updated_at,
        },
      }));

      return {
        items,
        total,
        page: useCursorPaging ? 1 : page, // 游标分页时页码概念不适用
        pageSize,
        totalPages,
        nextCursor,
        hasMore: useCursorPaging ? hasMore : page < totalPages,
      };
    },

    async getBookmarkStatus(userId, circleId) {
      const bookmark = await db
        .prepare(
          'SELECT id, created_at FROM bookmarks WHERE user_id = ? AND circle_id = ?'
        )
        .bind(userId, circleId)
        .first<{ id: string; created_at: string }>();

      return {
        isBookmarked: !!bookmark,
        bookmarkId: bookmark?.id || null,
        createdAt: bookmark?.created_at || null,
      };
    },

    async getBookmarkStats(userId, includeIds = false) {
      // 获取总收藏数
      const totalResult = await db
        .prepare('SELECT COUNT(*) as total FROM bookmarks WHERE user_id = ?')
        .bind(userId)
        .first<{ total: number }>();
      const totalBookmarks = totalResult?.total || 0;

      // 获取最近7天新增收藏数
      const recentResult = await db
        .prepare(
          `
          SELECT COUNT(*) as recent
          FROM bookmarks
          WHERE user_id = ?
          AND created_at >= datetime('now', '-7 days')
        `
        )
        .bind(userId)
        .first<{ recent: number }>();
      const recentBookmarks = recentResult?.recent || 0;

      // 分类统计功能已移除
      // 由于 circles 数据难以爬取且不可控，不再支持分类功能
      const categoryCounts: Record<string, number> = {};

      // 基础统计数据
      const baseStats = {
        totalBookmarks,
        recentBookmarks,
        categoryCounts,
      };

      // 如果不需要ID列表，直接返回基础统计
      if (!includeIds) {
        return baseStats;
      }

      // 获取收藏的社团ID列表（按创建时间倒序）
      const bookmarkedIdsResult = await db
        .prepare(
          `
          SELECT circle_id
          FROM bookmarks
          WHERE user_id = ?
          ORDER BY created_at DESC
        `
        )
        .bind(userId)
        .all<{ circle_id: string }>();

      const bookmarkedCircleIds = bookmarkedIdsResult.results.map(
        (row) => row.circle_id
      );

      return {
        ...baseStats,
        bookmarkedCircleIds,
      };
    },

    async batchBookmarks(userId, request) {
      const { action, circleIds } = request;
      const success: string[] = [];
      const failed: Array<{ circleId: string; reason: string }> = [];

      // 验证社团是否存在
      const circleCheckQuery = `
        SELECT id FROM circles WHERE id IN (${circleIds.map(() => '?').join(',')})
      `;
      const { results: existingCircles } = await db
        .prepare(circleCheckQuery)
        .bind(...circleIds)
        .all<{ id: string }>();

      const existingCircleIds = new Set(existingCircles.map((c) => c.id));

      for (const circleId of circleIds) {
        try {
          // 检查社团是否存在
          if (!existingCircleIds.has(circleId)) {
            failed.push({ circleId, reason: '社团不存在' });
            continue;
          }

          if (action === 'add') {
            // 检查是否已收藏
            const existing = await db
              .prepare(
                'SELECT id FROM bookmarks WHERE user_id = ? AND circle_id = ?'
              )
              .bind(userId, circleId)
              .first<{ id: string }>();

            if (existing) {
              failed.push({ circleId, reason: '已经收藏过了' });
              continue;
            }

            // 添加收藏
            const id = uuidv4();
            await db
              .prepare(
                'INSERT INTO bookmarks (id, user_id, circle_id) VALUES (?, ?, ?)'
              )
              .bind(id, userId, circleId)
              .run();

            success.push(circleId);
          } else if (action === 'remove') {
            // 检查是否已收藏
            const existing = await db
              .prepare(
                'SELECT id FROM bookmarks WHERE user_id = ? AND circle_id = ?'
              )
              .bind(userId, circleId)
              .first<{ id: string }>();

            if (!existing) {
              failed.push({ circleId, reason: '尚未收藏' });
              continue;
            }

            // 删除收藏
            await db
              .prepare(
                'DELETE FROM bookmarks WHERE user_id = ? AND circle_id = ?'
              )
              .bind(userId, circleId)
              .run();

            success.push(circleId);
          }
        } catch (error) {
          failed.push({
            circleId,
            reason: error instanceof Error ? error.message : '操作失败',
          });
        }
      }

      return {
        success,
        failed,
        total: circleIds.length,
        successCount: success.length,
        failedCount: failed.length,
      };
    },
  };
}
