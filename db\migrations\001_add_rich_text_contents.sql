-- ----------------------------------------
-- 富文本内容表迁移
-- 为事件、场馆、社团添加富文本内容支持
-- ----------------------------------------

-- 富文本内容表
DROP TABLE IF EXISTS rich_text_contents;
CREATE TABLE rich_text_contents (
  id TEXT PRIMARY KEY,                     -- uuid
  entity_type TEXT NOT NULL,               -- 'event', 'venue', 'circle'
  entity_id TEXT NOT NULL,                 -- 关联的实体ID
  content_type TEXT NOT NULL,              -- 'introduction', 'highlights', 'guide', 'notices'
  content TEXT NOT NULL,                   -- 富文本HTML内容
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  -- 约束：同一实体的同一内容类型只能有一条记录
  UNIQUE(entity_type, entity_id, content_type),
  
  -- 检查约束
  CHECK(entity_type IN ('event', 'venue', 'circle')),
  CHECK(content_type IN ('introduction', 'highlights', 'guide', 'notices'))
);

-- 索引优化
-- 按实体类型和ID查询内容
CREATE INDEX IF NOT EXISTS idx_rich_text_entity 
ON rich_text_contents(entity_type, entity_id);

-- 按实体类型、ID和内容类型查询特定内容
CREATE INDEX IF NOT EXISTS idx_rich_text_entity_content 
ON rich_text_contents(entity_type, entity_id, content_type);

-- 按更新时间查询最近修改的内容
CREATE INDEX IF NOT EXISTS idx_rich_text_updated 
ON rich_text_contents(updated_at);
