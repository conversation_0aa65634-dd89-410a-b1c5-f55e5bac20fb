import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Hono } from 'hono';
import { testClient } from 'hono/testing';
// D1Database 类型现在在全局作用域中可用
import { richTextRoutes } from '../routes-new';
import type { EntityType, LanguageCode } from '../schema';

// Mock D1 Database for testing
const createMockD1Database = (): D1Database => {
  const mockData = new Map<string, any>();

  return {
    prepare: (query: string) => ({
      bind: (...params: any[]) => ({
        all: async () => ({ results: [], success: true, meta: {} }),
        first: async () => null,
        run: async () => ({
          success: true,
          meta: { changes: 1, last_row_id: 1 },
        }),
      }),
      all: async () => ({ results: [], success: true, meta: {} }),
      first: async () => null,
      run: async () => ({
        success: true,
        meta: { changes: 1, last_row_id: 1 },
      }),
    }),
    dump: async () => new ArrayBuffer(0),
    batch: async () => [],
    exec: async () => ({ count: 0, duration: 0 }),
  } as D1Database;
};

describe('Rich Text Tabs API Integration Tests', () => {
  let app: Hono;
  let client: ReturnType<typeof testClient>;
  let mockDb: D1Database;

  beforeEach(() => {
    mockDb = createMockD1Database();
    app = new Hono();

    // Add middleware to inject mock database
    app.use('*', async (c, next) => {
      c.env = { DB: mockDb };
      await next();
    });

    app.route('/api/rich-text', richTextRoutes);
    client = testClient(app);
  });

  afterEach(() => {
    // Cleanup if needed
  });

  describe('Config Management API', () => {
    const entityType: EntityType = 'event';
    const languageCode: LanguageCode = 'en';

    describe('GET /configs', () => {
      it('should return active configs', async () => {
        const response = await client.api['rich-text'].configs.$get({
          query: {
            entity_type: entityType,
            language_code: languageCode,
          },
        });

        expect(response.status).toBe(200);

        const data = await response.json();
        expect(data).toHaveProperty('configs');
        expect(Array.isArray(data.configs)).toBe(true);
      });

      it('should return all configs when include_deleted is true', async () => {
        const response = await client.api['rich-text'].configs.$get({
          query: {
            entity_type: entityType,
            language_code: languageCode,
            include_deleted: 'true',
          },
        });

        expect(response.status).toBe(200);

        const data = await response.json();
        expect(data).toHaveProperty('configs');
        expect(Array.isArray(data.configs)).toBe(true);
      });

      it('should return 400 for invalid entity_type', async () => {
        const response = await client.api['rich-text'].configs.$get({
          query: {
            entity_type: 'invalid' as EntityType,
            language_code: languageCode,
          },
        });

        expect(response.status).toBe(400);
      });

      it('should return 400 for invalid language_code', async () => {
        const response = await client.api['rich-text'].configs.$get({
          query: {
            entity_type: entityType,
            language_code: 'invalid' as LanguageCode,
          },
        });

        expect(response.status).toBe(400);
      });
    });

    describe('POST /configs', () => {
      it('should create a new config', async () => {
        const newConfig = {
          entity_type: entityType,
          language_code: languageCode,
          key: 'test-config',
          label: 'Test Config',
          placeholder: 'Enter test content...',
          icon: 'test',
          sort_order: 10,
          is_active: true,
        };

        const response = await client.api['rich-text'].configs.$post({
          json: newConfig,
        });

        expect(response.status).toBe(201);

        const data = await response.json();
        expect(data).toHaveProperty('config');
        expect(data.config).toMatchObject(newConfig);
      });

      it('should return 400 for invalid config data', async () => {
        const invalidConfig = {
          entity_type: 'invalid',
          language_code: languageCode,
          key: '',
          label: '',
        };

        const response = await client.api['rich-text'].configs.$post({
          json: invalidConfig,
        });

        expect(response.status).toBe(400);
      });

      it('should return 409 for duplicate config key', async () => {
        const config = {
          entity_type: entityType,
          language_code: languageCode,
          key: 'duplicate-key',
          label: 'Duplicate Config',
          placeholder: 'Enter content...',
          icon: 'test',
          sort_order: 10,
          is_active: true,
        };

        // First creation should succeed
        const firstResponse = await client.api['rich-text'].configs.$post({
          json: config,
        });
        expect(firstResponse.status).toBe(201);

        // Second creation should fail
        const secondResponse = await client.api['rich-text'].configs.$post({
          json: config,
        });
        expect(secondResponse.status).toBe(409);
      });
    });

    describe('PUT /configs/:id', () => {
      it('should update an existing config', async () => {
        const configId = 'test-config-id';
        const updateData = {
          label: 'Updated Label',
          is_active: false,
        };

        const response = await client.api['rich-text'].configs[':id'].$put({
          param: { id: configId },
          json: updateData,
        });

        expect(response.status).toBe(200);

        const data = await response.json();
        expect(data).toHaveProperty('config');
        expect(data.config.label).toBe(updateData.label);
        expect(data.config.is_active).toBe(updateData.is_active);
      });

      it('should return 404 for non-existent config', async () => {
        const response = await client.api['rich-text'].configs[':id'].$put({
          param: { id: 'non-existent' },
          json: { label: 'Updated' },
        });

        expect(response.status).toBe(404);
      });
    });

    describe('DELETE /configs/:id', () => {
      it('should soft delete a config', async () => {
        const configId = 'test-config-id';

        const response = await client.api['rich-text'].configs[':id'].$delete({
          param: { id: configId },
        });

        expect(response.status).toBe(200);

        const data = await response.json();
        expect(data).toHaveProperty('success', true);
      });

      it('should return 404 for non-existent config', async () => {
        const response = await client.api['rich-text'].configs[':id'].$delete({
          param: { id: 'non-existent' },
        });

        expect(response.status).toBe(404);
      });

      it('should return 403 for preset config deletion', async () => {
        const presetConfigId = 'preset-config-id';

        const response = await client.api['rich-text'].configs[':id'].$delete({
          param: { id: presetConfigId },
        });

        expect(response.status).toBe(403);
      });
    });
  });

  describe('Content Management API', () => {
    const entityType: EntityType = 'event';
    const entityId = 'test-event-123';
    const languageCode: LanguageCode = 'en';

    describe('GET /contents/entity/:entityType/:entityId', () => {
      it('should return entity tabs with content', async () => {
        const response = await client.api['rich-text'].contents.entity[
          ':entityType'
        ][':entityId'].$get({
          param: {
            entityType,
            entityId,
          },
          query: {
            language_code: languageCode,
          },
        });

        expect(response.status).toBe(200);

        const data = await response.json();
        expect(data).toHaveProperty('entity_type', entityType);
        expect(data).toHaveProperty('entity_id', entityId);
        expect(data).toHaveProperty('language_code', languageCode);
        expect(data).toHaveProperty('tabs');
        expect(Array.isArray(data.tabs)).toBe(true);
      });

      it('should return 400 for invalid entity type', async () => {
        const response = await client.api['rich-text'].contents.entity[
          ':entityType'
        ][':entityId'].$get({
          param: {
            entityType: 'invalid' as EntityType,
            entityId,
          },
          query: {
            language_code: languageCode,
          },
        });

        expect(response.status).toBe(400);
      });
    });

    describe('PUT /contents/entity/:entityType/:entityId/:contentType', () => {
      it('should upsert content', async () => {
        const contentType = 'introduction';
        const content = {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: 'Test content',
                },
              ],
            },
          ],
        };

        const response = await client.api['rich-text'].contents.entity[
          ':entityType'
        ][':entityId'][':contentType'].$put({
          param: {
            entityType,
            entityId,
            contentType,
          },
          query: {
            language_code: languageCode,
          },
          json: { content: JSON.stringify(content) },
        });

        expect(response.status).toBe(200);

        const data = await response.json();
        expect(data).toHaveProperty('content');
        expect(data.content.entity_type).toBe(entityType);
        expect(data.content.entity_id).toBe(entityId);
        expect(data.content.content_type).toBe(contentType);
        expect(data.content.language_code).toBe(languageCode);
      });

      it('should return 400 for invalid content', async () => {
        const response = await client.api['rich-text'].contents.entity[
          ':entityType'
        ][':entityId'][':contentType'].$put({
          param: {
            entityType,
            entityId,
            contentType: 'test',
          },
          query: {
            language_code: languageCode,
          },
          json: { content: 'invalid json' },
        });

        expect(response.status).toBe(400);
      });
    });

    describe('POST /contents/entity/:entityType/:entityId/batch', () => {
      it('should batch upsert multiple contents', async () => {
        const contents = {
          introduction: JSON.stringify({
            type: 'doc',
            content: [
              {
                type: 'paragraph',
                content: [{ type: 'text', text: 'Introduction' }],
              },
            ],
          }),
          details: JSON.stringify({
            type: 'doc',
            content: [
              {
                type: 'paragraph',
                content: [{ type: 'text', text: 'Details' }],
              },
            ],
          }),
        };

        const response = await client.api['rich-text'].contents.entity[
          ':entityType'
        ][':entityId'].batch.$post({
          param: {
            entityType,
            entityId,
          },
          query: {
            language_code: languageCode,
          },
          json: { contents },
        });

        expect(response.status).toBe(200);

        const data = await response.json();
        expect(data).toHaveProperty('contents');
        expect(Array.isArray(data.contents)).toBe(true);
        expect(data.contents).toHaveLength(2);
      });

      it('should return 400 for empty contents', async () => {
        const response = await client.api['rich-text'].contents.entity[
          ':entityType'
        ][':entityId'].batch.$post({
          param: {
            entityType,
            entityId,
          },
          query: {
            language_code: languageCode,
          },
          json: { contents: {} },
        });

        expect(response.status).toBe(400);
      });
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for non-existent endpoints', async () => {
      const response = await client.api['rich-text']['non-existent'].$get();
      expect(response.status).toBe(404);
    });

    it('should return 405 for unsupported methods', async () => {
      const response = await client.api['rich-text'].configs.$patch({
        json: {},
      });
      expect(response.status).toBe(405);
    });

    it('should handle database connection errors gracefully', async () => {
      // Mock database error
      const errorApp = new Hono();
      errorApp.use('*', async (c, next) => {
        c.env = { DB: null }; // Simulate missing database
        await next();
      });
      errorApp.route('/api/rich-text', richTextRoutes);

      const errorClient = testClient(errorApp);

      const response = await errorClient.api['rich-text'].configs.$get({
        query: {
          entity_type: 'event',
          language_code: 'en',
        },
      });

      expect(response.status).toBe(500);
    });
  });

  describe('Performance Tests', () => {
    it('should handle concurrent requests', async () => {
      const promises = Array.from({ length: 10 }, (_, i) =>
        client.api['rich-text'].configs.$get({
          query: {
            entity_type: 'event',
            language_code: 'en',
          },
        })
      );

      const responses = await Promise.all(promises);

      responses.forEach((response) => {
        expect(response.status).toBe(200);
      });
    });

    it('should handle large content payloads', async () => {
      const largeContent = {
        type: 'doc',
        content: Array.from({ length: 1000 }, (_, i) => ({
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: `Large content paragraph ${i}. `.repeat(100),
            },
          ],
        })),
      };

      const response = await client.api['rich-text'].contents.entity[
        ':entityType'
      ][':entityId'][':contentType'].$put({
        param: {
          entityType: 'event',
          entityId: 'large-content-test',
          contentType: 'large-content',
        },
        query: {
          language_code: 'en',
        },
        json: { content: JSON.stringify(largeContent) },
      });

      expect(response.status).toBe(200);
    });
  });
});
