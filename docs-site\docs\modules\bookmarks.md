# 收藏模块 (Bookmarks Module)

## 概述

收藏模块提供用户收藏社团的功能，支持收藏管理、收藏列表查询、收藏状态检查和个性化排序。

## 功能特性

- ✅ 社团收藏和取消收藏（切换模式）
- ✅ 用户收藏列表查询
- ✅ 收藏状态检查
- ✅ 收藏统计信息
- ✅ 个性化社团列表排序
- ✅ 批量收藏状态查询
- ✅ 分页支持
- ✅ 用户权限控制
- ✅ 重复收藏防护
- ✅ 高性能缓存策略

## API端点

### 收藏管理

#### POST /circles/\{circleId\}/bookmark

**功能**: 切换指定社团的收藏状态（收藏/取消收藏）

**路径参数**:

- `circleId` (必需) - 社团ID

**请求头**:

- `Authorization: Bearer {token}` (必需)

**响应格式**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "isBookmarked": true
  }
}
```

#### GET /circles/\{circleId\}/bookmark/status

**功能**: 查询指定社团的收藏状态

**路径参数**:

- `circleId` (必需) - 社团ID

**请求头**:

- `Authorization: Bearer {token}` (必需)

**响应格式**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "isBookmarked": true,
    "bookmarkId": "bookmark-123",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### 收藏列表

#### GET /user/bookmarks

**功能**: 获取用户收藏的社团列表

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `20`
- `cursor` (可选) - 游标分页标识

**请求头**:

- `Authorization: Bearer {token}` (必需)

**响应格式**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": "circle-789",
        "name": "某某工作室",
        "category": "original",
        "bookmarkedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 15,
    "hasMore": true,
    "nextCursor": "cursor-token"
  }
}
```

### 收藏统计

#### GET /user/bookmarks/stats

**功能**: 获取用户收藏统计信息

**查询参数**:

- `includeIds` (可选) - 是否包含收藏社团ID列表，默认 `false`

**请求头**:

- `Authorization: Bearer {token}` (必需)

**响应格式**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalBookmarks": 42,
    "bookmarkedCircleIds": ["circle-123", "circle-456"]
  }
}
```

### 个性化社团列表

#### GET /circles

**功能**: 获取社团列表，支持个性化排序

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `50`
- `search` (可选) - 搜索关键词

**请求头**:

- `Authorization: Bearer {token}` (可选，影响排序)

**个性化特性**:

- 已登录用户：已收藏社团优先显示
- 未登录用户：按默认排序（名称）

**响应格式**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": "circle-123",
        "name": "示例社团",
        "isBookmarked": true
      }
    ],
    "total": 100
  }
}
```

## 数据结构

### Bookmark Schema

| 字段       | 类型   | 说明             | 必需 |
| ---------- | ------ | ---------------- | ---- |
| id         | string | 收藏记录唯一标识 | ✅   |
| user_id    | string | 用户ID           | ✅   |
| circle_id  | string | 社团ID           | ✅   |
| created_at | string | 收藏时间         | ✅   |

### 约束条件

- **唯一性约束**: 同一用户不能重复收藏同一社团
- **外键约束**: user_id和circle_id必须存在于对应的表中
- **级联删除**: 用户或社团删除时，相关收藏记录自动删除

## 使用示例

### JavaScript/TypeScript

```typescript
// 收藏社团
const bookmarkResponse = await fetch('/api/circles/circle-123/bookmark', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer YOUR_JWT_TOKEN',
  },
});
const bookmarkData = await bookmarkResponse.json();

// 取消收藏
const unbookmarkResponse = await fetch('/api/circles/circle-123/bookmark', {
  method: 'DELETE',
  headers: {
    Authorization: 'Bearer YOUR_JWT_TOKEN',
  },
});

// 检查收藏状态
const statusResponse = await fetch('/api/circles/circle-123/bookmark', {
  headers: {
    Authorization: 'Bearer YOUR_JWT_TOKEN',
  },
});
const statusData = await statusResponse.json();

// 获取收藏列表
const bookmarksResponse = await fetch(
  '/api/circles/bookmarks?page=1&pageSize=20',
  {
    headers: {
      Authorization: 'Bearer YOUR_JWT_TOKEN',
    },
  }
);
const bookmarksData = await bookmarksResponse.json();

// 使用openapi-typescript-fetch
import { createClient } from '@/lib/api/client';
const api = createClient();

// 设置认证令牌
api.configure({
  headers: {
    Authorization: 'Bearer YOUR_JWT_TOKEN',
  },
});

// 收藏社团
const { data: bookmark } = await api.POST('/circles/\{id\}/bookmark', {
  params: { path: { id: 'circle-123' } },
});

// 取消收藏
await api.DELETE('/circles/\{id\}/bookmark', {
  params: { path: { id: 'circle-123' } },
});

// 获取收藏列表
const { data: bookmarks } = await api.GET('/circles/bookmarks', {
  params: {
    query: { page: '1', pageSize: '20' },
  },
});
```

### cURL

```bash
# 收藏社团
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.example.com/circles/circle-123/bookmark"

# 取消收藏
curl -X DELETE \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.example.com/circles/circle-123/bookmark"

# 检查收藏状态
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.example.com/circles/circle-123/bookmark"

# 获取收藏列表
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.example.com/circles/bookmarks?page=1&pageSize=20"
```

### React Hook示例

```typescript
// 自定义Hook：管理收藏状态
import { useState, useEffect } from 'react';
import { createClient } from '@/lib/api/client';

export function useBookmark(circleId: string, token: string) {
  const [bookmarked, setBookmarked] = useState(false);
  const [loading, setLoading] = useState(false);
  const api = createClient();

  // 配置认证
  api.configure({
    headers: { Authorization: `Bearer ${token}` },
  });

  // 检查收藏状态
  useEffect(() => {
    async function checkStatus() {
      try {
        const { data } = await api.GET('/circles/\{id\}/bookmark', {
          params: { path: { id: circleId } },
        });
        setBookmarked(data.data.bookmarked);
      } catch (error) {
        setBookmarked(false);
      }
    }
    checkStatus();
  }, [circleId]);

  // 切换收藏状态
  const toggleBookmark = async () => {
    setLoading(true);
    try {
      if (bookmarked) {
        await api.DELETE('/circles/\{id\}/bookmark', {
          params: { path: { id: circleId } },
        });
        setBookmarked(false);
      } else {
        await api.POST('/circles/\{id\}/bookmark', {
          params: { path: { id: circleId } },
        });
        setBookmarked(true);
      }
    } catch (error) {
      console.error('Toggle bookmark failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return { bookmarked, loading, toggleBookmark };
}
```

## 权限控制

### 认证要求

所有收藏相关操作都需要用户认证：

- 必须提供有效的JWT令牌
- 令牌必须未过期
- 用户必须存在且状态正常

### 权限检查

- **收藏操作**: 用户只能管理自己的收藏
- **收藏列表**: 用户只能查看自己的收藏列表
- **收藏状态**: 用户只能查看自己对特定社团的收藏状态

## 性能优化

### 数据库优化

- `user_id` 和 `circle_id` 建立复合索引
- `created_at` 字段建立索引用于排序
- 使用唯一约束防止重复收藏

### 缓存策略

- 收藏列表缓存5分钟
- 收藏状态缓存10分钟
- 收藏操作后立即清理相关缓存

### 查询优化

- 收藏列表使用JOIN查询获取社团信息
- 分页查询使用LIMIT和OFFSET
- 避免N+1查询问题

## 错误处理

### 常见错误

**未认证**:

```json
{
  "code": 10004,
  "message": "令牌无效或已过期",
  "data": {}
}
```

**社团不存在**:

```json
{
  "code": 10002,
  "message": "资源不存在",
  "data": {}
}
```

**重复收藏**:

```json
{
  "code": 10003,
  "message": "唯一键冲突",
  "data": {}
}
```

**收藏不存在**:

```json
{
  "code": 10002,
  "message": "收藏记录不存在",
  "data": {}
}
```

## 限制和注意事项

1. **认证要求**: 所有操作都需要用户认证
2. **唯一性**: 同一用户不能重复收藏同一社团
3. **级联删除**: 用户或社团删除时，收藏记录会自动删除
4. **分页限制**: 收藏列表单页最大返回100条记录

## 未来规划

- 🔄 支持收藏分组和标签
- 🔄 添加收藏导出功能
- 🔄 实现收藏分享功能
- 🔄 支持收藏统计和分析
- 🔄 添加收藏推荐功能

## 相关文档

- [API规范](../api/request-spec.md)
- [社团模块](./circles.md)
- [用户模块](./users.md)
- [i18n指南](../development/i18n.md)
