import type { KVNamespace } from '@cloudflare/workers-types';

import type {
  BookmarkListResponse,
  BookmarkStatusResponse,
  BookmarkStatsResponse,
} from './schema';

/**
 * 缓存键生成器
 */
export const CACHE_KEYS = {
  USER_BOOKMARKS: (userId: string, queryHash: string) =>
    `bookmarks:user:${userId}:${queryHash}`,
  BOOKMARK_STATUS: (userId: string, circleId: string) =>
    `bookmark:${userId}:${circleId}`,
  BOOKMARK_COUNT: (userId: string) => `bookmark:count:${userId}`,
  CIRCLE_BOOKMARK_COUNT: (circleId: string) => `circle:bookmarks:${circleId}`,
  USER_BOOKMARK_STATS: (userId: string) => `bookmark:stats:${userId}`,
};

/**
 * 缓存过期时间（秒）
 */
export const CACHE_TTL = {
  USER_BOOKMARKS: 300, // 5分钟
  BOOKMARK_STATUS: 600, // 10分钟
  BOOKMARK_COUNT: 300, // 5分钟
  CIRCLE_BOOKMARK_COUNT: 1800, // 30分钟
  USER_BOOKMARK_STATS: 300, // 5分钟
};

/**
 * 简单的字符串哈希函数
 */
function simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(36).slice(0, 16);
}

/**
 * 查询参数哈希生成器
 */
function generateQueryHash(query: Record<string, any>): string {
  // 排除分页参数，因为不同页面应该有不同的缓存
  const { page: _page, cursor: _cursor, ...cacheableQuery } = query;
  const sortedKeys = Object.keys(cacheableQuery).sort();
  const hashString = sortedKeys
    .map((key) => `${key}:${cacheableQuery[key]}`)
    .join('|');
  return simpleHash(hashString);
}

/**
 * 收藏缓存服务
 */
export class BookmarkCache {
  constructor(private kv: KVNamespace) {}

  /**
   * 获取用户收藏列表缓存
   */
  async getUserBookmarks(
    userId: string,
    query: Record<string, any>
  ): Promise<BookmarkListResponse | null> {
    try {
      const queryHash = generateQueryHash(query);
      const key = CACHE_KEYS.USER_BOOKMARKS(userId, queryHash);
      const cached = await this.kv.get(key);

      if (cached) {
        return JSON.parse(cached) as BookmarkListResponse;
      }
      return null;
    } catch (error) {
      console.warn('获取收藏列表缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置用户收藏列表缓存
   */
  async setUserBookmarks(
    userId: string,
    query: Record<string, any>,
    data: BookmarkListResponse
  ): Promise<void> {
    try {
      const queryHash = generateQueryHash(query);
      const key = CACHE_KEYS.USER_BOOKMARKS(userId, queryHash);

      await this.kv.put(key, JSON.stringify(data), {
        expirationTtl: CACHE_TTL.USER_BOOKMARKS,
      });
    } catch (error) {
      console.warn('设置收藏列表缓存失败:', error);
    }
  }

  /**
   * 获取收藏状态缓存
   */
  async getBookmarkStatus(
    userId: string,
    circleId: string
  ): Promise<BookmarkStatusResponse | null> {
    try {
      const key = CACHE_KEYS.BOOKMARK_STATUS(userId, circleId);
      const cached = await this.kv.get(key);

      if (cached) {
        return JSON.parse(cached) as BookmarkStatusResponse;
      }
      return null;
    } catch (error) {
      console.warn('获取收藏状态缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置收藏状态缓存
   */
  async setBookmarkStatus(
    userId: string,
    circleId: string,
    data: BookmarkStatusResponse
  ): Promise<void> {
    try {
      const key = CACHE_KEYS.BOOKMARK_STATUS(userId, circleId);

      await this.kv.put(key, JSON.stringify(data), {
        expirationTtl: CACHE_TTL.BOOKMARK_STATUS,
      });
    } catch (error) {
      console.warn('设置收藏状态缓存失败:', error);
    }
  }

  /**
   * 获取收藏统计缓存
   */
  async getBookmarkStats(
    userId: string
  ): Promise<BookmarkStatsResponse | null> {
    try {
      const key = CACHE_KEYS.USER_BOOKMARK_STATS(userId);
      const cached = await this.kv.get(key);

      if (cached) {
        return JSON.parse(cached) as BookmarkStatsResponse;
      }
      return null;
    } catch (error) {
      console.warn('获取收藏统计缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置收藏统计缓存
   */
  async setBookmarkStats(
    userId: string,
    data: BookmarkStatsResponse
  ): Promise<void> {
    try {
      const key = CACHE_KEYS.USER_BOOKMARK_STATS(userId);

      await this.kv.put(key, JSON.stringify(data), {
        expirationTtl: CACHE_TTL.USER_BOOKMARK_STATS,
      });
    } catch (error) {
      console.warn('设置收藏统计缓存失败:', error);
    }
  }

  /**
   * 清除用户相关的所有缓存
   */
  async invalidateUserCache(userId: string): Promise<void> {
    try {
      // 由于 KV 不支持按前缀批量删除，我们只能删除已知的缓存键
      // 在实际应用中，可能需要维护一个缓存键列表或使用其他策略

      const keysToDelete = [
        CACHE_KEYS.BOOKMARK_COUNT(userId),
        CACHE_KEYS.USER_BOOKMARK_STATS(userId),
      ];

      // 删除收藏状态缓存需要知道具体的 circleId，这里暂时跳过
      // 在实际应用中，可以在设置缓存时同时维护一个用户的缓存键列表

      await Promise.all(
        keysToDelete.map((key) => this.kv.delete(key).catch(console.warn))
      );
    } catch (error) {
      console.warn('清除用户缓存失败:', error);
    }
  }

  /**
   * 清除特定收藏的缓存
   */
  async invalidateBookmarkCache(
    userId: string,
    circleId: string
  ): Promise<void> {
    try {
      const keysToDelete = [
        CACHE_KEYS.BOOKMARK_STATUS(userId, circleId),
        CACHE_KEYS.CIRCLE_BOOKMARK_COUNT(circleId),
      ];

      await Promise.all(
        keysToDelete.map((key) => this.kv.delete(key).catch(console.warn))
      );

      // 同时清除用户相关的缓存
      await this.invalidateUserCache(userId);
    } catch (error) {
      console.warn('清除收藏缓存失败:', error);
    }
  }
}

/**
 * 创建缓存实例的工厂函数
 */
export function createBookmarkCache(kv: KVNamespace): BookmarkCache {
  return new BookmarkCache(kv);
}
