import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ContentService } from '../service';
import { createContentRepository } from '../repository';
import type { D1Database } from '@cloudflare/workers-types';

// Mock repository
vi.mock('../repository', () => ({
  createContentRepository: vi.fn(),
}));

describe('Rich Text Service Unit Tests', () => {
  let contentService: ContentService;
  let mockRepository: any;
  let mockDB: D1Database;

  beforeEach(() => {
    vi.clearAllMocks();

    mockDB = {} as D1Database;

    mockRepository = {
      findByEntity: vi.fn(),
      findByEntityAndType: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      upsert: vi.fn(),
      delete: vi.fn(),
      deleteByEntity: vi.fn(),
    };

    (createContentRepository as any).mockReturnValue(mockRepository);
    contentService = new ContentService(mockDB);
  });

  describe('getEntityContent', () => {
    it('should return formatted content response', async () => {
      const mockContents = [
        {
          id: '1',
          entity_type: 'event',
          entity_id: 'test-event',
          content_type: 'introduction',
          content: '<p>介绍</p>',
        },
        {
          id: '2',
          entity_type: 'event',
          entity_id: 'test-event',
          content_type: 'highlights',
          content: '<p>亮点</p>',
        },
      ];

      mockRepository.findByEntity.mockResolvedValue(mockContents);

      const result = await contentService.getEntityContent(
        'event',
        'test-event'
      );

      expect(result).toEqual({
        introduction: '<p>介绍</p>',
        highlights: '<p>亮点</p>',
      });
      expect(mockRepository.findByEntity).toHaveBeenCalledWith(
        'event',
        'test-event'
      );
    });

    it('should return empty object when no content found', async () => {
      mockRepository.findByEntity.mockResolvedValue([]);

      const result = await contentService.getEntityContent(
        'event',
        'test-event'
      );

      expect(result).toEqual({});
    });
  });

  describe('getEntityContentByType', () => {
    it('should return content when found', async () => {
      const mockContent = {
        id: '1',
        entity_type: 'event',
        entity_id: 'test-event',
        content_type: 'introduction',
        content: '<p>介绍</p>',
      };

      mockRepository.findByEntityAndType.mockResolvedValue(mockContent);

      const result = await contentService.getEntityContentByType(
        'event',
        'test-event',
        'introduction'
      );

      expect(result).toBe('<p>介绍</p>');
      expect(mockRepository.findByEntityAndType).toHaveBeenCalledWith(
        'event',
        'test-event',
        'introduction'
      );
    });

    it('should return null when content not found', async () => {
      mockRepository.findByEntityAndType.mockResolvedValue(null);

      const result = await contentService.getEntityContentByType(
        'event',
        'test-event',
        'introduction'
      );

      expect(result).toBeNull();
    });
  });

  describe('createContent', () => {
    it('should sanitize HTML content before creating', async () => {
      const mockData = {
        entity_type: 'event' as const,
        entity_id: 'test-event',
        content_type: 'introduction' as const,
        content: '<p>测试内容</p><script>alert("xss")</script>',
      };

      const mockCreatedContent = {
        id: '1',
        ...mockData,
        content: '<p>测试内容</p>', // Script tag removed
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockRepository.create.mockResolvedValue(mockCreatedContent);

      const result = await contentService.createContent(mockData);

      expect(mockRepository.create).toHaveBeenCalledWith({
        ...mockData,
        content: '<p>测试内容</p>',
      });
      expect(result).toEqual(mockCreatedContent);
    });
  });

  describe('updateContent', () => {
    it('should sanitize HTML content before updating', async () => {
      const contentId = 'content-1';
      const updateData = {
        content: '<p>更新内容</p><script>alert("xss")</script>',
      };

      mockRepository.update.mockResolvedValue(undefined);

      await contentService.updateContent(contentId, updateData);

      expect(mockRepository.update).toHaveBeenCalledWith(contentId, {
        content: '<p>更新内容</p>',
      });
    });
  });

  describe('upsertContent', () => {
    it('should sanitize HTML content before upserting', async () => {
      const mockUpsertedContent = {
        id: '1',
        entity_type: 'event',
        entity_id: 'test-event',
        content_type: 'introduction',
        content: '<p>内容</p>',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockRepository.upsert.mockResolvedValue(mockUpsertedContent);

      const result = await contentService.upsertContent(
        'event',
        'test-event',
        'introduction',
        '<p>内容</p><script>alert("xss")</script>'
      );

      expect(mockRepository.upsert).toHaveBeenCalledWith(
        'event',
        'test-event',
        'introduction',
        '<p>内容</p>'
      );
      expect(result).toEqual(mockUpsertedContent);
    });
  });

  describe('batchUpdateContent', () => {
    it('should update multiple content types', async () => {
      const batchData = {
        introduction: '<p>介绍</p>',
        highlights: '<p>亮点</p>',
        guide: '<p>指南</p>',
      };

      const mockResults = [
        { id: '1', content: '<p>介绍</p>' },
        { id: '2', content: '<p>亮点</p>' },
        { id: '3', content: '<p>指南</p>' },
      ];

      mockRepository.upsert
        .mockResolvedValueOnce(mockResults[0])
        .mockResolvedValueOnce(mockResults[1])
        .mockResolvedValueOnce(mockResults[2]);

      const result = await contentService.batchUpdateContent(
        'event',
        'test-event',
        batchData
      );

      expect(mockRepository.upsert).toHaveBeenCalledTimes(3);
      expect(result).toEqual({
        introduction: '<p>介绍</p>',
        highlights: '<p>亮点</p>',
        guide: '<p>指南</p>',
      });
    });

    it('should skip empty content', async () => {
      const batchData = {
        introduction: '<p>介绍</p>',
        highlights: '',
        guide: '   ',
        notices: '<p>公告</p>',
      };

      mockRepository.upsert
        .mockResolvedValueOnce({ id: '1', content: '<p>介绍</p>' })
        .mockResolvedValueOnce({ id: '2', content: '<p>公告</p>' });

      const result = await contentService.batchUpdateContent(
        'event',
        'test-event',
        batchData
      );

      expect(mockRepository.upsert).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        introduction: '<p>介绍</p>',
        notices: '<p>公告</p>',
      });
    });
  });

  describe('deleteContent', () => {
    it('should call repository delete method', async () => {
      const contentId = 'content-1';
      mockRepository.delete.mockResolvedValue(undefined);

      await contentService.deleteContent(contentId);

      expect(mockRepository.delete).toHaveBeenCalledWith(contentId);
    });
  });

  describe('deleteEntityContent', () => {
    it('should call repository deleteByEntity method', async () => {
      mockRepository.deleteByEntity.mockResolvedValue(undefined);

      await contentService.deleteEntityContent('event', 'test-event');

      expect(mockRepository.deleteByEntity).toHaveBeenCalledWith(
        'event',
        'test-event'
      );
    });
  });

  describe('getContentList', () => {
    it('should return content list from repository', async () => {
      const mockContents = [
        {
          id: '1',
          entity_type: 'event',
          entity_id: 'test-event',
          content_type: 'introduction',
          content: '<p>介绍</p>',
        },
      ];

      mockRepository.findByEntity.mockResolvedValue(mockContents);

      const result = await contentService.getContentList('event', 'test-event');

      expect(result).toEqual(mockContents);
      expect(mockRepository.findByEntity).toHaveBeenCalledWith(
        'event',
        'test-event'
      );
    });
  });
});
