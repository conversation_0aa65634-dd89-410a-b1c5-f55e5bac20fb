import type { D1Database } from '@cloudflare/workers-types';

import type { Event, EventUpdateInput } from './schema';
import {
  D1EventRepository,
  NewEventData,
} from '@/infrastructure/db/eventRepository';

/**
 * EventRepository 定义
 */
export interface EventRepository {
  findById(id: string): Promise<Event | null>;
  create(data: NewEventData): Promise<Event>;
  update(
    id: string,
    partial: EventUpdateInput & { date_sort?: number }
  ): Promise<void>;
  delete(id: string): Promise<void>;
}

/**
 * 工厂：根据运行时依赖返回具体仓库实现
 */
export function createEventRepository(db: D1Database): EventRepository {
  // 当前仅实现 D1 版本
  return new D1EventRepository(db);
}
