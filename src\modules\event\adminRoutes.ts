import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import type { Context } from 'hono';
import * as eventController from './controller';
import * as eventService from './service';
import { eventSchema, eventCreateRequest, eventUpdateRequest } from './schema';
import {
  successResponse,
  paginatedResult,
  errorResponse,
} from '@/utils/schemas';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

import { getDB } from '@/infrastructure';
import type { Cache, Logger } from '@/infrastructure';

const routes = new OpenAPIHono<HonoApp>();

const getEventsRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '展会列表（分页 & 搜索）',
  tags: ['Admin.Events'],
  request: {
    query: z.object({
      page: z.string().optional().openapi({ example: '1' }),
      pageSize: z.string().optional().openapi({ example: '50' }),
      keyword: z.string().optional().openapi({ example: '例大祭' }),
      date_from: z.string().optional().openapi({ example: '20250101' }),
      date_to: z.string().optional().openapi({ example: '20251231' }),
    }),
  },
  responses: {
    200: {
      description: '分页列表',
      content: { 'application/json': { schema: paginatedResult(eventSchema) } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

const postEventRoute = createRoute({
  method: 'post',
  path: '/',
  summary: '创建展会',
  tags: ['Admin.Events'],
  request: {
    body: { content: { 'application/json': { schema: eventCreateRequest } } },
  },
  responses: {
    201: {
      description: '展会创建成功',
      content: { 'application/json': { schema: successResponse } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
    422: { description: 'Validation Error' },
  },
});

const getEventRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '展会详情',
  tags: ['Admin.Events'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '展会详情',
      content: { 'application/json': { schema: eventSchema } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

const patchEventRoute = createRoute({
  method: 'patch',
  path: '/{id}',
  summary: '更新展会',
  tags: ['Admin.Events'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
    body: { content: { 'application/json': { schema: eventUpdateRequest } } },
  },
  responses: {
    200: {
      description: '展会已保存',
      content: { 'application/json': { schema: successResponse } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
    422: { description: 'Validation Error' },
  },
});

const deleteEventRoute = createRoute({
  method: 'delete',
  path: '/{id}',
  summary: '删除展会',
  tags: ['Admin.Events'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '展会已删除',
      content: {
        'application/json': { schema: successResponse.pick({ message: true }) },
      },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

async function listEventsAdminHandler(c: Context) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');
  const data = await eventService.listEventsAdmin(
    db,
    new URL(c.req.url).searchParams,
    cache,
    logger
  );
  return c.json(data);
}

async function getEventAdminHandler(c: Context) {
  const db = getDB(c);
  const event = await eventService.getEventAdmin(db, c.req.param('id'));
  if (!event) return c.json({ message: 'Not Found' }, 404);
  return c.json(event);
}

registerOpenApiRoute(routes, getEventsRoute, listEventsAdminHandler);
registerOpenApiRoute(routes, postEventRoute, eventController.createEvent);
registerOpenApiRoute(routes, getEventRoute, getEventAdminHandler);
registerOpenApiRoute(routes, patchEventRoute, eventController.updateEvent);
registerOpenApiRoute(routes, deleteEventRoute, eventController.deleteEvent);

export { routes };
