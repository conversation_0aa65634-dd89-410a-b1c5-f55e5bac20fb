import { z } from '@hono/zod-openapi';

// 日志实体 Schema（对应 logs 表）
export const logSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  user_id: z.string().openapi({ example: 'uuid-user' }),
  username: z.string().openapi({ example: 'alice' }),
  action: z.string().openapi({ example: 'CREATE_EVENT' }),
  target_type: z.string().nullable().openapi({ example: 'event' }),
  target_id: z.string().nullable().openapi({ example: 'uuid-target' }),
  meta: z.string().nullable().openapi({ example: '{"foo":"bar"}' }),
  created_at: z.string().openapi({ example: '2025-01-01T00:00:00Z' }),
});

export type Log = z.infer<typeof logSchema>;
