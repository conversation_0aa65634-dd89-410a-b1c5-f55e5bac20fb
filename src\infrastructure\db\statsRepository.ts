// D1Database 类型现在在全局作用域中可用

import type {
  StatsRepository,
  StatsResult,
  StatsTotals,
  EventsByMonth,
} from '../../modules/stats/repository';

export class D1StatsRepository implements StatsRepository {
  constructor(private readonly db: D1Database) {}

  async getStats(year?: number): Promise<StatsResult> {
    // 1. totals
    const [circleRow, artistRow, eventRow] = await Promise.all([
      this.db
        .prepare('SELECT COUNT(*) AS total FROM circles')
        .first<{ total: number }>(),
      this.db
        .prepare('SELECT COUNT(*) AS total FROM artists')
        .first<{ total: number }>(),
      this.db
        .prepare('SELECT COUNT(*) AS total FROM events')
        .first<{ total: number }>(),
    ]);

    const totals: StatsTotals = {
      circles: circleRow?.total ?? 0,
      artists: artistRow?.total ?? 0,
      events: eventRow?.total ?? 0,
    };

    // 2. events by month
    const targetYear = year ?? new Date().getUTCFullYear();
    const { results } = await this.db
      .prepare(
        `SELECT strftime('%m', created_at) AS month, COUNT(*) AS count
         FROM events
         WHERE strftime('%Y', created_at) = ?
         GROUP BY month
         ORDER BY month ASC`
      )
      .bind(targetYear.toString())
      .all<{ month: string; count: number }>();

    const eventsByMonth: EventsByMonth[] = Array.from(
      { length: 12 },
      (_, i) => {
        const m = (i + 1).toString().padStart(2, '0');
        const row = results.find((r) => r.month === m);
        return { month: m, count: row ? Number(row.count) : 0 };
      }
    );

    return { totals, year: targetYear, eventsByMonth };
  }
}
