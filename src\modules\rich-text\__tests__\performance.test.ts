import { describe, it, expect, beforeEach, vi } from 'vitest';
import { performance } from 'perf_hooks';
import { RichTextTabsService } from '../service-new';
import { RichTextTabsCache } from '../cache';
import type {
  ContentTypeConfigRepository,
  RichTextContentRepository,
} from '../repository-new';
import type { EntityType, LanguageCode, ContentTypeConfig } from '../schema';

// Performance test utilities
class PerformanceTracker {
  private measurements: Map<string, number[]> = new Map();

  start(label: string): () => number {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      if (!this.measurements.has(label)) {
        this.measurements.set(label, []);
      }
      this.measurements.get(label)!.push(duration);

      return duration;
    };
  }

  getStats(label: string) {
    const measurements = this.measurements.get(label) || [];
    if (measurements.length === 0) {
      return null;
    }

    const sorted = [...measurements].sort((a, b) => a - b);
    const sum = measurements.reduce((a, b) => a + b, 0);

    return {
      count: measurements.length,
      min: Math.min(...measurements),
      max: Math.max(...measurements),
      avg: sum / measurements.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
    };
  }

  reset() {
    this.measurements.clear();
  }
}

// Mock repositories with performance simulation
const createMockConfigRepo = (
  latencyMs: number = 0
): ContentTypeConfigRepository => ({
  create: vi.fn().mockImplementation(async (data) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return {
      id: 'config-1',
      ...data,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }),
  update: vi.fn().mockImplementation(async (id, data) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return { id, ...data, updated_at: new Date().toISOString() };
  }),
  delete: vi.fn().mockImplementation(async (id, deletedBy) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return { id, deleted_at: new Date().toISOString(), deleted_by: deletedBy };
  }),
  restore: vi.fn().mockImplementation(async (id) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return { id, deleted_at: null, deleted_by: null };
  }),
  findById: vi.fn().mockImplementation(async (id) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return { id, entity_type: 'event', language_code: 'en' };
  }),
  getActiveConfigs: vi
    .fn()
    .mockImplementation(async (entityType, languageCode) => {
      await new Promise((resolve) => setTimeout(resolve, latencyMs));
      return Array.from({ length: 10 }, (_, i) => ({
        id: `config-${i}`,
        entity_type: entityType,
        language_code: languageCode,
        key: `key-${i}`,
        label: `Label ${i}`,
        sort_order: i,
        is_active: true,
        is_preset: i < 3,
      }));
    }),
  getAllConfigs: vi
    .fn()
    .mockImplementation(async (entityType, languageCode) => {
      await new Promise((resolve) => setTimeout(resolve, latencyMs));
      return Array.from({ length: 15 }, (_, i) => ({
        id: `config-${i}`,
        entity_type: entityType,
        language_code: languageCode,
        key: `key-${i}`,
        label: `Label ${i}`,
        sort_order: i,
        is_active: i < 10,
        is_preset: i < 3,
        deleted_at: i >= 10 ? new Date().toISOString() : null,
      }));
    }),
  reorder: vi.fn().mockImplementation(async (updates) => {
    await new Promise((resolve) =>
      setTimeout(resolve, latencyMs * updates.length)
    );
    return updates.map((u) => ({ ...u, updated_at: new Date().toISOString() }));
  }),
  batchUpdateStatus: vi.fn().mockImplementation(async (ids, isActive) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs * ids.length));
    return ids.map((id) => ({
      id,
      is_active: isActive,
      updated_at: new Date().toISOString(),
    }));
  }),
});

const createMockContentRepo = (
  latencyMs: number = 0
): RichTextContentRepository => ({
  create: vi.fn().mockImplementation(async (data) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return {
      id: 'content-1',
      ...data,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }),
  update: vi.fn().mockImplementation(async (id, data) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return { id, ...data, updated_at: new Date().toISOString() };
  }),
  upsert: vi.fn().mockImplementation(async (data) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return {
      id: 'content-1',
      ...data,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }),
  delete: vi.fn().mockImplementation(async (id) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return true;
  }),
  findById: vi.fn().mockImplementation(async (id) => {
    await new Promise((resolve) => setTimeout(resolve, latencyMs));
    return { id, content: '{}' };
  }),
  getByEntity: vi
    .fn()
    .mockImplementation(async (entityType, entityId, languageCode) => {
      await new Promise((resolve) => setTimeout(resolve, latencyMs));
      return Array.from({ length: 5 }, (_, i) => ({
        id: `content-${i}`,
        entity_type: entityType,
        entity_id: entityId,
        language_code: languageCode,
        content_type: `type-${i}`,
        content: `{"type":"doc","content":[]}`,
      }));
    }),
  batchUpsert: vi.fn().mockImplementation(async (contents) => {
    await new Promise((resolve) =>
      setTimeout(resolve, latencyMs * contents.length)
    );
    return contents.map((content, i) => ({
      id: `content-${i}`,
      ...content,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }));
  }),
});

describe('Rich Text Tabs Performance Tests', () => {
  let tracker: PerformanceTracker;
  let mockConfigRepo: ContentTypeConfigRepository;
  let mockContentRepo: RichTextContentRepository;
  let mockCache: RichTextTabsCache;
  let service: RichTextTabsService;

  beforeEach(() => {
    tracker = new PerformanceTracker();
    mockConfigRepo = createMockConfigRepo(5); // 5ms simulated latency
    mockContentRepo = createMockContentRepo(5);

    // Mock cache with instant responses
    mockCache = {
      getActiveConfigs: vi.fn().mockResolvedValue(null),
      setActiveConfigs: vi.fn().mockResolvedValue(undefined),
      getAllConfigs: vi.fn().mockResolvedValue(null),
      setAllConfigs: vi.fn().mockResolvedValue(undefined),
      getEntityTabs: vi.fn().mockResolvedValue(null),
      setEntityTabs: vi.fn().mockResolvedValue(undefined),
      invalidateConfigCache: vi.fn().mockResolvedValue(undefined),
      invalidateContentCache: vi.fn().mockResolvedValue(undefined),
    } as unknown as RichTextTabsCache;

    service = new RichTextTabsService(
      mockConfigRepo,
      mockContentRepo,
      mockCache
    );
  });

  describe('Single Operation Performance', () => {
    it('should get active configs within acceptable time', async () => {
      const entityType: EntityType = 'event';
      const languageCode: LanguageCode = 'en';

      const end = tracker.start('getActiveConfigs');
      await service.getActiveConfigs(entityType, languageCode);
      const duration = end();

      expect(duration).toBeLessThan(100); // Should complete within 100ms
    });

    it('should create config within acceptable time', async () => {
      const configData = {
        entity_type: 'event' as EntityType,
        language_code: 'en' as LanguageCode,
        key: 'test-key',
        label: 'Test Label',
        placeholder: 'Test placeholder',
        icon: 'test',
        sort_order: 1,
        is_active: true,
      };

      const end = tracker.start('createConfig');
      await service.createConfig(configData);
      const duration = end();

      expect(duration).toBeLessThan(50); // Should complete within 50ms
    });

    it('should get entity tabs within acceptable time', async () => {
      const entityType: EntityType = 'event';
      const entityId = 'test-event';
      const languageCode: LanguageCode = 'en';

      const end = tracker.start('getEntityTabs');
      await service.getEntityTabs(entityType, entityId, languageCode);
      const duration = end();

      expect(duration).toBeLessThan(150); // Should complete within 150ms (combines configs + content)
    });
  });

  describe('Batch Operation Performance', () => {
    it('should handle batch content upsert efficiently', async () => {
      const entityType: EntityType = 'event';
      const entityId = 'test-event';
      const languageCode: LanguageCode = 'en';

      const contents = Object.fromEntries(
        Array.from({ length: 20 }, (_, i) => [
          `content-type-${i}`,
          `{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Content ${i}"}]}]}`,
        ])
      );

      const end = tracker.start('batchUpsertContents');
      await service.batchUpsertContents(
        entityType,
        entityId,
        languageCode,
        contents
      );
      const duration = end();

      expect(duration).toBeLessThan(500); // Should complete within 500ms for 20 items
    });

    it('should handle multiple concurrent requests efficiently', async () => {
      const entityType: EntityType = 'event';
      const languageCode: LanguageCode = 'en';

      const concurrentRequests = 10;
      const promises = Array.from({ length: concurrentRequests }, (_, i) => {
        const end = tracker.start(`concurrent-${i}`);
        return service
          .getActiveConfigs(entityType, languageCode)
          .then(() => end());
      });

      const durations = await Promise.all(promises);
      const maxDuration = Math.max(...durations);
      const avgDuration =
        durations.reduce((a, b) => a + b, 0) / durations.length;

      expect(maxDuration).toBeLessThan(200); // No single request should take more than 200ms
      expect(avgDuration).toBeLessThan(100); // Average should be under 100ms
    });
  });

  describe('Cache Performance Impact', () => {
    it('should show significant performance improvement with cache', async () => {
      const entityType: EntityType = 'event';
      const languageCode: LanguageCode = 'en';

      const cachedConfigs = Array.from({ length: 10 }, (_, i) => ({
        id: `config-${i}`,
        entity_type: entityType,
        language_code: languageCode,
        key: `key-${i}`,
        label: `Label ${i}`,
        placeholder: '',
        icon: '',
        sort_order: i,
        is_active: true,
        is_preset: false,
        deleted_at: null,
        deleted_by: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      // Test without cache (cache miss)
      (mockCache.getActiveConfigs as vi.Mock).mockResolvedValueOnce(null);
      const endNoCacheTime = tracker.start('no-cache');
      await service.getActiveConfigs(entityType, languageCode);
      const noCacheDuration = endNoCacheTime();

      // Test with cache (cache hit)
      (mockCache.getActiveConfigs as vi.Mock).mockResolvedValueOnce(
        cachedConfigs
      );
      const endCacheTime = tracker.start('with-cache');
      await service.getActiveConfigs(entityType, languageCode);
      const cacheDuration = endCacheTime();

      // Cache should be significantly faster
      expect(cacheDuration).toBeLessThan(noCacheDuration * 0.5);
      expect(cacheDuration).toBeLessThan(10); // Cache hit should be very fast
    });
  });

  describe('Memory Usage Tests', () => {
    it('should handle large datasets without excessive memory usage', async () => {
      const entityType: EntityType = 'event';
      const languageCode: LanguageCode = 'en';

      // Create service with large dataset simulation
      const largeConfigRepo = createMockConfigRepo(1);
      (largeConfigRepo.getActiveConfigs as vi.Mock).mockImplementation(
        async () => {
          return Array.from({ length: 1000 }, (_, i) => ({
            id: `config-${i}`,
            entity_type: entityType,
            language_code: languageCode,
            key: `key-${i}`,
            label: `Label ${i}`,
            placeholder: `Placeholder ${i}`,
            icon: 'icon',
            sort_order: i,
            is_active: true,
            is_preset: false,
            deleted_at: null,
            deleted_by: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }));
        }
      );

      const largeService = new RichTextTabsService(
        largeConfigRepo,
        mockContentRepo,
        mockCache
      );

      const initialMemory = process.memoryUsage().heapUsed;

      // Perform multiple operations with large datasets
      for (let i = 0; i < 10; i++) {
        await largeService.getActiveConfigs(entityType, languageCode);
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Stress Tests', () => {
    it('should maintain performance under high load', async () => {
      const entityType: EntityType = 'event';
      const languageCode: LanguageCode = 'en';

      const iterations = 100;
      const durations: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const end = tracker.start(`stress-${i}`);
        await service.getActiveConfigs(entityType, languageCode);
        durations.push(end());
      }

      const stats = tracker.getStats('stress-0'); // Get stats for the pattern
      const avgDuration =
        durations.reduce((a, b) => a + b, 0) / durations.length;
      const maxDuration = Math.max(...durations);

      expect(avgDuration).toBeLessThan(100); // Average should stay under 100ms
      expect(maxDuration).toBeLessThan(200); // No outliers over 200ms

      // Performance should be consistent (standard deviation should be low)
      const variance =
        durations.reduce((acc, duration) => {
          return acc + Math.pow(duration - avgDuration, 2);
        }, 0) / durations.length;
      const stdDev = Math.sqrt(variance);

      expect(stdDev).toBeLessThan(avgDuration * 0.5); // Standard deviation should be less than 50% of average
    });
  });
});
