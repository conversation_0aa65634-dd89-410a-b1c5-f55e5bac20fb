import { describe, it, expect } from 'vitest';

import app from '@/app';

// 构造 mock D1 数据库
const mockDB = {
  prepare: (query: string) => {
    const upper = query.toUpperCase();
    const buildResp = () => ({
      all: async () => ({
        results: [{ id: '1', circle_id: '1', event_id: '1' }],
      }),
      first: async () => {
        if (upper.includes('COUNT(*)')) return { total: 1 };
        return { id: '1', circle_id: '1', event_id: '1' };
      },
      run: async () => ({ success: true }),
      bind: (..._args: any[]) => buildResp(),
    });
    return buildResp();
  },
};

// @ts-ignore
const Request = globalThis.Request;

function withEnv(url: string, env: any) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base), env);
}

describe('Appearances API', () => {
  it('should return all appearances', async () => {
    const res = await withEnv('/appearances', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(Array.isArray(data.items)).toBe(true);
    expect(data.page).toBe(1);
    expect(data.pageSize).toBe(50);
    expect(data.total).toBe(1);
  });

  it('should return appearance detail', async () => {
    const res = await withEnv('/appearances/1', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(data.id).toBe('1');
  });

  it('should return 404 for not found appearance', async () => {
    const res = await withEnv('/api/appearances/not-exist-id', { DB: mockDB });
    expect(res.status).toBe(404);
  });
});
