import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

import { ConsoleLogger } from '@/infrastructure/logger';

/**
 * ConsoleLogger 单元测试
 */

describe('ConsoleLogger', () => {
  let logger: ConsoleLogger;
  const spies: Record<string, any> = {} as any;

  beforeEach(() => {
    logger = new ConsoleLogger();
    // 为 console 方法创建 spy
    spies.info = vi.spyOn(console, 'info').mockImplementation(() => {});
    spies.warn = vi.spyOn(console, 'warn').mockImplementation(() => {});
    spies.error = vi.spyOn(console, 'error').mockImplementation(() => {});
    spies.debug = vi.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    // 恢复原始 console 方法
    Object.values(spies).forEach((spy) => spy.mockRestore());
  });

  it('should call console.info when logger.info invoked', () => {
    logger.info('hello');
    expect(spies.info).toHaveBeenCalledTimes(1);
  });

  it('should call console.warn when logger.warn invoked', () => {
    logger.warn('warn message');
    expect(spies.warn).toHaveBeenCalledTimes(1);
  });

  it('should call console.error when logger.error invoked', () => {
    logger.error('error message');
    expect(spies.error).toHaveBeenCalledTimes(1);
  });

  it('should call console.debug when logger.debug invoked', () => {
    logger.debug('debug message');
    expect(spies.debug).toHaveBeenCalledTimes(1);
  });
});
