import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { Context } from 'hono';
import {
  createSuccessResponse,
  createPaginatedResponse,
  jsonWithLocale,
  jsonPaginatedWithLocale,
} from '@/utils/responseFormatter';
import type { Locale } from '@/middlewares/locale';

describe('responseFormatter', () => {
  describe('createSuccessResponse', () => {
    it('should create a success response with basic data', () => {
      const data = { id: 1, name: 'test' };
      const locale: Locale = 'en';

      const response = createSuccessResponse(data, locale);

      expect(response).toEqual({
        success: true,
        data,
        locale: 'en',
        timestamp: expect.any(String),
      });
      expect(new Date(response.timestamp)).toBeInstanceOf(Date);
    });

    it('should create a success response with meta data', () => {
      const data = { id: 1, name: 'test' };
      const locale: Locale = 'zh';
      const meta = { version: '1.0', source: 'api' };

      const response = createSuccessResponse(data, locale, meta);

      expect(response).toEqual({
        success: true,
        data,
        locale: 'zh',
        timestamp: expect.any(String),
        meta,
      });
    });

    it('should handle different locales', () => {
      const data = 'test data';

      const enResponse = createSuccessResponse(data, 'en');
      const zhResponse = createSuccessResponse(data, 'zh');
      const jaResponse = createSuccessResponse(data, 'ja');

      expect(enResponse.locale).toBe('en');
      expect(zhResponse.locale).toBe('zh');
      expect(jaResponse.locale).toBe('ja');
    });

    it('should handle null and undefined data', () => {
      const nullResponse = createSuccessResponse(null, 'en');
      const undefinedResponse = createSuccessResponse(undefined, 'en');

      expect(nullResponse.data).toBeNull();
      expect(undefinedResponse.data).toBeUndefined();
      expect(nullResponse.success).toBe(true);
      expect(undefinedResponse.success).toBe(true);
    });

    it('should not include meta when not provided', () => {
      const response = createSuccessResponse('test', 'en');

      expect(response).not.toHaveProperty('meta');
    });

    it('should not include meta when meta is undefined', () => {
      const response = createSuccessResponse('test', 'en', undefined);

      expect(response).not.toHaveProperty('meta');
    });
  });

  describe('createPaginatedResponse', () => {
    it('should create a paginated response with basic pagination', () => {
      const items = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const total = 10;
      const page = 1;
      const pageSize = 3;
      const locale: Locale = 'en';

      const response = createPaginatedResponse(
        items,
        total,
        page,
        pageSize,
        locale
      );

      expect(response).toEqual({
        success: true,
        data: items,
        locale: 'en',
        timestamp: expect.any(String),
        meta: {
          total: 10,
          page: 1,
          pageSize: 3,
          hasMore: true,
        },
      });
    });

    it('should set hasMore to false when no more items', () => {
      const items = [{ id: 1 }, { id: 2 }];
      const total = 5;
      const page = 3;
      const pageSize = 2;

      const response = createPaginatedResponse(
        items,
        total,
        page,
        pageSize,
        'en'
      );

      expect(response.meta?.hasMore).toBe(false);
    });

    it('should set hasMore to false when items length is less than pageSize', () => {
      const items = [{ id: 1 }];
      const total = 10;
      const page = 1;
      const pageSize = 3;

      const response = createPaginatedResponse(
        items,
        total,
        page,
        pageSize,
        'en'
      );

      expect(response.meta?.hasMore).toBe(false);
    });

    it('should handle empty items array', () => {
      const items: any[] = [];
      const total = 0;
      const page = 1;
      const pageSize = 10;

      const response = createPaginatedResponse(
        items,
        total,
        page,
        pageSize,
        'en'
      );

      expect(response.data).toEqual([]);
      expect(response.meta?.hasMore).toBe(false);
      expect(response.meta?.total).toBe(0);
    });

    it('should handle last page correctly', () => {
      const items = [{ id: 1 }, { id: 2 }];
      const total = 12;
      const page = 6;
      const pageSize = 2;

      const response = createPaginatedResponse(
        items,
        total,
        page,
        pageSize,
        'en'
      );

      expect(response.meta?.hasMore).toBe(false);
    });
  });

  describe('jsonWithLocale', () => {
    let mockContext: Partial<Context>;

    beforeEach(() => {
      mockContext = {
        get: vi.fn(),
        json: vi.fn(),
      };
    });

    it('should return JSON response with locale from context', () => {
      const data = { test: 'data' };
      const mockResponse = new Response();

      (mockContext.get as any).mockReturnValue('zh');
      (mockContext.json as any).mockReturnValue(mockResponse);

      const result = jsonWithLocale(mockContext as Context, data);

      expect(mockContext.get).toHaveBeenCalledWith('locale');
      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data,
        locale: 'zh',
        timestamp: expect.any(String),
      });
      expect(result).toBe(mockResponse);
    });

    it('should default to "en" locale when not set in context', () => {
      const data = { test: 'data' };
      const mockResponse = new Response();

      (mockContext.get as any).mockReturnValue(null);
      (mockContext.json as any).mockReturnValue(mockResponse);

      jsonWithLocale(mockContext as Context, data);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data,
        locale: 'en',
        timestamp: expect.any(String),
      });
    });

    it('should include meta data when provided', () => {
      const data = { test: 'data' };
      const meta = { version: '1.0' };
      const mockResponse = new Response();

      (mockContext.get as any).mockReturnValue('ja');
      (mockContext.json as any).mockReturnValue(mockResponse);

      jsonWithLocale(mockContext as Context, data, meta);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data,
        locale: 'ja',
        timestamp: expect.any(String),
        meta,
      });
    });
  });

  describe('jsonPaginatedWithLocale', () => {
    let mockContext: Partial<Context>;

    beforeEach(() => {
      mockContext = {
        get: vi.fn(),
        json: vi.fn(),
      };
    });

    it('should return paginated JSON response with locale from context', () => {
      const items = [{ id: 1 }, { id: 2 }];
      const total = 10;
      const page = 1;
      const pageSize = 2;
      const mockResponse = new Response();

      (mockContext.get as any).mockReturnValue('zh');
      (mockContext.json as any).mockReturnValue(mockResponse);

      const result = jsonPaginatedWithLocale(
        mockContext as Context,
        items,
        total,
        page,
        pageSize
      );

      expect(mockContext.get).toHaveBeenCalledWith('locale');
      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: items,
        locale: 'zh',
        timestamp: expect.any(String),
        meta: {
          total,
          page,
          pageSize,
          hasMore: true,
        },
      });
      expect(result).toBe(mockResponse);
    });

    it('should default to "en" locale when not set in context', () => {
      const items = [{ id: 1 }];
      const total = 1;
      const page = 1;
      const pageSize = 10;
      const mockResponse = new Response();

      (mockContext.get as any).mockReturnValue(undefined);
      (mockContext.json as any).mockReturnValue(mockResponse);

      jsonPaginatedWithLocale(
        mockContext as Context,
        items,
        total,
        page,
        pageSize
      );

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: items,
        locale: 'en',
        timestamp: expect.any(String),
        meta: {
          total,
          page,
          pageSize,
          hasMore: false,
        },
      });
    });
  });
});
