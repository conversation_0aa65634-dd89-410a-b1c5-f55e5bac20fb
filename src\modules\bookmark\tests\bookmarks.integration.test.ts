import { describe, it, expect } from 'vitest';

import app from '@/app';

// 简易内存数据库 Mock
const bookmarks: any[] = [];
const circles = [
  { id: 'circle-1', name: '测试工作室1', category: 'original' },
  { id: 'circle-2', name: '测试工作室2', category: 'derivative' },
  { id: 'circle-3', name: '搜索工作室', category: 'original' },
];

const mockDB = {
  prepare: (query: string) => {
    // Better Auth session validation
    if (
      query.trim().startsWith('SELECT') &&
      query.includes('FROM session') &&
      query.includes('JOIN user')
    ) {
      return {
        bind: () => ({
          first: async () => ({
            session_id: 'session-123',
            user_id: 'user-1',
            expires_at: Math.floor(Date.now() / 1000) + 3600,
            id: 'user-1',
            email: '<EMAIL>',
            name: 'Tester',
            username: 'tester',
            role: 'viewer',
          }),
        }),
      };
    }

    // Bookmarks: 查询是否已收藏
    if (query.startsWith('SELECT id FROM bookmarks')) {
      return {
        bind: (userId: string, circleId: string) => ({
          first: async () =>
            bookmarks.find(
              (b) => b.user_id === userId && b.circle_id === circleId
            ) ?? undefined,
        }),
      };
    }

    // Bookmarks: 删除
    if (query.startsWith('DELETE FROM bookmarks')) {
      return {
        bind: (userId: string, circleId: string) => ({
          run: async () => {
            const idx = bookmarks.findIndex(
              (b) => b.user_id === userId && b.circle_id === circleId
            );
            if (idx > -1) bookmarks.splice(idx, 1);
            return {};
          },
        }),
      };
    }

    // Bookmarks: 插入
    if (query.startsWith('INSERT INTO bookmarks')) {
      return {
        bind: (_id: string, userId: string, circleId: string) => ({
          run: async () => {
            bookmarks.push({
              id: `bookmark-${Date.now()}`,
              user_id: userId,
              circle_id: circleId,
              created_at: new Date().toISOString(),
            });
            return {};
          },
        }),
      };
    }

    // Bookmarks: 获取收藏列表 (带 JOIN)
    if (
      query.includes('FROM bookmarks b') &&
      query.includes('JOIN circles c')
    ) {
      return {
        bind: (...params: any[]) => ({
          all: async () => {
            const userId = params[0];
            const search = params.find(
              (p) => typeof p === 'string' && p.includes('%')
            );

            let filteredBookmarks = bookmarks.filter(
              (b) => b.user_id === userId
            );

            if (search) {
              const searchTerm = search.replace(/%/g, '');
              filteredBookmarks = filteredBookmarks.filter((b) => {
                const circle = circles.find((c) => c.id === b.circle_id);
                return circle?.name.includes(searchTerm);
              });
            }

            const results = filteredBookmarks.map((b) => {
              const circle = circles.find((c) => c.id === b.circle_id);
              return {
                id: b.id,
                created_at: b.created_at,
                circle_id: b.circle_id,
                circle_name: circle?.name || 'Unknown',
                circle_category: circle?.category || 'unknown',
                circle_urls: null,
                circle_created_at: '2025-01-01T00:00:00Z',
                circle_updated_at: '2025-01-01T00:00:00Z',
              };
            });

            return { results };
          },
        }),
      };
    }

    // Bookmarks: 获取收藏状态
    if (query.includes('SELECT id, created_at FROM bookmarks')) {
      return {
        bind: (userId: string, circleId: string) => ({
          first: async () => {
            const bookmark = bookmarks.find(
              (b) => b.user_id === userId && b.circle_id === circleId
            );
            return bookmark
              ? { id: bookmark.id, created_at: bookmark.created_at }
              : null;
          },
        }),
      };
    }

    // Bookmarks: 任何包含JOIN circles的查询
    if (query.includes('JOIN circles')) {
      return {
        bind: (...params: any[]) => ({
          first: async () => ({
            total: 2, // 简化的mock数据
          }),
          all: async () => ({
            results: [
              {
                id: 'bookmark-1',
                created_at: '2024-01-01T00:00:00Z',
                circle_id: 'circle-2',
                circle_name: '测试社团2',
                circle_category: 'doujin',
                circle_urls: null,
                circle_created_at: '2024-01-01T00:00:00Z',
                circle_updated_at: '2024-01-01T00:00:00Z',
              },
              {
                id: 'bookmark-2',
                created_at: '2024-01-01T00:00:00Z',
                circle_id: 'circle-3',
                circle_name: '搜索社团',
                circle_category: 'doujin',
                circle_urls: null,
                circle_created_at: '2024-01-01T00:00:00Z',
                circle_updated_at: '2024-01-01T00:00:00Z',
              },
            ],
          }),
        }),
      };
    }

    // Bookmarks: 统计查询（不包括JOIN的COUNT查询）
    if (query.includes('COUNT(*)')) {
      return {
        bind: (...params: any[]) => ({
          first: async () => {
            const userId = params[0];
            const search = params.length > 1 ? params[1] : undefined;

            let userBookmarks = bookmarks.filter((b) => b.user_id === userId);

            // 应用搜索过滤（如果有的话）
            if (search && typeof search === 'string') {
              const searchTerm = search.replace(/%/g, '');
              userBookmarks = userBookmarks.filter((b) => {
                const circle = circles.find((c) => c.id === b.circle_id);
                return circle?.name.includes(searchTerm);
              });
            }

            if (query.includes('datetime')) {
              // 最近7天统计
              return {
                recent: userBookmarks.length,
              };
            }
            // 总数统计
            return {
              total: userBookmarks.length,
            };
          },
          all: async () => {
            const userId = params[0];
            // 分类统计
            const userBookmarks = bookmarks.filter((b) => b.user_id === userId);
            const categoryCounts: Record<string, number> = {};

            userBookmarks.forEach((b) => {
              const circle = circles.find((c) => c.id === b.circle_id);
              if (circle) {
                categoryCounts[circle.category] =
                  (categoryCounts[circle.category] || 0) + 1;
              }
            });

            const results = Object.entries(categoryCounts).map(
              ([category, count]) => ({
                category,
                count,
              })
            );

            return { results };
          },
        }),
      };
    }

    // fallback
    return {
      bind: () => ({
        run: async () => ({}),
        all: async () => ({ results: [] }),
        first: async () => undefined,
      }),
    };
  },
};

// @ts-ignore global polyfill
const Request = globalThis.Request;

function withEnv(url: string, init?: RequestInit & { auth?: string }) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  const headers = new Headers(init?.headers);
  if (init?.auth) headers.append('Authorization', init.auth);
  const req = new Request(base, { method: init?.method ?? 'POST', headers });
  return app.fetch(req, { DB: mockDB });
}

function withGetEnv(url: string, init?: RequestInit & { auth?: string }) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  const headers = new Headers(init?.headers);
  if (init?.auth) headers.append('Authorization', init.auth);
  const req = new Request(base, { method: 'GET', headers });
  return app.fetch(req, { DB: mockDB });
}

describe('Bookmarks API', () => {
  const authHeader = 'Bearer session-123';

  it('should return 401 when not logged in', async () => {
    const res = await withEnv('/circles/123/bookmark');
    expect(res.status).toBe(401);
  });

  it('should toggle bookmark status', async () => {
    // 第一次：收藏
    let res = await withEnv('/circles/circle-1/bookmark', { auth: authHeader });
    expect(res.status).toBe(200);
    let json = (await res.json()) as any;
    expect(json.data.isBookmarked).toBe(true);

    // 第二次：取消收藏
    res = await withEnv('/circles/circle-1/bookmark', { auth: authHeader });
    expect(res.status).toBe(200);
    json = (await res.json()) as any;
    expect(json.data.isBookmarked).toBe(false);
  });

  it('should get bookmark status', async () => {
    // 先收藏一个
    await withEnv('/circles/circle-1/bookmark', { auth: authHeader });

    // 检查收藏状态
    const res = await withGetEnv('/circles/circle-1/bookmark/status', {
      auth: authHeader,
    });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;
    expect(json.data.isBookmarked).toBe(true);
    expect(json.data.bookmarkId).toBeDefined();
    expect(json.data.createdAt).toBeDefined();
  });

  it.skip('should get user bookmarks list', async () => {
    // 先收藏几个
    await withEnv('/circles/circle-1/bookmark', { cookie });
    await withEnv('/circles/circle-2/bookmark', { cookie });

    // 获取收藏列表
    const res = await withGetEnv('/user/bookmarks', { cookie });
    expect(res.status).toBe(200);
    // 简化测试，只检查响应结构
    const json = (await res.json()) as any;
    expect(json.data).toBeDefined();
    expect(json.data.items).toBeDefined();
    expect(json.data.total).toBe(2);
    expect(json.data.page).toBe(1);
    expect(json.data.pageSize).toBe(20);
  });

  it.skip('should search bookmarks', async () => {
    // 先收藏一个包含"搜索"的工作室
    await withEnv('/circles/circle-3/bookmark', { cookie });

    // 搜索收藏
    const res = await withGetEnv('/user/bookmarks?search=搜索', { cookie });
    expect(res.status).toBe(200);
    // 简化测试，只检查响应结构
    const json = (await res.json()) as any;
    expect(json.data).toBeDefined();
    expect(json.data.items).toBeDefined();
    expect(json.data.items[0].circle.name).toContain('搜索');
  });

  it('should get bookmark stats', async () => {
    // 确保有一些收藏数据
    await withEnv('/circles/circle-1/bookmark', { auth: authHeader });
    await withEnv('/circles/circle-2/bookmark', { auth: authHeader });

    // 获取统计
    const res = await withGetEnv('/user/bookmarks/stats', { auth: authHeader });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;
    expect(json.data.totalBookmarks).toBeGreaterThan(0);
    expect(json.data.recentBookmarks).toBeGreaterThan(0);
    expect(json.data.categoryCounts).toBeDefined();
  });

  it('should get bookmark stats with circle IDs when includeIds=true', async () => {
    // 确保有一些收藏数据
    await withEnv('/circles/circle-1/bookmark', { auth: authHeader });
    await withEnv('/circles/circle-2/bookmark', { auth: authHeader });
    await withEnv('/circles/circle-3/bookmark', { auth: authHeader });

    // 获取包含ID列表的统计
    const res = await withGetEnv('/user/bookmarks/stats?includeIds=true', {
      auth: authHeader,
    });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;

    // 验证基础统计数据
    expect(json.data.totalBookmarks).toBeGreaterThanOrEqual(2);
    expect(json.data.recentBookmarks).toBeGreaterThan(0);
    expect(json.data.categoryCounts).toBeDefined();

    // 验证新增的ID列表
    expect(json.data.bookmarkedCircleIds).toBeDefined();
    expect(Array.isArray(json.data.bookmarkedCircleIds)).toBe(true);
    expect(json.data.bookmarkedCircleIds.length).toBeGreaterThanOrEqual(2);
    expect(json.data.bookmarkedCircleIds).toContain('circle-1');
    expect(json.data.bookmarkedCircleIds).toContain('circle-3');
  });

  it('should not include circle IDs when includeIds=false', async () => {
    // 确保有一些收藏数据
    await withEnv('/circles/circle-1/bookmark', { auth: authHeader });

    // 获取不包含ID列表的统计
    const res = await withGetEnv('/user/bookmarks/stats?includeIds=false', {
      auth: authHeader,
    });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;

    // 验证基础统计数据存在
    expect(json.data.totalBookmarks).toBeDefined();
    expect(json.data.recentBookmarks).toBeDefined();
    expect(json.data.categoryCounts).toBeDefined();

    // 验证ID列表不存在
    expect(json.data.bookmarkedCircleIds).toBeUndefined();
  });

  it('should handle empty bookmark list with includeIds=true', async () => {
    // 使用新的用户（没有收藏）- 需要正确的认证格式
    const newUserAuth = 'Bearer new-user-session-123';

    // 获取包含ID列表的统计
    const res = await withGetEnv('/user/bookmarks/stats?includeIds=true', {
      auth: newUserAuth,
    });
    expect(res.status).toBe(200);
    const json = (await res.json()) as any;

    // 验证空收藏的情况
    expect(json.data.totalBookmarks).toBe(0);
    expect(json.data.recentBookmarks).toBe(0);
    expect(json.data.categoryCounts).toEqual({});
    expect(json.data.bookmarkedCircleIds).toEqual([]);
  });

  it('should return 401 for protected routes when not logged in', async () => {
    const routes = [
      '/user/bookmarks',
      '/circles/circle-1/bookmark/status',
      '/user/bookmarks/stats',
    ];

    for (const route of routes) {
      const res = await withGetEnv(route);
      expect(res.status).toBe(401);
    }
  });
});
