# Feed模块 (Feed Module)

## 概述

Feed模块提供多语言内容流功能，支持获取事件(events)和社团(circles)的时间线内容，支持分页和类型过滤。

## 功能特性

- ✅ 多语言内容流 (en, zh, ja)
- ✅ 事件和社团混合展示
- ✅ 分页支持
- ✅ 类型过滤
- ✅ 多语言缓存隔离
- ✅ 时间排序

## API端点

### GET /feed

**功能**: 获取多语言内容流

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `limit` (可选) - 每页数量，默认 `20`，最大 `100`
- `type` (可选) - 内容类型：`all`、`events`、`circles`，默认 `all`

**请求头**:

- `X-Locale` (推荐) - 指定内容语言
- `Accept-Language` - 标准语言头部
- `Cookie: locale=zh` - 语言偏好设置

**响应格式**:

```json
{
  "success": true,
  "data": [
    {
      "id": "feed-event-123",
      "type": "event",
      "content": {
        "id": "event-123",
        "name": "Comiket 103",
        "description": "世界最大的同人志即卖会",
        "start_date": "2024-12-30T10:00:00Z",
        "image_url": "https://example.com/comiket103.jpg"
      },
      "created_at": "2024-01-15T08:00:00Z"
    },
    {
      "id": "feed-circle-456",
      "type": "circle",
      "content": {
        "id": "circle-456",
        "name": "某某工作室",
        "description": "专注于原创漫画创作",
        "start_date": null,
        "image_url": null
      },
      "created_at": "2024-01-14T15:30:00Z"
    }
  ],
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "meta": {
    "total": 200,
    "page": 1,
    "limit": 20,
    "hasMore": true
  }
}
```

## 数据结构

### FeedItem

每个Feed项包含以下字段：

- `id` - Feed项唯一标识 (格式: `feed-{type}-{original_id}`)
- `type` - 内容类型 (`event` | `circle`)
- `content` - 内容详情
- `created_at` - 创建时间

### FeedContent

内容详情根据类型不同：

**事件内容**:

- `id` - 事件ID
- `name` - 事件名称
- `description` - 事件描述
- `start_date` - 开始时间
- `image_url` - 图片URL

**社团内容**:

- `id` - 社团ID
- `name` - 社团名称
- `description` - 社团描述
- `start_date` - null (社团无开始时间)
- `image_url` - null (暂不支持社团图片)

## 排序规则

### 单一类型排序

**仅事件** (`type=events`):

- 按创建时间降序
- 相同创建时间按事件日期降序

**仅社团** (`type=circles`):

- 按创建时间降序
- 相同创建时间按名称升序

### 混合类型排序 (`type=all`)

1. 分别获取事件和社团数据
2. 在内存中按创建时间混合排序
3. 取前N条结果

## 分页机制

### 参数验证

- `page`: 最小值1，无上限
- `limit`: 最小值1，最大值100

### 分页计算

```typescript
const offset = (page - 1) * limit;
```

### hasMore判断

```typescript
const hasMore = items.length === limit && page * limit < total;
```

## 缓存策略

### 缓存键格式

```
feed:{locale}:{type}:page:{page}:limit:{limit}
```

### 示例

```
feed:zh:all:page:1:limit:20
feed:en:events:page:2:limit:10
feed:ja:circles:page:1:limit:50
```

### 缓存时间

- TTL: 5分钟
- 自动失效: 相关数据更新时

## 使用示例

### JavaScript/TypeScript

```typescript
// 获取第一页内容
const response = await fetch('/api/feed?page=1&limit=20&type=all', {
  headers: { 'X-Locale': 'zh' },
});
const data = await response.json();

// 使用openapi-typescript-fetch
import { createClient } from '@/lib/api/client';
const api = createClient();

const { data: feed } = await api.GET('/feed', {
  params: {
    query: {
      page: '1',
      limit: '20',
      type: 'events',
    },
  },
  headers: { 'X-Locale': 'ja' },
});

// 分页加载
async function loadMoreFeed(page: number) {
  const { data } = await api.GET('/feed', {
    params: {
      query: {
        page: page.toString(),
        limit: '20',
      },
    },
    headers: { 'X-Locale': 'zh' },
  });

  return {
    items: data.data,
    hasMore: data.meta.hasMore,
    total: data.meta.total,
  };
}
```

### cURL

```bash
# 获取第一页所有内容
curl -H "X-Locale: zh" \
  "https://api.example.com/feed?page=1&limit=20&type=all"

# 获取第二页事件内容
curl -H "Accept-Language: ja-JP,ja;q=0.9" \
  "https://api.example.com/feed?page=2&limit=10&type=events"

# 获取社团内容
curl -H "X-Locale: en" \
  "https://api.example.com/feed?type=circles&limit=50"
```

## 性能优化

### 数据库优化

- 使用LIMIT和OFFSET进行分页
- 为created_at字段建立索引
- 分别查询后在应用层合并(混合类型时)

### 缓存优化

- 语言隔离缓存
- 分页参数包含在缓存键中
- 5分钟TTL平衡实时性和性能

### 内存优化

- 混合排序时限制数据量
- 及时释放临时数组
- 避免不必要的数据复制

## 限制和注意事项

1. **混合排序性能**: `type=all`时需要内存排序，大数据量时可能影响性能
2. **分页一致性**: 在数据频繁更新时，分页结果可能出现重复或遗漏
3. **语言支持**: 仅支持en、zh、ja三种语言
4. **图片支持**: 社团暂不支持图片字段

## 错误处理

### 常见错误

**参数验证错误**:

```json
{
  "success": false,
  "error": {
    "code": 40002,
    "message": "Invalid parameter: limit must be between 1 and 100"
  }
}
```

**服务器错误**:

```json
{
  "success": false,
  "error": {
    "code": 50002,
    "message": "Feed fetch failed"
  }
}
```

## 未来规划

- 🔄 支持实时更新推送
- 🔄 添加内容过滤和排序选项
- 🔄 支持用户个性化推荐
- 🔄 优化混合排序性能
- 🔄 添加内容预览和摘要功能

## 相关文档

- [API规范](../api/request-spec.md#52-feed-api)
- [i18n指南](../development/i18n.md)
- [性能指南](../development/performance.md)
- [搜索模块](./search.md)
