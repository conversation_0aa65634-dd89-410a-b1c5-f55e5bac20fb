# 前端 Better Auth 迁移指南

## 📋 迁移概览

本文档将指导你将前端认证系统从自研认证迁移到 Better Auth，确保与后端 API 的完美对接。

## 🔄 API 端点变更

### 原有端点 vs 新端点

| 功能         | 原有端点              | 新端点                                                       | 请求格式变更     |
| ------------ | --------------------- | ------------------------------------------------------------ | ---------------- |
| 注册         | `POST /auth/register` | `POST /auth/sign-up/email`                                   | 支持邮箱+用户名  |
| 登录         | `POST /auth/login`    | `POST /auth/sign-in/email`<br/>`POST /auth/sign-in/username` | 支持邮箱或用户名 |
| 登出         | `POST /auth/logout`   | `POST /auth/sign-out`                                        | 无变更           |
| 获取用户信息 | `GET /auth/me`        | `GET /auth/session`                                          | 响应格式变更     |
| 刷新会话     | `POST /auth/refresh`  | 自动处理                                                     | 无需手动调用     |

## 🔧 请求/响应格式变更

### 1. 注册接口

**原有格式：**

```typescript
// POST /auth/register
{
  "username": "alice",
  "password": "password123"
}

// 响应
{
  "id": "uuid-123",
  "username": "alice"
}
```

**新格式：**

```typescript
// POST /auth/sign-up/email
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "Alice",  // 可选
  "username": "alice_user"  // 可选，支持用户名
}

// 响应
{
  "user": {
    "id": "uuid-123",
    "email": "<EMAIL>",
    "name": "Alice",
    "username": "alice_user",
    "role": "user"
  },
  "session": {
    "id": "session-123",
    "userId": "uuid-123",
    "expiresAt": "2024-02-01T00:00:00.000Z"
  }
}
```

### 2. 登录接口

**原有格式：**

```typescript
// POST /auth/login
{
  "username": "alice",
  "password": "password123"
}

// 响应
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "id": "uuid-123",
    "username": "alice",
    "role": "viewer"
  }
}
```

**新格式：**

```typescript
// 方式1: 邮箱登录
// POST /auth/sign-in/email
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 方式2: 用户名登录 (保持原有体验)
// POST /auth/sign-in/username
{
  "username": "alice_user",
  "password": "password123"
}

// 响应 (两种方式相同)
{
  "user": {
    "id": "uuid-123",
    "email": "<EMAIL>",
    "name": "Alice",
    "username": "alice_user",
    "role": "user"
  },
  "session": {
    "id": "session-123",
    "userId": "uuid-123",
    "expiresAt": "2024-02-01T00:00:00.000Z"
  }
}
```

### 3. 获取用户信息

**原有格式：**

```typescript
// GET /auth/me
// 响应
{
  "id": "uuid-123",
  "username": "alice",
  "role": "viewer"
}
```

**新格式：**

```typescript
// GET /auth/session
// 响应
{
  "user": {
    "id": "uuid-123",
    "email": "<EMAIL>",
    "name": "Alice",
    "role": "user"
  },
  "session": {
    "id": "session-123",
    "userId": "uuid-123",
    "expiresAt": "2024-02-01T00:00:00.000Z"
  }
}
```

## 🔄 前端代码迁移

### 1. 认证服务更新

**原有认证服务：**

```typescript
class AuthService {
  async login(username: string, password: string) {
    const response = await fetch('/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password }),
      credentials: 'include',
    });

    const data = await response.json();
    if (data.code === 0) {
      return data.data;
    }
    throw new Error(data.message);
  }

  async getCurrentUser() {
    const response = await fetch('/auth/me', {
      credentials: 'include',
    });
    return response.json();
  }
}
```

**新认证服务：**

```typescript
class AuthService {
  async login(email: string, password: string) {
    const response = await fetch('/auth/sign-in/email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    const data = await response.json();
    return data.user;
  }

  async register(email: string, password: string, name?: string) {
    const response = await fetch('/auth/sign-up/email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password, name }),
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Registration failed');
    }

    const data = await response.json();
    return data.user;
  }

  async getCurrentUser() {
    const response = await fetch('/auth/session', {
      credentials: 'include',
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data.user;
  }

  async logout() {
    await fetch('/auth/sign-out', {
      method: 'POST',
      credentials: 'include',
    });
  }
}
```

### 2. React Hook 更新

**原有 Hook：**

```typescript
interface User {
  id: string;
  username: string;
  role: string;
}

const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);

  const login = async (username: string, password: string) => {
    const userData = await authService.login(username, password);
    setUser(userData);
  };

  // ...
};
```

**新 Hook：**

```typescript
interface User {
  id: string;
  email: string;
  name?: string;
  role: string;
}

const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);

  const login = async (email: string, password: string) => {
    const userData = await authService.login(email, password);
    setUser(userData);
  };

  const register = async (email: string, password: string, name?: string) => {
    const userData = await authService.register(email, password, name);
    setUser(userData);
  };

  // ...
};
```

### 3. 登录表单更新

**原有登录表单：**

```tsx
const LoginForm = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    await login(username, password);
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="用户名"
        value={username}
        onChange={(e) => setUsername(e.target.value)}
      />
      <input
        type="password"
        placeholder="密码"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <button type="submit">登录</button>
    </form>
  );
};
```

**新登录表单：**

```tsx
const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    await login(email, password);
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="email"
        placeholder="邮箱地址"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <input
        type="password"
        placeholder="密码"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <button type="submit">登录</button>
    </form>
  );
};
```

## 🍪 Cookie 管理

### Cookie 名称变更

- **原有**: `auth_session`
- **新的**: `better-auth.session_token`

Better Auth 会自动管理 cookie，无需手动处理。

## 🔐 角色系统

### 角色映射

| 原有角色 | 新角色   | 权限保持        |
| -------- | -------- | --------------- |
| `admin`  | `admin`  | ✅ 不变         |
| `editor` | `editor` | ✅ 不变         |
| `viewer` | `user`   | ✅ 基础用户权限 |

### 权限检查更新

**原有权限检查：**

```typescript
const hasAdminAccess = user?.role === 'admin';
const hasEditorAccess = ['admin', 'editor'].includes(user?.role);
```

**新权限检查：**

```typescript
const hasAdminAccess = user?.role === 'admin';
const hasEditorAccess = ['admin', 'editor'].includes(user?.role);
const hasUserAccess = ['admin', 'editor', 'user'].includes(user?.role);
```

## 🚨 注意事项

### 1. 用户数据迁移

如果你有现有用户，需要通知他们：

- 登录方式从用户名改为邮箱
- 临时邮箱格式：`<EMAIL>`
- 密码保持不变

### 2. 错误处理

Better Auth 的错误响应格式可能不同，需要更新错误处理逻辑：

```typescript
// 新的错误处理
const handleAuthError = (error: any) => {
  if (error.status === 401) {
    return '邮箱或密码错误';
  }
  if (error.status === 400) {
    return '请求格式错误';
  }
  return '登录失败，请稍后重试';
};
```

### 3. 会话管理

Better Auth 自动处理会话刷新，无需手动调用 refresh 接口。

## 🧪 测试清单

- [ ] 邮箱注册功能
- [ ] 邮箱登录功能
- [ ] 登出功能
- [ ] 会话持久化
- [ ] 权限控制
- [ ] 错误处理
- [ ] 自动会话刷新

## 📚 参考资源

- [Better Auth 官方文档](https://www.better-auth.com/)
- [Better Auth React 集成](https://www.better-auth.com/docs/integrations/react)
- [Better Auth TypeScript 类型](https://www.better-auth.com/docs/concepts/typescript)

## 🔄 渐进式迁移建议

1. **第一阶段**: 更新 API 调用，保持向后兼容
2. **第二阶段**: 更新 UI 组件，支持邮箱登录
3. **第三阶段**: 移除旧的认证代码
4. **第四阶段**: 添加新功能（邮箱验证、OAuth 等）

## 💻 完整示例代码

### TypeScript 类型定义

```typescript
// types/auth.ts
export interface User {
  id: string;
  email: string;
  name?: string;
  role: 'admin' | 'editor' | 'user';
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  id: string;
  userId: string;
  expiresAt: string;
  token: string;
}

export interface AuthResponse {
  user: User;
  session: Session;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name?: string;
}
```

### 完整的认证服务

```typescript
// services/authService.ts
import {
  User,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
} from '@/types/auth';

class AuthService {
  private baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787';

  async login(data: LoginRequest): Promise<User> {
    const response = await fetch(`${this.baseURL}/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Login failed');
    }

    const result: AuthResponse = await response.json();
    return result.user;
  }

  async register(data: RegisterRequest): Promise<User> {
    const response = await fetch(`${this.baseURL}/auth/sign-up/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Registration failed');
    }

    const result: AuthResponse = await response.json();
    return result.user;
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await fetch(`${this.baseURL}/auth/session`, {
        credentials: 'include',
      });

      if (!response.ok) {
        return null;
      }

      const result: AuthResponse = await response.json();
      return result.user;
    } catch {
      return null;
    }
  }

  async logout(): Promise<void> {
    await fetch(`${this.baseURL}/auth/sign-out`, {
      method: 'POST',
      credentials: 'include',
    });
  }

  // 检查用户权限
  hasRole(user: User | null, roles: string | string[]): boolean {
    if (!user) return false;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    return allowedRoles.includes(user.role);
  }

  // 检查是否为管理员
  isAdmin(user: User | null): boolean {
    return this.hasRole(user, 'admin');
  }

  // 检查是否为编辑或管理员
  canEdit(user: User | null): boolean {
    return this.hasRole(user, ['admin', 'editor']);
  }
}

export const authService = new AuthService();
```

### React Context 和 Hook

```typescript
// contexts/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from '@/types/auth';
import { authService } from '@/services/authService';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name?: string) => Promise<void>;
  logout: () => Promise<void>;
  hasRole: (roles: string | string[]) => boolean;
  isAdmin: () => boolean;
  canEdit: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const userData = await authService.login({ email, password });
    setUser(userData);
  };

  const register = async (email: string, password: string, name?: string) => {
    const userData = await authService.register({ email, password, name });
    setUser(userData);
  };

  const logout = async () => {
    await authService.logout();
    setUser(null);
  };

  const hasRole = (roles: string | string[]) => {
    return authService.hasRole(user, roles);
  };

  const isAdmin = () => {
    return authService.isAdmin(user);
  };

  const canEdit = () => {
    return authService.canEdit(user);
  };

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    hasRole,
    isAdmin,
    canEdit,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### 权限保护组件

```typescript
// components/ProtectedRoute.tsx
import React, { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRoles?: string | string[];
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles,
  fallback = <div>Access denied</div>,
}) => {
  const { user, isLoading, hasRole } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return null; // Will redirect to login
  }

  if (requiredRoles && !hasRole(requiredRoles)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};
```

## 🎯 迁移检查清单

### 后端验证

- [ ] Better Auth 路由正常工作 (`/auth/*`)
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 会话验证正常
- [ ] 角色权限控制正常

### 前端更新

- [ ] 更新 API 端点路径
- [ ] 更新请求/响应数据格式
- [ ] 更新用户类型定义
- [ ] 更新认证服务
- [ ] 更新 React Context/Hook
- [ ] 更新登录/注册表单
- [ ] 更新权限检查逻辑
- [ ] 更新错误处理

### 测试验证

- [ ] 用户注册流程
- [ ] 用户登录流程
- [ ] 会话持久化
- [ ] 自动登出
- [ ] 权限控制
- [ ] 错误处理
- [ ] 跨页面状态同步

## 🚀 部署注意事项

1. **环境变量更新**

   ```env
   NEXT_PUBLIC_API_URL=https://your-api-domain.com
   ```

2. **CORS 配置**
   确保后端 CORS 配置包含前端域名

3. **Cookie 设置**
   生产环境确保 `secure: true` 和正确的 `sameSite` 设置

4. **错误监控**
   添加认证相关的错误监控和日志
