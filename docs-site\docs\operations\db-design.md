# 数据库设计：Bookmarks / Subscriptions

> 本文档用于记录新功能的表结构与索引设计，供后端与 DBA 评审。

---

## 1. bookmarks（收藏）

| 字段       | 类型     | 约束                      | 说明                |
| ---------- | -------- | ------------------------- | ------------------- |
| id         | CHAR(26) | PK                        | Snowflake/CUID 主键 |
| user_id    | CHAR(26) | FK → users.id             | 收藏人              |
| circle_id  | CHAR(26) | FK → circles.id           | 被收藏社团          |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间            |

### 索引

- `idx_bookmarks_user` (`user_id`)
- 唯一约束 `uq_user_circle` (`user_id`, `circle_id`) 防止重复收藏

## 2. subscriptions（订阅）

| 字段           | 类型     | 约束                      | 说明       |
| -------------- | -------- | ------------------------- | ---------- |
| id             | CHAR(26) | PK                        | 主键       |
| user_id        | CHAR(26) | FK → users.id             | 发起订阅者 |
| target_user_id | CHAR(26) | FK → users.id             | 被订阅用户 |
| created_at     | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间   |

### 索引

- `idx_subs_user` (`user_id`)
- `idx_subs_target` (`target_user_id`)
- 唯一约束 `uq_user_target` (`user_id`, `target_user_id`)

---

## 3. 事务注意事项

- 删除用户或社团时需级联删除对应收藏 / 订阅记录，或使用软删除标记。
- 高并发情况下应使用数据库唯一索引保证幂等性。

---

_Last updated: 2025-07-12_
