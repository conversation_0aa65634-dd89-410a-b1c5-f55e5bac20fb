import { z } from '@hono/zod-openapi';

/**
 * 通用成功响应 Schema
 * code   : 成功状态码，固定为 0
 * message: 请求结果描述
 * data   : 业务数据，可为任意类型（由各路由指定）
 */
export const successResponse = z
  .object({
    code: z.literal(0),
    message: z.string(),
    data: z.any().optional(),
  })
  .openapi('SuccessResponse');

export type SuccessResponse = z.infer<typeof successResponse>;

/**
 * 业务错误码枚举
 * 说明请参见 docs/api/error-codes.md
 */
export enum ErrorCode {
  // --- 1xxxx Circles / Admin ---
  PARAM_MISSING_OR_INVALID = 10001,
  RESOURCE_NOT_FOUND = 10002,
  UNIQUE_CONSTRAINT_VIOLATION = 10003,

  // --- 2xxxx Auth ---
  UNAUTHORIZED = 20001,
  FORBIDDEN = 20002,

  // --- 3xxxx Field Filter ---
  FIELD_EMPTY = 30001,
  UNKNOWN_FIELD = 30002,

  // --- 4xxxx Auth – User
  USER_REGISTER_FAILED = 40001,
  USER_NOT_FOUND = 40002,
  PASSWORD_INCORRECT = 40003,

  // --- 5xxxx Appearances ---
  APPEARANCE_NOT_FOUND = 50001,
  APPEARANCE_BOOTH_REQUIRED = 50002,

  // --- 6xxxx Circles / Artists / Events ---
  CIRCLE_NOT_FOUND = 60001,
  ARTIST_NOT_FOUND = 60002,
  EVENT_NOT_FOUND = 60003,
}

export const errorCodeSchema = z
  .nativeEnum(ErrorCode)
  .openapi({
    description: '业务错误码',
    enum: Object.values(ErrorCode).filter((v) => typeof v === 'number'),
    'x-enum-varnames': Object.keys(ErrorCode).filter((k) => isNaN(Number(k))),
    'x-enum-descriptions': [
      '参数缺失或无效',
      '资源不存在',
      '唯一键冲突',
      '未登录',
      '权限不足',
      'fields 参数为空',
      '请求了未知字段',
      '用户注册失败',
      '用户不存在',
      '密码错误',
      '参展记录不存在',
      'booth_id 必填',
      '社团不存在',
      '作者不存在',
      '展会不存在',
    ],
  })
  .openapi('ErrorCodes');

/**
 * 通用错误响应 Schema
 * code      : 业务错误码
 * message   : 错误信息（面向终端用户）
 * detail    : 额外调试信息，可选
 * requestId : 方便排查的请求追踪 ID
 */
export const errorResponse = z
  .object({
    // 业务错误码，使用枚举以在 OpenAPI 中生成 x-enum 信息
    // 直接引用独立错误码 Schema，避免重复定义
    code: errorCodeSchema,
    message: z.string(),
    detail: z.any().optional(),
    requestId: z.string(),
  })
  .openapi('ErrorResponse');

export type ErrorResponse = z.infer<typeof errorResponse>;

/**
 * 分页结果通用结构
 * total     : 结果总条数
 * page      : 当前页码（从 1 开始）
 * pageSize  : 每页条数
 * items     : 数据数组
 */
export const paginatedResult = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z
    .object({
      total: z.number().int().openapi({ example: 120 }),
      page: z.number().int().openapi({ example: 1 }),
      pageSize: z.number().int().openapi({ example: 20 }),
      items: z.array(itemSchema),
    })
    .openapi('PaginatedResult');

export type PaginatedResult = z.infer<ReturnType<typeof paginatedResult>>;

/**
 * 表单 / 参数验证错误详情
 * fieldErrors: 以字段名为键的错误信息映射
 */
export const validationErrorSchema = z
  .object({
    fieldErrors: z.record(z.string()).openapi({
      example: { name: '必填字段', email: '邮箱格式无效' },
    }),
  })
  .openapi('ValidationError');

export type ValidationError = z.infer<typeof validationErrorSchema>;
