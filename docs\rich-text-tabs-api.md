# Rich Text Tabs API Documentation

## Overview

The Rich Text Tabs API provides comprehensive management of dynamic tab configurations and rich-text content for Events and Venues entities. It supports multi-language content, preset configurations, and advanced caching mechanisms.

## Base URL

```
/api/rich-text
```

## Authentication

All endpoints require valid authentication. Include the authorization header:

```
Authorization: Bearer <your-token>
```

## Common Parameters

### Entity Types

- `event` - Event entities
- `venue` - Venue entities

### Language Codes

- `en` - English
- `zh` - Chinese (Simplified)
- `ja` - Japanese

## Configuration Management

### Get Active Configurations

Retrieve active tab configurations for a specific entity type and language.

```http
GET /configs?entity_type={entityType}&language_code={languageCode}
```

**Parameters:**

- `entity_type` (required): Entity type (`event` | `venue`)
- `language_code` (required): Language code (`en` | `zh` | `ja`)
- `include_deleted` (optional): Include soft-deleted configs (`true` | `false`)

**Response:**

```json
{
  "configs": [
    {
      "id": "config-123",
      "entity_type": "event",
      "language_code": "en",
      "key": "introduction",
      "label": "Introduction",
      "placeholder": "Enter event introduction...",
      "icon": "info",
      "sort_order": 0,
      "is_active": true,
      "is_preset": true,
      "deleted_at": null,
      "deleted_by": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Create Configuration

Create a new tab configuration.

```http
POST /configs
```

**Request Body:**

```json
{
  "entity_type": "event",
  "language_code": "en",
  "key": "details",
  "label": "Event Details",
  "placeholder": "Enter event details...",
  "icon": "file-text",
  "sort_order": 1,
  "is_active": true
}
```

**Response:**

```json
{
  "config": {
    "id": "config-456",
    "entity_type": "event",
    "language_code": "en",
    "key": "details",
    "label": "Event Details",
    "placeholder": "Enter event details...",
    "icon": "file-text",
    "sort_order": 1,
    "is_active": true,
    "is_preset": false,
    "deleted_at": null,
    "deleted_by": null,
    "created_at": "2024-01-01T01:00:00Z",
    "updated_at": "2024-01-01T01:00:00Z"
  }
}
```

### Update Configuration

Update an existing configuration.

```http
PUT /configs/{configId}
```

**Request Body:**

```json
{
  "label": "Updated Label",
  "placeholder": "Updated placeholder...",
  "is_active": false
}
```

**Response:**

```json
{
  "config": {
    "id": "config-456",
    "label": "Updated Label",
    "placeholder": "Updated placeholder...",
    "is_active": false,
    "updated_at": "2024-01-01T02:00:00Z"
  }
}
```

### Delete Configuration

Soft delete a configuration (preset configurations cannot be deleted).

```http
DELETE /configs/{configId}
```

**Response:**

```json
{
  "success": true,
  "message": "Configuration deleted successfully"
}
```

### Restore Configuration

Restore a soft-deleted configuration.

```http
POST /configs/{configId}/restore
```

**Response:**

```json
{
  "config": {
    "id": "config-456",
    "deleted_at": null,
    "deleted_by": null,
    "updated_at": "2024-01-01T03:00:00Z"
  }
}
```

### Reorder Configurations

Update the sort order of multiple configurations.

```http
PUT /configs/reorder
```

**Request Body:**

```json
{
  "updates": [
    { "id": "config-123", "sort_order": 1 },
    { "id": "config-456", "sort_order": 0 }
  ]
}
```

**Response:**

```json
{
  "configs": [
    {
      "id": "config-456",
      "sort_order": 0,
      "updated_at": "2024-01-01T04:00:00Z"
    },
    {
      "id": "config-123",
      "sort_order": 1,
      "updated_at": "2024-01-01T04:00:00Z"
    }
  ]
}
```

### Batch Update Status

Update the active status of multiple configurations.

```http
PUT /configs/batch-status
```

**Request Body:**

```json
{
  "config_ids": ["config-123", "config-456"],
  "is_active": false
}
```

**Response:**

```json
{
  "configs": [
    {
      "id": "config-123",
      "is_active": false,
      "updated_at": "2024-01-01T05:00:00Z"
    },
    {
      "id": "config-456",
      "is_active": false,
      "updated_at": "2024-01-01T05:00:00Z"
    }
  ]
}
```

## Content Management

### Get Entity Tabs

Retrieve all tabs with their configurations and content for a specific entity.

```http
GET /contents/entity/{entityType}/{entityId}?language_code={languageCode}
```

**Parameters:**

- `entityType` (path): Entity type (`event` | `venue`)
- `entityId` (path): Entity identifier
- `language_code` (query): Language code (`en` | `zh` | `ja`)
- `include_inactive` (optional): Include inactive tabs (`true` | `false`)

**Response:**

```json
{
  "entity_type": "event",
  "entity_id": "event-123",
  "language_code": "en",
  "tabs": [
    {
      "config": {
        "id": "config-123",
        "key": "introduction",
        "label": "Introduction",
        "placeholder": "Enter event introduction...",
        "icon": "info",
        "sort_order": 0,
        "is_active": true,
        "is_preset": true
      },
      "content": {
        "id": "content-789",
        "content_type": "introduction",
        "content": "{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Event introduction content\"}]}]}",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T06:00:00Z"
      }
    }
  ]
}
```

### Upsert Content

Create or update content for a specific tab.

```http
PUT /contents/entity/{entityType}/{entityId}/{contentType}?language_code={languageCode}
```

**Parameters:**

- `entityType` (path): Entity type (`event` | `venue`)
- `entityId` (path): Entity identifier
- `contentType` (path): Content type (tab key)
- `language_code` (query): Language code (`en` | `zh` | `ja`)

**Request Body:**

```json
{
  "content": "{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Updated content\"}]}]}"
}
```

**Response:**

```json
{
  "content": {
    "id": "content-789",
    "entity_type": "event",
    "entity_id": "event-123",
    "language_code": "en",
    "content_type": "introduction",
    "content": "{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Updated content\"}]}]}",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T07:00:00Z"
  }
}
```

### Batch Upsert Contents

Create or update multiple contents for an entity.

```http
POST /contents/entity/{entityType}/{entityId}/batch?language_code={languageCode}
```

**Request Body:**

```json
{
  "contents": {
    "introduction": "{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Introduction content\"}]}]}",
    "details": "{\"type\":\"doc\",\"content\":[{\"type\":\"paragraph\",\"content\":[{\"type\":\"text\",\"text\":\"Details content\"}]}]}"
  }
}
```

**Response:**

```json
{
  "contents": [
    {
      "id": "content-789",
      "content_type": "introduction",
      "content": "...",
      "updated_at": "2024-01-01T08:00:00Z"
    },
    {
      "id": "content-790",
      "content_type": "details",
      "content": "...",
      "updated_at": "2024-01-01T08:00:00Z"
    }
  ]
}
```

## Batch Operations

### Import Configurations

Import multiple configurations from external data.

```http
POST /batch/import/configs
```

**Request Body:**

```json
{
  "entity_type": "event",
  "language_code": "en",
  "configs": [
    {
      "key": "schedule",
      "label": "Schedule",
      "placeholder": "Enter schedule...",
      "icon": "calendar",
      "sort_order": 2,
      "is_active": true
    }
  ],
  "options": {
    "overwrite_existing": false,
    "skip_presets": true
  }
}
```

### Export Configurations

Export configurations for backup or migration.

```http
GET /batch/export/configs?entity_type={entityType}&language_code={languageCode}
```

**Query Parameters:**

- `entity_type` (required): Entity type
- `language_code` (required): Language code
- `exclude_presets` (optional): Exclude preset configurations
- `active_only` (optional): Export only active configurations

### Sync Across Languages

Synchronize configurations across multiple languages.

```http
POST /batch/sync/configs
```

**Request Body:**

```json
{
  "entity_type": "event",
  "source_language": "en",
  "target_languages": ["zh", "ja"],
  "options": {
    "include_presets": false,
    "overwrite_existing": true
  }
}
```

## Error Responses

### Standard Error Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": {
      "field": "entity_type",
      "issue": "Must be one of: event, venue"
    }
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR` (400): Invalid request data
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `CONFLICT` (409): Resource already exists
- `PRESET_CANNOT_DELETE` (403): Cannot delete preset configuration
- `PRESET_CANNOT_DISABLE` (403): Cannot disable preset configuration
- `INTERNAL_SERVER_ERROR` (500): Server error

## Rate Limiting

API requests are rate-limited to prevent abuse:

- **Standard endpoints**: 100 requests per minute
- **Batch operations**: 10 requests per minute
- **Export operations**: 5 requests per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Caching

The API implements multi-layer caching for optimal performance:

- **Memory Cache**: Fast access to frequently used data
- **KV Cache**: Persistent caching across requests
- **Cache TTL**: 5 minutes for configurations, 1 minute for content

Cache headers indicate cache status:

```
X-Cache-Status: HIT
X-Cache-TTL: 300
```

## Webhooks

Configure webhooks to receive notifications for important events:

### Supported Events

- `config.created` - New configuration created
- `config.updated` - Configuration updated
- `config.deleted` - Configuration deleted
- `content.updated` - Content updated
- `batch.completed` - Batch operation completed

### Webhook Payload

```json
{
  "event": "config.created",
  "timestamp": "2024-01-01T09:00:00Z",
  "data": {
    "config": {
      "id": "config-123",
      "entity_type": "event",
      "language_code": "en",
      "key": "new-tab"
    }
  }
}
```
