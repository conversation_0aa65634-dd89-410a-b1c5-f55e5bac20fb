import { z } from '@hono/zod-openapi';

// 用户实体 Schema（DTO）
export const userSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  username: z.string().openapi({ example: 'alice' }),
  role: z.string().openapi({ example: 'viewer' }),
});

export type User = z.infer<typeof userSchema>;

// 创建请求体（需提供用户名和密码，角色可选）
export const userCreateRequest = z.object({
  username: z.string().min(1).openapi({ example: 'alice' }),
  password: z.string().min(8).openapi({ example: 'pwd12345' }),
  role: z.string().optional().openapi({ example: 'viewer' }),
});

export type UserCreateInput = z.infer<typeof userCreateRequest>;

// 更新请求体（全部字段可选）
export const userUpdateRequest = z.object({
  username: z.string().optional().openapi({ example: 'alice' }),
  role: z.string().optional().openapi({ example: 'editor' }),
  password: z.string().optional().openapi({ example: 'newpassword' }),
});

export type UserUpdateInput = z.infer<typeof userUpdateRequest>;
