import { Context, Next } from 'hono';
import { BetterAuthContext } from '../types';
import { auth as betterAuth } from '../lib/better-auth';

/**
 * Better Auth 中间件
 * 替换原有的 authMiddleware，保持兼容性
 */
export const betterAuthMiddleware = () => {
  return async (c: Context, next: Next) => {
    // 初始化认证上下文
    const authContext: BetterAuthContext = {
      user: null,
      session: null,
    };

    try {
      // 使用 Better Auth 官方 API 验证 session
      const env = c.env as CloudflareBindings;
      const authInstance = betterAuth(env);

      const session = await authInstance.api.getSession({
        headers: c.req.raw.headers,
      });

      if (session) {
        // 设置用户信息
        authContext.user = {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
          role: (session.user as any).role || 'user',
        };

        authContext.session = {
          id: session.session.id,
          userId: session.session.userId,
          expiresAt: new Date(session.session.expiresAt),
        };

        // 兼容现有的 roleGuard：设置用户信息到 c.var.user
        c.set('user', {
          id: authContext.user.id,
          role: authContext.user.role,
          username: authContext.user.name || authContext.user.email,
          email: authContext.user.email,
        });
      }
    } catch (error) {
      // 认证失败时静默处理，不影响公开路由
      console.error('[BetterAuth] Error validating session:', error);
    }

    // 设置认证上下文，保持与原有 authMiddleware 的兼容性
    c.set('auth', authContext);

    await next();
  };
};

/**
 * 获取当前认证用户的辅助函数
 */
export const getCurrentUser = (c: Context) => {
  const authContext = c.get('auth') as BetterAuthContext;
  return authContext?.user || null;
};

/**
 * 获取当前会话的辅助函数
 */
export const getCurrentSession = (c: Context) => {
  const authContext = c.get('auth') as BetterAuthContext;
  return authContext?.session || null;
};

/**
 * 检查用户是否已认证的辅助函数
 */
export const isAuthenticated = (c: Context): boolean => {
  const user = getCurrentUser(c);
  return user !== null;
};

/**
 * 检查用户角色的辅助函数
 */
export const hasRole = (c: Context, role: string | string[]): boolean => {
  const user = getCurrentUser(c);
  if (!user) return false;
  const roles = Array.isArray(role) ? role : [role];
  return roles.includes(user.role);
};
