import { describe, it, expect, vi } from 'vitest';

// 统一在顶层 mock uuid，避免在多个测试中重复 mock 导致覆盖问题
vi.mock('uuid', () => ({ v4: () => 'gen-id' }));

import { jsonError, validationError, jsonSuccess } from '@/utils/errorResponse';

/**
 * 构造最小化 Hono Context stub，用于捕获 header/json/body 调用。
 */
function createCtx(requestId?: string) {
  const headers: Record<string, string> = {};
  let storedReqId = requestId;
  return {
    header: (name: string, value: string) => {
      headers[name] = value;
    },
    json: (data: any, status?: number) => ({ data, status }),
    body: (data: any, status?: number) => ({ data, status }),
    get: (key: string) => (key === 'requestId' ? storedReqId : undefined),
    set: (key: string, value: any) => {
      if (key === 'requestId') storedReqId = value;
    },
    req: { query: () => undefined },
    // 仅用于断言 header
    _headers: headers,
  } as any;
}

describe('errorResponse utilities', () => {
  it('jsonError should set X-Request-Id header and return body', () => {
    const ctx = createCtx();
    const result: any = jsonError(ctx, 123, 'Something wrong', 400);

    expect(ctx._headers['X-Request-Id']).toBe('gen-id');
    expect(result.data.code).toBe(123);
    expect(result.data.message).toBe('Something wrong');
    expect(result.data.requestId).toBe('gen-id');
    expect(result.status).toBe(400);
  });

  it('validationError should include fieldErrors detail and status 422', () => {
    const ctx = createCtx();
    const errors = { name: 'required' };
    const res: any = validationError(ctx, errors, 'Invalid', 10002);
    expect(res.status).toBe(422);
    expect(res.data.code).toBe(10002);
    expect(res.data.detail.fieldErrors).toEqual(errors);
  });

  it('jsonSuccess should set X-Success-Message header and include data', () => {
    const ctx = createCtx();
    const data: any = jsonSuccess(ctx, 'ok', { foo: 'bar' });
    expect(ctx._headers['X-Success-Message']).toBe(encodeURIComponent('ok'));
    expect(data.data.code).toBe(0);
    expect(data.data.message).toBe('ok');
    expect(data.data.data).toEqual({ foo: 'bar' });
    expect(data.status).toBe(200);
  });

  it('jsonSuccess with 204 should return empty body response', () => {
    const ctx = createCtx();
    const res: any = jsonSuccess(ctx, 'no content', undefined, 204);
    expect(res.status).toBe(204);
    expect(res.data).toBeNull();
  });
});
