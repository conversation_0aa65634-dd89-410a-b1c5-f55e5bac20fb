// stats.integration.test.ts - /admin/stats 访问测试
import { describe, it, expect } from 'vitest';

import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

function createMockDB(role?: 'admin' | 'editor' | 'viewer') {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      const buildResponse = () => ({
        all: async () => {
          if (upper.includes('FROM SESSION') && upper.includes('JOIN USER')) {
            return {
              results: role
                ? [
                    {
                      session_id: 'session-123',
                      user_id: 's1',
                      expires_at: Math.floor(Date.now() / 1000) + 3600,
                      id: 's1',
                      email: '<EMAIL>',
                      name: 'Tester',
                      username: 'tester',
                      role,
                    },
                  ]
                : [],
            };
          }
          if (upper.includes('GROUP BY MONTH')) {
            return {
              results: [
                { month: '01', count: 2 },
                { month: '03', count: 1 },
              ],
            };
          }
          return { results: [] };
        },
        first: async () => {
          if (upper.includes('COUNT(*)') && upper.includes('FROM CIRCLES')) {
            return { total: 3 };
          }
          if (upper.includes('COUNT(*)') && upper.includes('FROM ARTISTS')) {
            return { total: 2 };
          }
          if (upper.includes('COUNT(*)') && upper.includes('FROM EVENTS')) {
            return { total: 5 };
          }
          // Better Auth session validation
          if (upper.includes('FROM SESSION') && upper.includes('JOIN USER')) {
            return role
              ? {
                  session_id: 'session-123',
                  user_id: 's1',
                  expires_at: Math.floor(Date.now() / 1000) + 3600,
                  id: 's1',
                  email: '<EMAIL>',
                  name: 'Tester',
                  username: 'tester',
                  role,
                }
              : null;
          }
          return null;
        },
        run: async () => ({ success: true }),
      });

      return {
        ...buildResponse(),
        bind: (..._args: any[]) => buildResponse(),
      };
    },
  };
}

function fetchWithEnv(url: string, env: any, headers?: HeadersInit) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  const req = new Request(base, { headers });
  return app.fetch(req, env);
}

describe('Admin stats API', () => {
  it('should reject unauthenticated access', async () => {
    const res = await fetchWithEnv('/admin/stats', { DB: createMockDB() });
    expect(res.status).toBe(401);
  });

  it('should reject non-admin role', async () => {
    const res = await fetchWithEnv(
      '/admin/stats',
      { DB: createMockDB('viewer') },
      {
        Authorization: 'Bearer session-123',
      }
    );
    expect(res.status).toBe(403);
  });

  it('should return stats for admin role', async () => {
    const res = await fetchWithEnv(
      '/admin/stats',
      { DB: createMockDB('admin') },
      {
        Authorization: 'Bearer session-123',
      }
    );
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(data.totals).toEqual({ circles: 3, artists: 2, events: 5 });
    expect(Array.isArray(data.eventsByMonth)).toBe(true);
    expect(data.eventsByMonth.length).toBe(12);
    expect(data.eventsByMonth[0]).toEqual({ month: '01', count: 2 });
    expect(data.eventsByMonth[2]).toEqual({ month: '03', count: 1 });
  });
});
