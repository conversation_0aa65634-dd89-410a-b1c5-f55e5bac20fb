import { Context } from 'hono';

import { queryLogs } from './service';
import { getDB } from '@/infrastructure';

/**
 * 获取操作日志列表（支持分页与筛选）
 * GET /admin/logs
 */
export async function listLogs(c: Context) {
  const search = new URL(c.req.url).searchParams;
  const page = search.get('page') ? Number(search.get('page')) : 1;
  const pageSize = search.get('pageSize') ? Number(search.get('pageSize')) : 20;

  const db = getDB(c);

  const res = await queryLogs(db, {
    page,
    pageSize,
    user_id: search.get('user_id'),
    action: search.get('action'),
    target_type: search.get('target_type'),
  });

  return c.json(res);
}
