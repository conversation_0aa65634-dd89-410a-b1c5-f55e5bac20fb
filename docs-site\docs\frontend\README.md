# 前端对接文档包

> 🚀 **5分钟快速上手** - 本文档包含前端开发者对接 Ayafeed API 所需的全部信息

## 📋 文档目录

| 文档                                    | 说明                | 重要程度   |
| --------------------------------------- | ------------------- | ---------- |
| [快速开始](./quick-start.md)            | 5分钟上手指南       | ⭐⭐⭐⭐⭐ |
| [API客户端生成](./client-generation.md) | 类型安全的API客户端 | ⭐⭐⭐⭐⭐ |
| [认证集成](./authentication.md)         | 用户登录和权限验证  | ⭐⭐⭐⭐   |
| [多语言集成](./i18n-integration.md)     | 国际化最佳实践      | ⭐⭐⭐⭐   |
| [错误处理](./error-handling.md)         | 统一错误处理方案    | ⭐⭐⭐     |
| [常用示例](./common-examples.md)        | 复制粘贴即用的代码  | ⭐⭐⭐     |
| [业务流程](./business-flows.md)         | 核心业务流程说明    | ⭐⭐       |

## 🎯 核心资源

### OpenAPI 规范文件

```
docs-site/static/openapi.json
```

**用途**: 自动生成前端API客户端代码

### 基础URL

```
开发环境: http://localhost:8787
生产环境: https://api.ayafeed.com
```

## 🚀 快速开始

### 1. 生成API客户端

```bash
# 安装依赖
npm install openapi-typescript-fetch

# 生成类型定义
npx openapi-typescript docs-site/static/openapi.json -o src/types/api.ts

# 或使用我们提供的脚本
curl -o generate-api-client.sh https://raw.githubusercontent.com/ayafeed/ayafeed-api/main/scripts/generate-frontend-client.sh
chmod +x generate-api-client.sh && ./generate-api-client.sh
```

### 2. 创建API客户端

```typescript
// src/lib/api.ts
import { Fetcher } from 'openapi-typescript-fetch';
import type { paths } from '@/types/api';

export const api = Fetcher.for<paths>();

api.configure({
  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787',
  init: {
    headers: {
      'Content-Type': 'application/json',
    },
  },
});
```

### 3. 使用示例

```typescript
// 获取事件列表
const { data: events } = await api.GET('/events', {
  headers: { 'X-Locale': 'zh' },
});

// 搜索内容
const { data: searchResults } = await api.GET('/search', {
  params: {
    query: { q: 'Comiket', type: 'events' },
  },
  headers: { 'X-Locale': 'ja' },
});
```

## 📞 技术支持

### 联系方式

- **GitHub Issues**: [提交问题](https://github.com/ayafeed/ayafeed-api/issues)
- **API文档**: [完整文档](../api/)
- **更新通知**: 关注仓库获取API变更通知

### 常见问题

1. **API变更如何获知?** - 订阅仓库通知，关注 [CHANGELOG](../reference/changelog/CHANGELOG.md)
2. **如何处理认证?** - 查看 [认证集成指南](./authentication.md)
3. **多语言如何实现?** - 参考 [多语言集成指南](./i18n-integration.md)

## 🔄 更新机制

### API版本管理

- **当前版本**: v0.4.2.5
- **版本策略**: 语义化版本控制
- **破坏性变更**: 会在主版本号中体现

### 同步流程

1. 后端更新API后会更新 `openapi.json`
2. 前端重新生成客户端代码
3. 更新相关的业务逻辑代码
4. 测试验证功能正常

---

**下一步**: 阅读 [快速开始指南](./quick-start.md) 开始集成 🚀
