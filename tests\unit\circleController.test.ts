import { describe, it, expect, beforeEach, vi } from 'vitest';

import { createCircle, getCircle } from '@/modules/circle/controller';

// ---- Mock circleService ----
vi.mock('@/modules/circle/service', () => {
  return {
    listCircles: vi.fn(),
    createCircle: vi.fn(),
    getCircle: vi.fn(),
    updateCircle: vi.fn(),
    deleteCircle: vi.fn(),
  };
});

// 使用 ESM import 以避免 no-require lint
import * as circleService from '@/modules/circle/service';

function createCtx({
  body = {},
  params = {},
}: {
  body?: any;
  params?: Record<string, string>;
}) {
  const headers: Record<string, string> = {};
  return {
    req: {
      json: async () => body,
      param: (key: string) => params[key],
    },
    env: { DB: {} },
    json: (data: any, status?: number) => ({ data, status }),
    header: (name: string, value: string) => {
      headers[name] = value;
    },
    get: () => undefined,
    set: () => undefined,
    _headers: headers,
  } as any;
}

describe('Circle Controller', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('createCircle: should return validationError when name missing', async () => {
    const ctx = createCtx({ body: {} });

    const res: any = await createCircle(ctx);
    expect(res.data.code).toBe(10001); // default code in validationError
    expect(res.status).toBe(422);
  });

  it('createCircle: should return jsonError on UNIQUE conflict', async () => {
    // make createCircle throw unique error
    (circleService.createCircle as any).mockRejectedValueOnce(
      new Error('UNIQUE something')
    );

    const ctx = createCtx({ body: { name: 'circle1' } });
    const res: any = await createCircle(ctx);
    expect(res.status).toBe(409);
    expect(res.data.code).toBe(10003);
  });

  it('createCircle: should return success 201 when created', async () => {
    const circle = { id: 'c1', name: 'circle1' };
    (circleService.createCircle as any).mockResolvedValueOnce(circle);

    const ctx = createCtx({ body: { name: 'circle1' } });
    const res: any = await createCircle(ctx);

    expect(res.status).toBe(201);
    expect(res.data.message).toBe('社团创建成功');
    expect(res.data.data).toEqual(circle);
  });

  it('getCircle: should return 404 when not found', async () => {
    (circleService.getCircle as any).mockResolvedValueOnce(undefined);
    const ctx = createCtx({ params: { id: 'notfound' } });

    const res: any = await getCircle(ctx);
    expect(res.status).toBe(404);
    expect(res.data.code).toBe(10002);
  });
});
