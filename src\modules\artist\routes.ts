import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import { artistSchema } from './schema';
import { getDB } from '@/infrastructure';
import { paginatedResult } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { jsonWithFields } from '@/utils/fieldFilter';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const pubArtists = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI ----------
const listArtistsRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '作者列表',
  tags: ['Artists'],
  request: {
    query: z.object({
      page: z.string().optional().openapi({ example: '1' }),
      pageSize: z.string().optional().openapi({ example: '50' }),
    }),
  },
  responses: {
    200: {
      description: '作者列表',
      content: {
        'application/json': { schema: paginatedResult(artistSchema) },
      },
    },
  },
});

const getArtistRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '作者详情',
  tags: ['Artists'],
  request: {
    params: z.object({ id: z.string() }),
    query: z.object({ lang: z.string().optional() }),
  },
  responses: {
    200: {
      description: '作者详情',
      content: { 'application/json': { schema: artistSchema } },
    },
    404: { description: 'Not Found' },
  },
});

// ---------- Handlers ----------
async function listArtistsHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const search = new URL(c.req.url).searchParams;
  const page = Math.max(Number(search.get('page') || '1'), 1);
  const pageSize = Math.max(Number(search.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  const totalRes = await db
    .prepare('SELECT COUNT(*) AS total FROM artists')
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  const { results: items } = await db
    .prepare('SELECT * FROM artists ORDER BY name ASC LIMIT ? OFFSET ?')
    .bind(pageSize, offset)
    .all();

  return jsonWithFields(c, { items, total, page, pageSize });
}

async function getArtistHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const id = c.req.param('id');
  const lang = c.req.query('lang') || 'ja';
  const artist = await db
    .prepare('SELECT * FROM artists WHERE id = ?')
    .bind(id)
    .first();
  if (!artist) return jsonError(c, 60002, '资源不存在', 404);
  const translation = await db
    .prepare(
      'SELECT description FROM artist_translations WHERE artist_id = ? AND locale = ?'
    )
    .bind(id, lang)
    .first();
  return jsonWithFields(c, {
    ...artist,
    description: translation?.description || null,
  });
}

// ---------- Register ----------
registerOpenApiRoute(pubArtists, listArtistsRoute, listArtistsHandler);
registerOpenApiRoute(pubArtists, getArtistRoute, getArtistHandler);

export { pubArtists, pubArtists as routes };
