import { z } from '@hono/zod-openapi';

// 参展记录 Schema
export const appearanceSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  circle_id: z.string().openapi({ example: 'circle-uuid' }),
  event_id: z.string().openapi({ example: 'event-uuid' }),
  artist_id: z
    .string()
    .nullable()
    .optional()
    .openapi({ example: 'artist-uuid' }),
  booth_id: z.string().openapi({ example: 'A01a' }),
  path: z
    .string()
    .nullable()
    .optional()
    .openapi({ example: '/2025/05/03/A1.jpg' }),
  created_at: z.string().openapi({ example: '2024-01-01T00:00:00Z' }),
  updated_at: z.string().openapi({ example: '2024-01-01T00:00:00Z' }),
});

export type Appearance = z.infer<typeof appearanceSchema>;
