# Ayafeed API 错误码对照表

| Code  | HTTP 状态 | 模块           | 含义                                 |
| ----- | --------- | -------------- | ------------------------------------ |
| 10001 | 422       | Circle / Admin | 参数缺失或无效                       |
| 10002 | 404       | Circle / Admin | 资源不存在                           |
| 10003 | 409       | Admin          | 唯一键冲突                           |
| 20001 | 401       | Auth           | 未登录                               |
| 20002 | 403       | Auth           | 权限不足                             |
| 30001 | 400       | Field Filter   | `fields` 参数为空                    |
| 30002 | 400       | Field Filter   | 请求了未知字段                       |
| 40001 | 400       | Auth           | 用户注册失败（Zod 校验外的其他错误） |
| 40002 | 400       | Auth           | 用户不存在                           |
| 40003 | 400       | Auth           | 密码错误                             |
| 50001 | 404       | Appearances    | 参展记录不存在                       |
| 50002 | 400       | Appearances    | `booth_id` 必填                      |
| 60001 | 404       | Circles        | 社团不存在                           |
| 60002 | 404       | Artists        | 作者不存在                           |
| 60003 | 404       | Events         | 展会不存在                           |

> 说明：
> • **code = 0 或缺失** 时，前端会退化为通用文案 `Network error`。
> • 所有错误响应均遵循格式：
>
> ```json
> {
>   "code": 12345,
>   "message": "...",
>   "requestId": "...",
>   "detail": { ... }
> }
> ```
>
> • `requestId` 用于日志追踪，当出现 5xx 或难以复现问题时，可将该 ID 提供给后端定位。
> • 为保持代码与文档同步，`ErrorCode` 枚举由 Zod Schema 生成 OpenAPI 时自动携带 `x-enum-varnames` 与 `x-enum-descriptions`，请 **勿手动修改此表中的枚举编号**，如需新增请在源码 `src/schemas/common.ts` 更新。
