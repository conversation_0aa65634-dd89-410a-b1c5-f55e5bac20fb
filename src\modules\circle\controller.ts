import { Context } from 'hono';

import { CircleCreateInput, CircleUpdateInput } from './schema';
import * as circleService from './service';
import { getDB } from '@/infrastructure';
import { Cache, Logger } from '@/infrastructure';
// import type { Locale } from '@/middlewares/locale';
import { recordLog } from '@/utils/auditLog';
import { jsonError, jsonSuccess, validationError } from '@/utils/errorResponse';

// ---------- Controller Handlers ----------
export async function listCircles(c: Context) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');

  // 管理端暂时不支持搜索参数，返回所有数据
  const result = await circleService.listCircles(db, {}, cache, logger);
  return c.json(result.items);
}

export async function createCircle(c: Context) {
  const body: CircleCreateInput = await c.req.json();
  if (!body.name) {
    return validationError(c, { name: '必填字段' });
  }

  const db = getDB(c);
  try {
    const circle = await circleService.createCircle(db, body);
    await recordLog(c, {
      action: 'CREATE_CIRCLE',
      targetType: 'circle',
      targetId: circle.id,
    });
    return jsonSuccess(c, '社团创建成功', circle, 201);
  } catch (e: unknown) {
    if (e instanceof Error && /UNIQUE/.test(e.message)) {
      return jsonError(c, 10003, '唯一键冲突', 409);
    }
    throw e;
  }
}

export async function getCircle(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const circle = await circleService.getCircle(db, id);
  if (!circle) return jsonError(c, 10002, '资源不存在', 404);
  return c.json(circle);
}

export async function updateCircle(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const body: CircleUpdateInput = await c.req.json();

  const updated = await circleService.updateCircle(db, id, body);
  await recordLog(c, {
    action: 'UPDATE_CIRCLE',
    targetType: 'circle',
    targetId: id,
  });
  return jsonSuccess(c, '社团已保存', updated);
}

export async function deleteCircle(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  await circleService.deleteCircle(db, id);
  await recordLog(c, {
    action: 'DELETE_CIRCLE',
    targetType: 'circle',
    targetId: id,
  });
  return jsonSuccess(c, '社团已删除', undefined, 204);
}
