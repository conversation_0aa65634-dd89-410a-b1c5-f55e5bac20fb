# Rich Text 模块迁移指南

> 📝 **版本**: v1.0.0  
> 🕒 **更新时间**: 2025-01-04  
> 👥 **目标读者**: 前端开发者  
> 🎯 **目标**: 从 Rich Text v1.0.0 迁移到 Rich Text Tabs v3.0.0

## 📋 快速导航

- [🔄 迁移概述](#-迁移概述)
- [📊 版本对比](#-版本对比)
- [🚀 迁移策略](#-迁移策略)
- [🔌 API 差异详解](#-api-差异详解)
- [💻 代码迁移示例](#-代码迁移示例)
- [⚠️ 注意事项](#️-注意事项)
- [❓ 常见问题](#-常见问题)

## 🔄 迁移概述

### 为什么要迁移？

**Rich Text Tabs v3.0.0** 提供了更强大的功能：

- ✅ **多语言支持**: 独立的三语言资源管理
- ✅ **动态标签页**: 可配置的标签页管理
- ✅ **预设保护**: 核心内容的保护机制
- ✅ **批量操作**: 高效的批量管理功能
- ✅ **更好的性能**: 多维度缓存优化

### 迁移时间表

- **阶段 1** (当前): 新旧版本并存，新功能使用新版本
- **阶段 2** (2025-02): 逐步迁移现有功能
- **阶段 3** (2025-03): 完全切换到新版本，移除旧版本

## 📊 版本对比

| 功能特性         | v1.0.0 (Legacy) | v3.0.0 (New)      |
| ---------------- | --------------- | ----------------- |
| **API 前缀**     | `/rich-text`    | `/rich-text-tabs` |
| **多语言支持**   | ❌ 无           | ✅ 三语言独立管理 |
| **标签页管理**   | ❌ 固定类型     | ✅ 动态配置       |
| **批量操作**     | ❌ 无           | ✅ 完整支持       |
| **预设保护**     | ❌ 无           | ✅ 预设标签页保护 |
| **缓存策略**     | 基础缓存        | 多维度智能缓存    |
| **OpenAPI 支持** | ✅ 基础支持     | ✅ 完整支持       |

### API 路由对比

**旧版本 (v1.0.0)**:

```
GET    /rich-text/{entityType}/{entityId}/content
GET    /rich-text/{entityType}/{entityId}/content/{contentType}
POST   /rich-text/{entityType}/{entityId}/content
POST   /rich-text/batch
DELETE /rich-text/{entityType}/{entityId}/content
POST   /rich-text/api/upload/images
```

**新版本 (v3.0.0)**:

```
# 配置管理
GET    /rich-text-tabs/configs/{entityType}/{languageCode}
POST   /rich-text-tabs/configs
PUT    /rich-text-tabs/configs/{id}
DELETE /rich-text-tabs/configs/{id}
POST   /rich-text-tabs/configs/{id}/restore
POST   /rich-text-tabs/configs/reorder
POST   /rich-text-tabs/configs/batch-status

# 内容管理
GET    /rich-text-tabs/tabs/{entityType}/{entityId}/{languageCode}
POST   /rich-text-tabs/content
POST   /rich-text-tabs/content/batch
```

## 🚀 迁移策略

### 渐进式迁移方案

**1. 新功能优先使用新版本**

```typescript
// ✅ 推荐：新功能使用新版本
const { data: tabsData } = useEntityTabs('event', 'reitaisai-22', 'en');

// ⚠️ 维护：现有功能暂时保持旧版本
const { data: legacyData } = useEntityContent('event', 'reitaisai-22');
```

**2. 功能模块逐步迁移**

- **第一批**: 新增的多语言功能
- **第二批**: 事件详情页面
- **第三批**: 场馆详情页面
- **第四批**: 管理后台功能

**3. 数据兼容性保障**

- 新版本支持读取旧版本数据
- 迁移过程中数据不会丢失
- 提供数据格式转换工具

## 🔌 API 差异详解

### 1. 获取内容数据

**旧版本**:

```typescript
// 获取所有内容
GET / rich - text / event / reitaisai - 22 / content;

// 获取特定类型内容
GET / rich - text / event / reitaisai - 22 / content / introduction;
```

**新版本**:

```typescript
// 获取完整标签页数据（包含配置和内容）
GET / rich - text - tabs / tabs / event / reitaisai - 22 / en;

// 响应包含：
// - configs: 标签页配置列表
// - contents: 对应的内容数据
```

### 2. 更新内容

**旧版本**:

```typescript
POST /rich-text/event/reitaisai-22/content
{
  "entity_type": "event",
  "entity_id": "reitaisai-22",
  "content_type": "introduction",
  "content": "<p>内容</p>"
}
```

**新版本**:

```typescript
POST /rich-text-tabs/content
{
  "entity_type": "event",
  "entity_id": "reitaisai-22",
  "language_code": "en",
  "content_type": "introduction",
  "content": "<p>内容</p>"
}
```

### 3. 批量操作

**旧版本**:

```typescript
POST /rich-text/batch
{
  "operations": [
    {
      "entity_type": "event",
      "entity_id": "reitaisai-22",
      "content_type": "introduction",
      "content": "<p>内容1</p>"
    }
  ]
}
```

**新版本**:

```typescript
POST /rich-text-tabs/content/batch
{
  "entity_type": "event",
  "entity_id": "reitaisai-22",
  "language_code": "en",
  "contents": [
    {
      "content_type": "introduction",
      "content": "<p>内容1</p>"
    },
    {
      "content_type": "details",
      "content": "<p>内容2</p>"
    }
  ]
}
```

## 💻 代码迁移示例

### React Hook 迁移

**旧版本 Hook**:

```typescript
// hooks/useRichText.ts (Legacy)
export function useEntityContent(entityType: string, entityId: string) {
  return useQuery({
    queryKey: ['richText', entityType, entityId],
    queryFn: () => api.get(`/rich-text/${entityType}/${entityId}/content`),
  });
}

export function useUpdateContent() {
  return useMutation({
    mutationFn: (data: UpdateContentRequest) =>
      api.post(
        `/rich-text/${data.entity_type}/${data.entity_id}/content`,
        data
      ),
  });
}
```

**新版本 Hook**:

```typescript
// hooks/useRichTextTabs.ts (New)
export function useEntityTabs(
  entityType: string,
  entityId: string,
  languageCode: string
) {
  return useQuery({
    queryKey: ['richTextTabs', entityType, entityId, languageCode],
    queryFn: () =>
      api.get(`/rich-text-tabs/tabs/${entityType}/${entityId}/${languageCode}`),
  });
}

export function useUpdateTabContent() {
  return useMutation({
    mutationFn: (data: UpdateTabContentRequest) =>
      api.post('/rich-text-tabs/content', data),
  });
}

export function useTabConfigs(entityType: string, languageCode: string) {
  return useQuery({
    queryKey: ['richTextConfigs', entityType, languageCode],
    queryFn: () =>
      api.get(`/rich-text-tabs/configs/${entityType}/${languageCode}`),
  });
}
```

### 组件迁移示例

**旧版本组件**:

```typescript
// components/RichTextEditor.tsx (Legacy)
interface Props {
  entityType: string;
  entityId: string;
  contentType: string;
}

export function RichTextEditor({ entityType, entityId, contentType }: Props) {
  const { data: content } = useEntityContentByType(entityType, entityId, contentType);
  const updateMutation = useUpdateContent();

  const handleSave = (newContent: string) => {
    updateMutation.mutate({
      entity_type: entityType,
      entity_id: entityId,
      content_type: contentType,
      content: newContent
    });
  };

  return (
    <div>
      <TiptapEditor
        content={content}
        onSave={handleSave}
      />
    </div>
  );
}
```

**新版本组件**:

```typescript
// components/RichTextTabsEditor.tsx (New)
interface Props {
  entityType: string;
  entityId: string;
  languageCode: string;
  contentType?: string; // 可选，用于直接编辑特定标签页
}

export function RichTextTabsEditor({
  entityType,
  entityId,
  languageCode,
  contentType
}: Props) {
  const { data: tabsData } = useEntityTabs(entityType, entityId, languageCode);
  const { data: configs } = useTabConfigs(entityType, languageCode);
  const updateMutation = useUpdateTabContent();

  const [activeTab, setActiveTab] = useState(contentType || configs?.[0]?.key);

  const handleSave = (newContent: string) => {
    updateMutation.mutate({
      entity_type: entityType,
      entity_id: entityId,
      language_code: languageCode,
      content_type: activeTab,
      content: newContent
    });
  };

  return (
    <div>
      {/* 标签页导航 */}
      <TabNavigation
        tabs={configs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* 编辑器 */}
      <TiptapEditor
        content={tabsData?.contents?.[activeTab]}
        onSave={handleSave}
      />
    </div>
  );
}
```

## ⚠️ 注意事项

### 1. 数据兼容性

**重要变更**:

- 新版本增加了 `language_code` 字段
- 实体类型限制为 `event` 和 `venue`（移除了 `circle`）
- 内容类型变为动态配置，不再是固定枚举

### 2. 语言处理

**新版本必须指定语言**:

```typescript
// ❌ 旧版本：无语言概念
useEntityContent('event', 'reitaisai-22');

// ✅ 新版本：必须指定语言
useEntityTabs('event', 'reitaisai-22', 'en');
```

## ❓ 常见问题

### Q1: 迁移过程中数据会丢失吗？

**A**: 不会。新版本完全兼容旧版本数据，迁移过程中：

- 旧数据会自动转换为新格式
- 默认语言设置为英语 (`en`)
- 所有内容都会保留

### Q2: 如何处理多语言内容？

**A**: 新版本提供了完整的多语言支持：

```typescript
// 为不同语言创建内容
const languages = ['en', 'zh', 'ja'] as const;

languages.forEach((lang) => {
  updateTabContent({
    entity_type: 'event',
    entity_id: 'reitaisai-22',
    language_code: lang,
    content_type: 'introduction',
    content: getLocalizedContent(lang),
  });
});
```

## 📚 相关资源

- **新版本 API 文档**: [OpenAPI 规范](./openapi.json) - 查看 `Rich Text Tabs` 相关接口
- **旧版本 API 文档**: [OpenAPI 规范](./openapi.json) - 查看 `Rich Text` 相关接口
- **后端扩展文档**: [Rich Text Tabs Backend Extension](./rich-text-tabs-backend-extension.md)

## 🔄 迁移检查清单

### 开发阶段

- [ ] 了解新旧版本 API 差异
- [ ] 更新类型定义
- [ ] 创建新版本 Hooks
- [ ] 实现语言切换逻辑
- [ ] 添加错误处理

### 测试阶段

- [ ] 测试数据兼容性
- [ ] 验证多语言功能
- [ ] 测试批量操作
- [ ] 性能对比测试

### 部署阶段

- [ ] 确认新旧版本并存
- [ ] 监控迁移过程
- [ ] 收集用户反馈
- [ ] 逐步切换流量

---

**💡 迁移建议**: 建议采用渐进式迁移策略，优先在新功能中使用新版本 API，然后逐步迁移现有功能。整个迁移过程预计需要 2-3 个迭代周期完成。

**🎯 迁移优势**: 新版本提供了更强大的多语言支持、动态配置能力和更好的性能表现，值得投入时间进行迁移。
