import bcrypt from 'bcryptjs';
import { v4 as uuid } from 'uuid';

/**
 * 创建 BetterAuth 兼容的测试用户
 *
 * 用法：
 *   pnpm tsx scripts/create-test-user.ts [email] [password] [name]
 *
 * 示例：
 *   pnpm tsx scripts/create-test-user.ts <EMAIL> password123 "Test User"
 */

async function main() {
  const [, , emailArg, passwordArg, nameArg] = process.argv;

  const email = emailArg || '<EMAIL>';
  const password = passwordArg || 'password123';
  const name = nameArg || 'Test User';

  const userId = uuid();
  const accountId = uuid();
  const passwordHash = await bcrypt.hash(password, 12);
  const now = Date.now();

  console.log('-- BetterAuth 兼容的测试用户数据');
  console.log('-- 邮箱:', email);
  console.log('-- 密码:', password);
  console.log('');

  // 插入用户数据
  const userSql = `INSERT INTO user (id, name, email, email_verified, image, created_at, updated_at, username, role, locale) VALUES (
  '${userId}',
  '${name}',
  '${email}',
  0,
  NULL,
  ${now},
  ${now},
  '${name.toLowerCase().replace(/\s+/g, '_')}',
  'user',
  'en'
);`;

  // 插入账户数据（密码）
  const accountSql = `INSERT INTO account (id, account_id, provider_id, user_id, access_token, refresh_token, id_token, access_token_expires_at, refresh_token_expires_at, scope, password, created_at, updated_at) VALUES (
  '${accountId}',
  '${email}',
  'credential',
  '${userId}',
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  '${passwordHash}',
  ${now},
  ${now}
);`;

  console.log(userSql);
  console.log('');
  console.log(accountSql);
  console.log('');
  console.log('-- 执行方式：');
  console.log('-- 1. 复制上述 SQL 到文件 test-user.sql');
  console.log(
    '-- 2. 运行: wrangler d1 execute ayafeed-api --file=test-user.sql'
  );
  console.log(
    '-- 或者直接执行: wrangler d1 execute ayafeed-api --command="上述SQL"'
  );
}

main().catch((e) => {
  console.error('Error:', e);
  process.exit(1);
});
