import { describe, it, expect, vi } from 'vitest';

import { listArtists, getArtist } from '@/modules/artist/controller';

/**
 * 构造最小化 Context stub，满足 controller 依赖。
 */
function createMockContext({
  url = 'http://localhost/artists',
  query = {} as Record<string, string>,
  params = {} as Record<string, string>,
  db = createMockDB(),
}: {
  url?: string;
  query?: Record<string, string>;
  params?: Record<string, string>;
  db?: any;
}) {
  const urlObj = new URL(url);
  Object.entries(query).forEach(([k, v]) => urlObj.searchParams.set(k, v));

  // 本地存储用于模拟 Context.set/get
  const store: Record<string, any> = {};

  return {
    req: {
      url: urlObj.toString(),
      query: (key: string) => urlObj.searchParams.get(key) ?? undefined,
      param: (key: string) => params[key],
    },
    env: { DB: db },
    header: vi.fn(),
    json: (data: any, _status?: number) => data,
    get: (key: string) => store[key],
    set: (key: string, value: any) => {
      store[key] = value;
    },
  } as any;
}

/**
 * 创建默认的 D1 mock 数据库。
 */
function createMockDB({ exists = true } = {}) {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();
      const buildResp = () => ({
        all: async () => ({
          results: [{ id: '1', name: 'Artist1' }],
        }),
        first: async () => {
          // total 计数
          if (upper.includes('COUNT(*)')) return { total: 1 };
          // 翻译表
          if (upper.includes('ARTIST_TRANSLATIONS'))
            return { description: 'Translated desc' };
          // 主查询
          return exists ? { id: '1', name: 'Artist1' } : undefined;
        },
        bind: (..._args: any[]) => buildResp(),
      });
      return buildResp();
    },
  };
}

describe('Artist Controller', () => {
  it('listArtists: should return paginated artists', async () => {
    const ctx = createMockContext({});
    const result: any = await listArtists(ctx);

    expect(result.items.length).toBe(1);
    expect(result.total).toBe(1);
    expect(result.page).toBe(1);
    expect(result.pageSize).toBe(50);
  });

  it('getArtist: should return artist detail with translation', async () => {
    const ctx = createMockContext({
      url: 'http://localhost/artists/1',
      params: { id: '1' },
      query: { lang: 'ja' },
    });

    const data: any = await getArtist(ctx);
    expect(data.id).toBe('1');
    expect(data.description).toBe('Translated desc');
  });

  it('getArtist: should return error when artist not found', async () => {
    const ctx = createMockContext({
      params: { id: 'no' },
      db: createMockDB({ exists: false }),
    });

    const data: any = await getArtist(ctx);
    expect(data.code).toBe(60002);
    expect(data.message).toBe('资源不存在');
  });
});
