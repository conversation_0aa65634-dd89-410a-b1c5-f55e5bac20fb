import { describe, it, expect } from 'vitest';
import app from '@/app';

// Mock data
const mockContent = {
  id: 'content-1',
  entity_type: 'event',
  entity_id: 'reitaisai-22',
  content_type: 'introduction',
  content: '<p>这是一个测试内容</p>',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockDB = {
  prepare: () => ({
    bind: () => ({
      all: () => Promise.resolve({ results: [mockContent] }),
      first: () => Promise.resolve(mockContent),
      run: () => Promise.resolve({ success: true }),
    }),
  }),
} as any;

// @ts-ignore
const Request = globalThis.Request;

function withEnv(url: string, env: any, options?: RequestInit) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base, options), env);
}

describe('Rich Text API Integration Tests', () => {
  describe('GET /{entityType}/{entityId}/content', () => {
    it('should return all content for an entity', async () => {
      const res = await withEnv('/rich-text/event/reitaisai-22/content', {
        DB: mockDB,
      });
      expect(res.status).toBe(200);

      const data = (await res.json()) as any;
      expect(data.message).toBeDefined();
      expect(data.data).toBeDefined();
    });

    it('should return 400 for invalid entity type', async () => {
      const res = await withEnv('/rich-text/invalid/reitaisai-22/content', {
        DB: mockDB,
      });
      expect(res.status).toBe(400);
    });

    it('should return 400 for missing entity ID', async () => {
      const res = await withEnv('/rich-text/event//content', { DB: mockDB });
      expect(res.status).toBe(404); // Route not found
    });
  });

  describe('GET /{entityType}/{entityId}/content/{contentType}', () => {
    it('should return specific content type', async () => {
      const res = await withEnv(
        '/rich-text/event/reitaisai-22/content/introduction',
        {
          DB: mockDB,
        }
      );
      expect(res.status).toBe(200);

      const data = (await res.json()) as any;
      expect(data.message).toBeDefined();
      expect(data.data.content).toBeDefined();
    });

    it('should return 400 for invalid content type', async () => {
      const res = await withEnv(
        '/rich-text/event/reitaisai-22/content/invalid',
        {
          DB: mockDB,
        }
      );
      expect(res.status).toBe(400);
    });
  });

  describe('POST /{entityType}/{entityId}/content', () => {
    it('should create or update content', async () => {
      const requestBody = {
        entity_type: 'event',
        entity_id: 'reitaisai-22',
        content_type: 'introduction',
        content: '<p>新的内容</p>',
      };

      const res = await withEnv(
        '/rich-text/event/reitaisai-22/content',
        { DB: mockDB },
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      expect(res.status).toBe(201);
      const data = (await res.json()) as any;
      expect(data.message).toBeDefined();
      expect(data.data).toBeDefined();
    });

    it('should return 400 for invalid request body', async () => {
      const requestBody = {
        entity_type: 'invalid',
        entity_id: 'reitaisai-22',
        content_type: 'introduction',
        content: '<p>内容</p>',
      };

      const res = await withEnv(
        '/rich-text/event/reitaisai-22/content',
        { DB: mockDB },
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      expect(res.status).toBe(400);
    });
  });

  describe('PUT /{entityType}/{entityId}/content', () => {
    it('should batch update content', async () => {
      const requestBody = {
        introduction: '<p>介绍内容</p>',
        highlights: '<p>亮点内容</p>',
        guide: '<p>指南内容</p>',
        notices: '<p>公告内容</p>',
      };

      const res = await withEnv(
        '/rich-text/event/reitaisai-22/content',
        { DB: mockDB },
        {
          method: 'PUT',
          body: JSON.stringify(requestBody),
          headers: { 'Content-Type': 'application/json' },
        }
      );

      expect(res.status).toBe(200);
      const data = (await res.json()) as any;
      expect(data.message).toBeDefined();
      expect(data.data).toBeDefined();
    });
  });

  describe('DELETE /{entityType}/{entityId}/content', () => {
    it('should delete all content for an entity', async () => {
      const res = await withEnv(
        '/rich-text/event/reitaisai-22/content',
        { DB: mockDB },
        {
          method: 'DELETE',
        }
      );

      expect(res.status).toBe(200);
      const data = (await res.json()) as any;
      expect(data.message).toBeDefined();
    });
  });
});
