# 用户模块 (Users Module)

## 概述

用户模块提供用户认证、授权和用户信息管理功能，支持基于角色的访问控制(RBAC)。

## 功能特性

- ✅ 用户注册和登录
- ✅ JWT令牌认证
- ✅ 基于角色的访问控制 (RBAC)
- ✅ 用户信息管理
- ✅ 密码安全处理
- ✅ 审计日志记录

## 用户角色

### 角色层级

| 角色     | 权限级别 | 说明                     |
| -------- | -------- | ------------------------ |
| `admin`  | 最高     | 系统管理员，拥有所有权限 |
| `editor` | 高       | 内容编辑者，可以管理内容 |
| `viewer` | 中       | 查看者，只能查看内容     |
| `user`   | 基础     | 普通用户，基础功能       |

### 权限矩阵

| 功能         | admin | editor | viewer | user |
| ------------ | ----- | ------ | ------ | ---- |
| 查看公开内容 | ✅    | ✅     | ✅     | ✅   |
| 管理用户     | ✅    | ❌     | ❌     | ❌   |
| 管理事件     | ✅    | ✅     | ❌     | ❌   |
| 管理社团     | ✅    | ✅     | ❌     | ❌   |
| 查看统计     | ✅    | ❌     | ❌     | ❌   |
| 查看日志     | ✅    | ❌     | ❌     | ❌   |
| 收藏功能     | ✅    | ✅     | ✅     | ✅   |

## API端点

### 认证接口

#### POST /auth/register

**功能**: 用户注册

**请求体**:

```json
{
  "username": "newuser",
  "password": "securepassword123",
  "email": "<EMAIL>"
}
```

**响应格式**:

```json
{
  "code": 0,
  "message": "用户注册成功",
  "data": {
    "user": {
      "id": "user-123",
      "username": "newuser",
      "email": "<EMAIL>",
      "role": "user",
      "created_at": "2024-01-15T10:30:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### POST /auth/login

**功能**: 用户登录

**请求体**:

```json
{
  "username": "existinguser",
  "password": "userpassword123"
}
```

**响应格式**:

```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "user": {
      "id": "user-123",
      "username": "existinguser",
      "email": "<EMAIL>",
      "role": "user"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### POST /auth/logout

**功能**: 用户登出

**请求头**:

- `Authorization: Bearer {token}` (必需)

### 用户管理接口 (需要admin权限)

#### GET /admin/users

**功能**: 获取用户列表

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `50`

#### POST /admin/users

**功能**: 创建用户

#### PUT /admin/users/\{id\}

**功能**: 更新用户信息

#### DELETE /admin/users/\{id\}

**功能**: 删除用户

## 数据结构

### User Schema

| 字段          | 类型   | 说明         | 必需 |
| ------------- | ------ | ------------ | ---- |
| id            | string | 用户唯一标识 | ✅   |
| username      | string | 用户名       | ✅   |
| email         | string | 邮箱地址     | ✅   |
| password_hash | string | 密码哈希     | ✅   |
| role          | string | 用户角色     | ✅   |
| created_at    | string | 创建时间     | ✅   |
| updated_at    | string | 更新时间     | ✅   |

### JWT Token结构

```json
{
  "sub": "user-123",
  "username": "testuser",
  "role": "user",
  "iat": 1642234567,
  "exp": 1642320967
}
```

## 认证流程

### 注册流程

1. 客户端提交用户名、密码、邮箱
2. 服务器验证数据格式和唯一性
3. 密码使用bcrypt加密存储
4. 创建用户记录，默认角色为`user`
5. 生成JWT令牌返回给客户端

### 登录流程

1. 客户端提交用户名和密码
2. 服务器查找用户记录
3. 验证密码哈希
4. 生成新的JWT令牌
5. 返回用户信息和令牌

### 令牌验证

1. 客户端在请求头中携带令牌
2. 服务器验证令牌签名和有效期
3. 提取用户信息和角色
4. 进行权限检查

## 使用示例

### JavaScript/TypeScript

```typescript
// 用户注册
const registerResponse = await fetch('/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'newuser',
    password: 'securepassword123',
    email: '<EMAIL>',
  }),
});
const registerData = await registerResponse.json();

// 用户登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'existinguser',
    password: 'userpassword123',
  }),
});
const loginData = await loginResponse.json();

// 使用令牌访问受保护的资源
const protectedResponse = await fetch('/api/admin/users', {
  headers: {
    Authorization: `Bearer ${loginData.data.token}`,
  },
});

// 使用openapi-typescript-fetch
import { createClient } from '@/lib/api/client';
const api = createClient();

// 登录
const { data: loginResult } = await api.POST('/auth/login', {
  body: {
    username: 'testuser',
    password: 'password123',
  },
});

// 设置认证令牌
api.configure({
  headers: {
    Authorization: `Bearer ${loginResult.data.token}`,
  },
});

// 访问受保护的资源
const { data: users } = await api.GET('/admin/users');
```

### cURL

```bash
# 用户注册
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "securepassword123",
    "email": "<EMAIL>"
  }' \
  "https://api.example.com/auth/register"

# 用户登录
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "username": "existinguser",
    "password": "userpassword123"
  }' \
  "https://api.example.com/auth/login"

# 访问受保护的资源
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.example.com/admin/users"

# 用户登出
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.example.com/auth/logout"
```

## 安全措施

### 密码安全

- 使用bcrypt进行密码哈希
- 最小密码长度要求
- 密码复杂度验证
- 防止密码在响应中泄露

### 令牌安全

- JWT令牌有效期限制 (24小时)
- 使用强随机密钥签名
- 令牌包含必要的用户信息
- 支持令牌撤销机制

### 访问控制

- 基于角色的权限检查
- 路由级别的权限验证
- 操作级别的权限控制
- 审计日志记录

## 错误处理

### 常见错误

**用户名已存在**:

```json
{
  "code": 10003,
  "message": "唯一键冲突",
  "data": {}
}
```

**登录失败**:

```json
{
  "code": 10001,
  "message": "用户名或密码错误",
  "data": {}
}
```

**权限不足**:

```json
{
  "code": 20001,
  "message": "权限不足",
  "data": {}
}
```

**令牌无效**:

```json
{
  "code": 10004,
  "message": "令牌无效或已过期",
  "data": {}
}
```

## 限制和注意事项

1. **用户名唯一性**: 用户名在系统中必须唯一
2. **邮箱唯一性**: 邮箱地址在系统中必须唯一
3. **密码强度**: 密码必须满足最小长度和复杂度要求
4. **令牌有效期**: JWT令牌有24小时有效期
5. **角色权限**: 角色权限是固定的，不支持动态权限

## 未来规划

- 🔄 支持OAuth2.0第三方登录
- 🔄 实现双因素认证(2FA)
- 🔄 添加用户头像和个人资料
- 🔄 支持用户偏好设置
- 🔄 实现用户活动统计
- 🔄 添加密码重置功能

## 相关文档

- [API规范](../api/request-spec.md)
- [收藏模块](./bookmarks.md)
- [API规范](../api/request-spec.md)
- [性能指南](../development/performance.md)
