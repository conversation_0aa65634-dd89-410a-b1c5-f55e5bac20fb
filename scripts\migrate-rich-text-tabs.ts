#!/usr/bin/env tsx

/**
 * 富文本标签页管理系统 - 数据库迁移执行脚本
 * 版本: v3.0.0
 *
 * 功能:
 * - 执行数据库结构迁移
 * - 数据备份和恢复
 * - 迁移状态验证
 * - 回滚支持
 *
 * 注意: 此脚本用于开发环境，生产环境请使用 Cloudflare D1 迁移工具
 */

import { readFileSync } from 'fs';
import { join } from 'path';

// 模拟 D1 数据库接口用于开发环境
interface MockD1Database {
  prepare(sql: string): {
    bind(...params: any[]): {
      run(): Promise<{ changes: number; success: boolean }>;
      first(): Promise<any>;
      all(): Promise<{ results: any[] }>;
    };
    run(): Promise<{ changes: number; success: boolean }>;
    first(): Promise<any>;
    all(): Promise<{ results: any[] }>;
  };
}

interface MigrationResult {
  success: boolean;
  message: string;
  details?: any;
}

class RichTextTabsMigration {
  private migrationPath = join(
    __dirname,
    '../db/migrations/001_rich_text_tabs_migration.sql'
  );

  /**
   * 执行迁移
   */
  async execute(): Promise<MigrationResult> {
    try {
      console.log('🚀 开始执行富文本标签页管理系统迁移...');

      // 1. 检查迁移是否已执行
      const isExecuted = await this.checkMigrationStatus();
      if (isExecuted) {
        return {
          success: true,
          message: '迁移已经执行过，跳过重复执行',
        };
      }

      // 2. 读取迁移脚本
      const migrationSQL = readFileSync(this.migrationPath, 'utf-8');

      // 3. 执行迁移
      console.log('📝 执行数据库结构变更...');
      await this.executeMigrationSQL(migrationSQL);

      // 4. 验证迁移结果
      console.log('✅ 验证迁移结果...');
      const validation = await this.validateMigration();

      if (!validation.success) {
        throw new Error(`迁移验证失败: ${validation.message}`);
      }

      console.log('🎉 富文本标签页管理系统迁移完成！');
      return {
        success: true,
        message: '迁移执行成功',
        details: validation.details,
      };
    } catch (error) {
      console.error('❌ 迁移执行失败:', error);
      return {
        success: false,
        message: `迁移失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 检查迁移状态
   */
  private async checkMigrationStatus(): Promise<boolean> {
    try {
      const result = await db
        .prepare(
          `
        SELECT COUNT(*) as count 
        FROM migration_history 
        WHERE migration_name = ?
      `
        )
        .bind('001_rich_text_tabs_migration.sql')
        .first();

      return (result?.count as number) > 0;
    } catch (error) {
      // 如果表不存在，说明是首次迁移
      return false;
    }
  }

  /**
   * 执行迁移SQL
   */
  private async executeMigrationSQL(sql: string): Promise<void> {
    // 将SQL按语句分割并逐个执行
    const statements = sql
      .split(';')
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        await db.prepare(statement).run();
      }
    }
  }

  /**
   * 验证迁移结果
   */
  private async validateMigration(): Promise<MigrationResult> {
    try {
      // 1. 检查表是否创建成功
      const tables = await this.checkTablesExist();
      if (!tables.success) {
        return tables;
      }

      // 2. 检查预设数据是否插入成功
      const presetData = await this.checkPresetData();
      if (!presetData.success) {
        return presetData;
      }

      // 3. 检查索引是否创建成功
      const indexes = await this.checkIndexes();
      if (!indexes.success) {
        return indexes;
      }

      return {
        success: true,
        message: '所有验证通过',
        details: {
          tables: tables.details,
          presetData: presetData.details,
          indexes: indexes.details,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `验证过程出错: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * 检查表是否存在
   */
  private async checkTablesExist(): Promise<MigrationResult> {
    const requiredTables = [
      'content_type_configs',
      'rich_text_contents',
      'migration_history',
    ];
    const existingTables: string[] = [];

    for (const tableName of requiredTables) {
      const result = await db
        .prepare(
          `
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
      `
        )
        .bind(tableName)
        .first();

      if (result) {
        existingTables.push(tableName);
      }
    }

    const allTablesExist = existingTables.length === requiredTables.length;

    return {
      success: allTablesExist,
      message: allTablesExist ? '所有必需表已创建' : '部分表创建失败',
      details: {
        required: requiredTables,
        existing: existingTables,
        missing: requiredTables.filter((t) => !existingTables.includes(t)),
      },
    };
  }

  /**
   * 检查预设数据
   */
  private async checkPresetData(): Promise<MigrationResult> {
    const presetCount = await db
      .prepare(
        `
      SELECT COUNT(*) as count 
      FROM content_type_configs 
      WHERE is_preset = TRUE
    `
      )
      .first();

    const expectedPresetCount = 6; // 2 实体类型 × 3 语言
    const actualCount = presetCount?.count as number;

    return {
      success: actualCount === expectedPresetCount,
      message:
        actualCount === expectedPresetCount
          ? '预设数据插入成功'
          : `预设数据数量不匹配，期望 ${expectedPresetCount}，实际 ${actualCount}`,
      details: {
        expected: expectedPresetCount,
        actual: actualCount,
      },
    };
  }

  /**
   * 检查索引
   */
  private async checkIndexes(): Promise<MigrationResult> {
    const requiredIndexes = [
      'idx_config_entity_lang_active',
      'idx_config_key_lookup',
      'idx_config_preset',
      'idx_config_deleted',
      'idx_content_entity_lang',
      'idx_content_type_lookup',
      'idx_content_updated',
      'idx_content_entity_type',
    ];

    const existingIndexes: string[] = [];

    for (const indexName of requiredIndexes) {
      const result = await db
        .prepare(
          `
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name=?
      `
        )
        .bind(indexName)
        .first();

      if (result) {
        existingIndexes.push(indexName);
      }
    }

    const allIndexesExist = existingIndexes.length === requiredIndexes.length;

    return {
      success: allIndexesExist,
      message: allIndexesExist ? '所有索引已创建' : '部分索引创建失败',
      details: {
        required: requiredIndexes,
        existing: existingIndexes,
        missing: requiredIndexes.filter((i) => !existingIndexes.includes(i)),
      },
    };
  }

  /**
   * 回滚迁移（如果需要）
   */
  async rollback(): Promise<MigrationResult> {
    try {
      console.log('🔄 开始回滚迁移...');

      // 删除新创建的表
      await db.prepare('DROP TABLE IF EXISTS content_type_configs').run();
      await db.prepare('DROP TABLE IF EXISTS rich_text_contents').run();

      // 恢复备份数据（如果存在）
      const backupExists = await db
        .prepare(
          `
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='rich_text_contents_backup'
      `
        )
        .first();

      if (backupExists) {
        // 重新创建原始表结构并恢复数据
        await db
          .prepare(
            `
          CREATE TABLE rich_text_contents AS 
          SELECT * FROM rich_text_contents_backup
        `
          )
          .run();

        await db.prepare('DROP TABLE rich_text_contents_backup').run();
      }

      // 删除迁移记录
      await db
        .prepare(
          `
        DELETE FROM migration_history 
        WHERE migration_name = ?
      `
        )
        .bind('001_rich_text_tabs_migration.sql')
        .run();

      console.log('✅ 迁移回滚完成');
      return {
        success: true,
        message: '迁移回滚成功',
      };
    } catch (error) {
      console.error('❌ 回滚失败:', error);
      return {
        success: false,
        message: `回滚失败: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
}

// 主执行函数
async function main() {
  const migration = new RichTextTabsMigration();

  const args = process.argv.slice(2);
  const command = args[0];

  if (command === 'rollback') {
    const result = await migration.rollback();
    console.log(result.message);
    process.exit(result.success ? 0 : 1);
  } else {
    const result = await migration.execute();
    console.log(result.message);
    if (result.details) {
      console.log('详细信息:', JSON.stringify(result.details, null, 2));
    }
    process.exit(result.success ? 0 : 1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

export { RichTextTabsMigration };
