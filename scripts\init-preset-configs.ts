#!/usr/bin/env tsx

/**
 * 初始化预设配置脚本
 * 确保所有必需的预设配置都存在
 */
import { getDB } from '@/infrastructure';

interface PresetConfigData {
  id: string;
  entity_type: string;
  language_code: string;
  key: string;
  label: string;
  placeholder: string;
  icon: string;
  sort_order: number;
  is_active: boolean;
  is_preset: boolean;
}

const PRESET_CONFIGS: Omit<PresetConfigData, 'id'>[] = [
  // Event 预设配置
  {
    entity_type: 'event',
    language_code: 'en',
    key: 'introduction',
    label: 'Introduction',
    placeholder: 'Enter event introduction...',
    icon: 'info',
    sort_order: 0,
    is_active: true,
    is_preset: true,
  },
  {
    entity_type: 'event',
    language_code: 'zh',
    key: 'introduction',
    label: '介绍',
    placeholder: '输入活动介绍...',
    icon: 'info',
    sort_order: 0,
    is_active: true,
    is_preset: true,
  },
  {
    entity_type: 'event',
    language_code: 'ja',
    key: 'introduction',
    label: '紹介',
    placeholder: 'イベント紹介を入力...',
    icon: 'info',
    sort_order: 0,
    is_active: true,
    is_preset: true,
  },

  // Venue 预设配置
  {
    entity_type: 'venue',
    language_code: 'en',
    key: 'overview',
    label: 'Overview',
    placeholder: 'Enter venue overview...',
    icon: 'map-pin',
    sort_order: 0,
    is_active: true,
    is_preset: true,
  },
  {
    entity_type: 'venue',
    language_code: 'zh',
    key: 'overview',
    label: '概览',
    placeholder: '输入场馆概览...',
    icon: 'map-pin',
    sort_order: 0,
    is_active: true,
    is_preset: true,
  },
  {
    entity_type: 'venue',
    language_code: 'ja',
    key: 'overview',
    label: '概要',
    placeholder: '会場概要を入力...',
    icon: 'map-pin',
    sort_order: 0,
    is_active: true,
    is_preset: true,
  },
];

function generatePresetId(
  entityType: string,
  languageCode: string,
  key: string
): string {
  return `preset-${entityType}-${languageCode}-${key}`;
}

async function initPresetConfigs() {
  console.log('🚀 开始初始化预设配置...\n');

  try {
    const db = getDB(null as any); // 根据实际情况调整
    const now = new Date().toISOString();

    let createdCount = 0;
    let skippedCount = 0;

    for (const config of PRESET_CONFIGS) {
      const id = generatePresetId(
        config.entity_type,
        config.language_code,
        config.key
      );

      // 检查是否已存在
      const existingStmt = db.prepare(`
        SELECT id FROM content_type_configs 
        WHERE entity_type = ? AND language_code = ? AND key = ? AND deleted_at IS NULL
      `);

      const existing = await existingStmt
        .bind(config.entity_type, config.language_code, config.key)
        .first();

      if (existing) {
        console.log(
          `⏭️  跳过已存在: ${config.entity_type}/${config.language_code}/${config.key}`
        );
        skippedCount++;
        continue;
      }

      // 创建新的预设配置
      const insertStmt = db.prepare(`
        INSERT INTO content_type_configs (
          id, entity_type, language_code, key, label, placeholder, icon,
          sort_order, is_active, is_preset, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      await insertStmt
        .bind(
          id,
          config.entity_type,
          config.language_code,
          config.key,
          config.label,
          config.placeholder,
          config.icon,
          config.sort_order,
          config.is_active ? 1 : 0,
          config.is_preset ? 1 : 0,
          now,
          now
        )
        .run();

      console.log(
        `✅ 创建成功: ${config.entity_type}/${config.language_code}/${config.key} - "${config.label}"`
      );
      createdCount++;
    }

    console.log(`\n📊 初始化完成:`);
    console.log(`  ✅ 新创建: ${createdCount} 条`);
    console.log(`  ⏭️  已跳过: ${skippedCount} 条`);
    console.log(`  📋 总计: ${PRESET_CONFIGS.length} 条预设配置`);

    if (createdCount > 0) {
      console.log(
        `\n🎉 预设配置初始化成功！现在可以正常使用 rich-text-tabs 功能了。`
      );
    } else {
      console.log(`\n✨ 所有预设配置都已存在，无需创建。`);
    }
  } catch (error) {
    console.error('❌ 初始化失败:', error);
    console.log('\n💡 可能的原因:');
    console.log('  1. 数据库连接失败');
    console.log('  2. content_type_configs 表不存在');
    console.log('  3. 权限不足');
    process.exit(1);
  }
}

// 执行初始化
initPresetConfigs().catch(console.error);
