{"name": "ayafeed-api", "version": "*******", "scripts": {"dev": "wrangler dev", "build": "wrangler deploy --dry-run", "deploy": "wrangler deploy --minify", "lint": "eslint --max-warnings 0 .", "format": "prettier --write .", "type-check": "tsc -p tsconfig.json --noEmit", "test": "vitest", "gen:openapi": "tsx scripts/generate-openapi.ts", "gen:types": "openapi-typescript docs-site/static/openapi.json -o src/api-types.d.ts", "gen:api": "pnpm run gen:openapi && pnpm run gen:types", "db:seed": "tsx scripts/batch-pnpm.ts", "prepare": "husky", "cf-gen-types": "wrangler types --env-interface CloudflareBindings"}, "lint-staged": {"*.{js,jsx,ts,tsx,cjs,mjs}": ["eslint --fix --cache", "prettier --write"], "*.{json,md,yaml,yml}": ["prettier --write"], "*.css": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@auth/core": "^0.40.0", "@auth/d1-adapter": "^1.10.0", "@hono-rate-limiter/cloudflare": "^0.2.2", "@hono/auth-js": "^1.0.17", "@hono/zod-validator": "^0.7.1", "@neondatabase/serverless": "^1.0.1", "bcryptjs": "^3.0.2", "better-auth": "^1.3.4", "dependency-cruiser": "^16.10.4", "drizzle-orm": "^0.44.4", "fs": "0.0.1-security", "hono": "^4.8.5", "hono-rate-limiter": "^0.4.2", "path": "^0.12.7", "resend": "^4.7.0", "uuid": "^9.0.1", "zod": "^3.25.76"}, "devDependencies": {"@better-auth/cli": "^1.3.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.31.0", "@hono/zod-openapi": "^0.19.10", "@types/minimist": "^1.2.5", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitest/coverage-v8": "3.2.4", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "drizzle-kit": "^0.31.4", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-boundaries": "^5.0.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.1", "fast-glob": "^3.3.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "minimist": "^1.2.8", "openapi-typescript": "^7.8.0", "prettier": "^3.6.2", "release-please": "^17.1.1", "stylelint": "^16.22.0", "stylelint-config-standard": "^38.0.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4", "wrangler": "^4.24.3"}}