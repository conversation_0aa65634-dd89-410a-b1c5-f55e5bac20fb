# OpenAPI 自动化指南（v0.4.2.5）

> 目标：彻底摆脱手动维护 `docs-site/static/openapi.json`，通过代码与脚本实现 **一键生成 → CI 校验 → 类型同步** 的闭环。

---

## 1. 核心流程

```mermaid
flowchart TD
  A[Zod Schemas] -- openapi-zod -> B(OpenAPI Spec in memory)
  B -- generate-openapi.ts -> C(docs-site/static/openapi.json)
  C -- openapi-typescript -> D(api-types.d.ts)
  A -. compile check .-> E(TypeScript 编译)
```

1. **Zod Schema 定义**：在各模块（如 `src/modules/*/schema.ts`）和通用模式（`src/schemas/common.ts`）中定义所有请求 / 响应模型。
2. **脚本 `scripts/generate-openapi.ts`** 读取各模块的 Zod 定义，使用 `@hono/zod-openapi` 生成 OpenAPI 结构体。
3. **静态文件输出**：脚本写入 `docs-site/static/openapi.json`，并自动合并：
   - `components.securitySchemes.cookieAuth`
   - `servers`
   - 全局 `tags`
4. **类型生成**：调用 `openapi-typescript` 将最新 Spec 转为 `src/api-types.d.ts`，供前端 / 测试使用。
5. **CI 校验**：在 Pull Request 中执行：

```bash
pnpm run gen:api && git diff --exit-code docs-site/static/openapi.json src/api-types.d.ts
```

若 Spec 过期将导致 CI 失败，提醒开发者更新并提交。

---

## 2. 约定与最佳实践

### 2.1 可复用 Schema

在 `src/schemas/common.ts` 集中声明：

- `SuccessResponse<T>`
- `ErrorResponse`
- `ValidationError`
- `PaginatedResult<T>`
- `ErrorCode` **枚举**（附 `x-enum-varnames` / `x-enum-descriptions`）

### 2.2 路由定义

使用 `@hono/zod-openapi` 的 `createRoute()` 和 `registerOpenApiRoute()` 描述并导出：

```ts
import { OpenAPIHono, createRoute } from '@hono/zod-openapi';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const routes = new OpenAPIHono<HonoApp>();

const listCirclesRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '获取社团列表',
  tags: ['Admin.Circles'],
  responses: {
    200: {
      description: '社团列表',
      content: {
        'application/json': { schema: paginatedResult(circleSchema) },
      },
    },
  },
});

registerOpenApiRoute(routes, listCirclesRoute, circleController.listCircles);
```

- 标签命名避免空格，如 `Admin.Circles`、`Admin.Events`。
- 写操作（创建 / 更新 / 删除）需在 `200/201` 响应体中引用 `SuccessResponse` 并带 `message`。
- 列表接口返回 `PaginatedResult<T>`。

### 2.3 状态码约定

| 操作        | HTTP 状态            |
| ----------- | -------------------- |
| 创建        | 201                  |
| 更新 / 删除 | 200                  |
| 列表 / 详情 | 200                  |
| 校验失败    | 400 或 422（待评估） |

### 2.4 版本号自动递增

`generate-openapi.ts` 会读取当前 `docs-site/static/openapi.json.info.version`（例如 `*******`），根据 `package.json.version` 自动 +patch（第四位）。

---

## 3. FAQ

**Q：为什么不用 swagger-jsdoc？**  
A：Zod 定义即代码实现，可避免注释与实现分离；且类型直接复用，减少维护成本。

**Q：手动编辑 `docs-site/static/openapi.json` 会怎样？**  
A：CI 会检测 diff 并失败，请始终使用 `pnpm run gen:api`。

---

_Last updated: 2025-07-28_
