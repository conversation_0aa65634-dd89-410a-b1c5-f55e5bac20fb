# 社团模块 (Circles Module)

## 概述

社团模块提供同人社团的管理和查询功能，支持多语言显示和社团信息管理。

## 功能特性

- ✅ 多语言社团信息 (主要为日语，支持zh/en)
- ✅ 社团列表查询和分页
- ✅ 单个社团详情获取
- ✅ 社团管理 (CRUD操作)
- ✅ URL管理
- ✅ 多语言缓存支持

## API端点

### 公开接口

#### GET /circles

**功能**: 获取社团列表

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `50`
- `fields` (可选) - 返回字段过滤

**请求头**:

- `X-Locale` (推荐) - 指定显示语言
- `Accept-Language` - 标准语言头部

**响应格式**:

```json
{
  "items": [
    {
      "id": "circle-123",
      "name": "某某工作室",
      "urls": "{\"twitter\":\"@example\",\"website\":\"https://example.com\"}",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 500,
  "page": 1,
  "pageSize": 50
}
```

#### GET /circles/\{id\}

**功能**: 获取单个社团详情

**路径参数**:

- `id` (必需) - 社团ID

**响应格式**:

```json
{
  "id": "circle-123",
  "name": "某某工作室",
  "category": "original",
  "urls": "{\"twitter\":\"@example\",\"website\":\"https://example.com\"}",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 管理接口 (需要认证)

#### POST /admin/circles

**功能**: 创建新社团

**请求体**:

```json
{
  "name": "新社团名称",
  "category": "original",
  "urls": "{\"twitter\":\"@newcircle\",\"website\":\"https://newcircle.com\"}"
}
```

#### PUT /admin/circles/\{id\}

**功能**: 更新社团信息

#### DELETE /admin/circles/\{id\}

**功能**: 删除社团

## 数据结构

### Circle Schema

| 字段       | 类型   | 说明               | 必需 |
| ---------- | ------ | ------------------ | ---- |
| id         | string | 社团唯一标识       | ✅   |
| name       | string | 社团名称           | ✅   |
| urls       | string | JSON格式的链接信息 | -    |
| created_at | string | 创建时间           | ✅   |
| updated_at | string | 更新时间           | ✅   |

### URLs字段格式

urls字段存储JSON格式的社交链接：

```json
{
  "twitter": "@circle_name",
  "website": "https://circle-website.com",
  "pixiv": "https://pixiv.net/users/123456",
  "booth": "https://circle.booth.pm"
}
```

## 多语言支持

### 当前实现

社团模块目前使用简化的多语言支持：

- **主要语言**: 日语 (大部分社团名称为日语)
- **查询逻辑**: 根据locale参数查询对应的多语言字段
- **降级处理**: 如果指定语言的字段为空，显示原始name字段

### 查询示例

```sql
-- locale = 'zh' 时的查询
SELECT
  id,
  COALESCE(name_zh, name) as name,
  urls,
  created_at,
  updated_at
FROM circles
WHERE name_zh IS NOT NULL OR name IS NOT NULL
ORDER BY COALESCE(name_zh, name) ASC
```

## 缓存策略

### 缓存键格式

```
circles:{locale}:all
circles:{locale}:detail:{id}
```

### 示例

```
circles:zh:all
circles:ja:all
circles:en:detail:circle-123
```

### 缓存时间

- 列表缓存: 5分钟
- 详情缓存: 10分钟
- 自动失效: 社团数据更新时

## 使用示例

### JavaScript/TypeScript

```typescript
// 获取社团列表
const response = await fetch('/api/circles?page=1&pageSize=20', {
  headers: { 'X-Locale': 'ja' },
});
const data = await response.json();

// 获取社团详情
const circleResponse = await fetch('/api/circles/circle-123', {
  headers: { 'X-Locale': 'zh' },
});
const circle = await circleResponse.json();

// 使用openapi-typescript-fetch
import { createClient } from '@/lib/api/client';
const api = createClient();

// 获取社团列表
const { data: circles } = await api.GET('/circles', {
  params: {
    query: { page: '1', pageSize: '20' },
  },
  headers: { 'X-Locale': 'ja' },
});

// 创建社团 (需要认证)
const { data: newCircle } = await api.POST('/admin/circles', {
  body: {
    name: '新社团',
    urls: JSON.stringify({
      twitter: '@newcircle',
      website: 'https://newcircle.com',
    }),
  },
  headers: {
    Authorization: 'Bearer YOUR_TOKEN',
    'X-Locale': 'zh',
  },
});
```

### cURL

```bash
# 获取社团列表
curl -H "X-Locale: ja" \
  "https://api.example.com/circles?page=1&pageSize=20"

# 获取社团详情
curl -H "Accept-Language: zh-CN,zh;q=0.9" \
  "https://api.example.com/circles/circle-123"

# 创建社团 (需要认证)
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "X-Locale: zh" \
  -d '{
    "name": "新社团名称",
    "category": "original",
    "urls": "{\"twitter\":\"@newcircle\",\"website\":\"https://newcircle.com\"}"
  }' \
  "https://api.example.com/admin/circles"
```

## 关联功能

### 收藏功能

社团可以被用户收藏：

```typescript
// 收藏社团
await api.POST('/circles/\{id\}/bookmark', {
  params: { path: { id: 'circle-123' } },
  headers: { Authorization: 'Bearer YOUR_TOKEN' },
});

// 取消收藏
await api.DELETE('/circles/\{id\}/bookmark', {
  params: { path: { id: 'circle-123' } },
  headers: { Authorization: 'Bearer YOUR_TOKEN' },
});
```

### 出展记录

查看社团的出展历史：

```typescript
// 获取社团出展记录
const { data: appearances } = await api.GET('/circles/\{id\}/appearances', {
  params: { path: { id: 'circle-123' } },
  headers: { 'X-Locale': 'ja' },
});
```

## 性能优化

### 数据库优化

- `name` 字段建立索引
- `created_at` 字段建立索引
- 多语言字段建立复合索引

### 缓存优化

- 语言隔离缓存
- 列表和详情分别缓存
- 数据更新时主动清理缓存

### 查询优化

- 使用COALESCE处理多语言字段
- 避免复杂的JOIN操作
- 合理使用LIMIT和OFFSET

## 权限控制

### 公开接口

- 无需认证
- 支持CORS
- 基础速率限制

### 管理接口

- 需要JWT认证
- 需要admin或editor角色
- 操作记录审计日志

## 错误处理

### 常见错误

**社团不存在**:

```json
{
  "code": 10002,
  "message": "资源不存在",
  "data": {}
}
```

**唯一键冲突**:

```json
{
  "code": 10003,
  "message": "唯一键冲突",
  "data": {}
}
```

**参数验证错误**:

```json
{
  "code": 40002,
  "message": "缺少必填字段",
  "data": {
    "name": "必填字段"
  }
}
```

## 限制和注意事项

1. **名称唯一性**: 社团名称在系统中必须唯一
2. **URLs格式**: urls字段必须是有效的JSON字符串
3. **多语言数据**: 大部分社团数据为日语，其他语言可能缺失

## 未来规划

- 🔄 完善多语言翻译表支持
- 🔄 添加社团标签和搜索功能
- 🔄 支持社团头像和封面图片
- 🔄 实现社团关注和推荐功能
- 🔄 添加社团统计信息(粉丝数、作品数等)

## 相关文档

- [API规范](../api/request-spec.md)
- [收藏模块](./bookmarks.md)
- [事件模块](./events.md)
- [搜索模块](./search.md)
