import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  createEvent,
  updateEvent,
  deleteEvent,
  getEvent,
  listEvents,
} from '@/modules/event/controller';
import * as eventService from '@/modules/event/service';
import { jsonError, jsonSuccess, validationError } from '@/utils/errorResponse';
import { getDB } from '@/infrastructure';
import { recordLog } from '@/utils/auditLog';

// Mock dependencies
vi.mock('@/modules/event/service');
vi.mock('@/utils/errorResponse');
vi.mock('@/infrastructure');
vi.mock('@/utils/auditLog');
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-123'),
}));

// Mock context
const createMockContext = (overrides: any = {}) => ({
  req: {
    json: vi.fn(),
    param: vi.fn(),
    url: 'http://localhost/test',
  },
  get: vi.fn((key: string) => {
    if (key === 'locale') return 'en';
    return null;
  }),
  json: vi.fn(),
  ...overrides,
});

describe('event/controller', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getDB as any).mockReturnValue({});
    (jsonSuccess as any).mockReturnValue({ status: 200 });
    (jsonError as any).mockReturnValue({ status: 404 });
    (validationError as any).mockReturnValue({ status: 400 });
    (recordLog as any).mockResolvedValue(undefined);
  });

  describe('listEvents', () => {
    it('should list events successfully', async () => {
      const mockEventsData = {
        items: [{ id: 'event-1', name: 'Test Event' }],
        total: 1,
        page: 1,
        pageSize: 50,
      };
      const mockContext = createMockContext();
      mockContext.get.mockImplementation((key: string) => {
        if (key === 'locale') return 'en';
        if (key === 'cache') return null;
        if (key === 'logger') return null;
        return null;
      });
      (eventService.listEvents as any).mockResolvedValue(mockEventsData);

      await listEvents(mockContext as any);

      expect(eventService.listEvents).toHaveBeenCalledWith(
        {},
        expect.any(URLSearchParams),
        'en',
        null,
        null
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockEventsData);
    });

    it('should handle different locales', async () => {
      const mockEventsData = {
        items: [{ id: 'event-1', name: 'テストイベント' }],
        total: 1,
        page: 1,
        pageSize: 50,
      };
      const mockContext = createMockContext();
      mockContext.get.mockImplementation((key: string) => {
        if (key === 'locale') return 'ja';
        if (key === 'cache') return null;
        if (key === 'logger') return null;
        return null;
      });
      (eventService.listEvents as any).mockResolvedValue(mockEventsData);

      await listEvents(mockContext as any);

      expect(eventService.listEvents).toHaveBeenCalledWith(
        {},
        expect.any(URLSearchParams),
        'ja',
        null,
        null
      );
    });

    it('should pass cache and logger when available', async () => {
      const mockCache = { get: vi.fn(), set: vi.fn() };
      const mockLogger = { debug: vi.fn(), info: vi.fn() };
      const mockEventsData = {
        items: [],
        total: 0,
        page: 1,
        pageSize: 50,
      };
      const mockContext = createMockContext();
      mockContext.get.mockImplementation((key: string) => {
        if (key === 'locale') return 'en';
        if (key === 'cache') return mockCache;
        if (key === 'logger') return mockLogger;
        return null;
      });
      (eventService.listEvents as any).mockResolvedValue(mockEventsData);

      await listEvents(mockContext as any);

      expect(eventService.listEvents).toHaveBeenCalledWith(
        {},
        expect.any(URLSearchParams),
        'en',
        mockCache,
        mockLogger
      );
    });
  });

  describe('createEvent', () => {
    it('should create event successfully with valid data', async () => {
      const mockEventData = {
        name_en: 'Test Event',
        name_ja: 'テストイベント',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月1日',
        date_sort: 20250501,
      };

      const mockCreatedEvent = { id: 'mock-uuid-123', ...mockEventData };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockEventData);
      (eventService.createEvent as any).mockResolvedValue(mockCreatedEvent);

      await createEvent(mockContext as any);

      expect(eventService.createEvent).toHaveBeenCalledWith(
        {},
        expect.objectContaining({
          ...mockEventData,
          id: 'mock-uuid-123',
          date_sort: 20250501,
        })
      );
      expect(recordLog).toHaveBeenCalledWith(mockContext, {
        action: 'CREATE_EVENT',
        targetType: 'event',
        targetId: 'mock-uuid-123',
      });
      expect(jsonSuccess).toHaveBeenCalledWith(
        mockContext,
        '展会创建成功',
        mockCreatedEvent,
        201
      );
    });

    it('should return validation error for missing required fields', async () => {
      const mockEventData = {
        name_en: 'Test Event',
        // Missing name_ja, date_en, date_ja
      };

      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockEventData);

      await createEvent(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        name_ja: '必填字段',
        date_en: '必填字段',
        date_ja: '必填字段',
      });
      expect(eventService.createEvent).not.toHaveBeenCalled();
    });

    it('should auto-generate date_sort from date_ja when not provided', async () => {
      const mockEventData = {
        name_en: 'Test Event',
        name_ja: 'テストイベント',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月1日 10:30',
        // No date_sort provided
      };

      const mockCreatedEvent = { id: 'mock-uuid-123', ...mockEventData };
      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockEventData);
      (eventService.createEvent as any).mockResolvedValue(mockCreatedEvent);

      await createEvent(mockContext as any);

      expect(eventService.createEvent).toHaveBeenCalledWith(
        {},
        expect.objectContaining({
          date_sort: 20255110, // Extracted from '2025年5月1日 10:30' -> '20255110' (first 8 digits)
        })
      );
    });

    it('should handle date extraction with insufficient digits', async () => {
      const mockEventData = {
        name_en: 'Test Event',
        name_ja: 'テストイベント',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月', // Only 6 digits, not enough for date_sort
        // No date_sort provided
      };

      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockEventData);

      await createEvent(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        date_sort: '日期排序字段无效，需提供 yyyymmdd 数值',
      });
      expect(eventService.createEvent).not.toHaveBeenCalled();
    });

    it('should return validation error for invalid date_sort', async () => {
      const mockEventData = {
        name_en: 'Test Event',
        name_ja: 'テストイベント',
        date_en: 'Invalid Date',
        date_ja: 'Invalid Date',
        // No valid digits to extract
      };

      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockEventData);

      await createEvent(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        date_sort: '日期排序字段无效，需提供 yyyymmdd 数值',
      });
      expect(eventService.createEvent).not.toHaveBeenCalled();
    });

    it('should handle short digit extraction gracefully', async () => {
      const mockEventData = {
        name_en: 'Test Event',
        name_ja: 'テストイベント',
        date_en: 'May 1, 2025',
        date_ja: '2025年5月', // Only 6 digits
        // No date_sort provided
      };

      const mockContext = createMockContext();
      mockContext.req.json.mockResolvedValue(mockEventData);

      await createEvent(mockContext as any);

      expect(validationError).toHaveBeenCalledWith(mockContext, {
        date_sort: '日期排序字段无效，需提供 yyyymmdd 数值',
      });
    });
  });

  describe('getEvent', () => {
    it('should return event when found', async () => {
      const mockEvent = { id: 'event-1', name: 'Test Event' };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('event-1');
      (eventService.getEvent as any).mockResolvedValue(mockEvent);

      await getEvent(mockContext as any);

      expect(eventService.getEvent).toHaveBeenCalledWith({}, 'event-1', 'en');
      expect(mockContext.json).toHaveBeenCalledWith(mockEvent);
    });

    it('should return 404 when event not found', async () => {
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('nonexistent-id');
      (eventService.getEvent as any).mockResolvedValue(null);

      await getEvent(mockContext as any);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        10002,
        '资源不存在',
        404
      );
    });

    it('should use different locales correctly', async () => {
      const mockEvent = { id: 'event-1', name: 'テストイベント' };
      const mockContext = createMockContext();
      mockContext.get.mockImplementation((key: string) => {
        if (key === 'locale') return 'ja';
        return null;
      });
      mockContext.req.param.mockReturnValue('event-1');
      (eventService.getEvent as any).mockResolvedValue(mockEvent);

      await getEvent(mockContext as any);

      expect(eventService.getEvent).toHaveBeenCalledWith({}, 'event-1', 'ja');
    });
  });

  describe('updateEvent', () => {
    it('should update event successfully', async () => {
      const mockUpdateData = { name_en: 'Updated Event' };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('event-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);
      (eventService.updateEvent as any).mockResolvedValue(undefined);

      await updateEvent(mockContext as any);

      expect(eventService.updateEvent).toHaveBeenCalledWith(
        {},
        'event-1',
        mockUpdateData
      );
      expect(recordLog).toHaveBeenCalledWith(mockContext, {
        action: 'UPDATE_EVENT',
        targetType: 'event',
        targetId: 'event-1',
      });
      expect(jsonSuccess).toHaveBeenCalledWith(mockContext, '展会已保存');
    });

    it('should convert string date_sort to number', async () => {
      const mockUpdateData = {
        name_en: 'Updated Event',
        date_sort: '20250501', // String instead of number
      };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('event-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);
      (eventService.updateEvent as any).mockResolvedValue(undefined);

      await updateEvent(mockContext as any);

      expect(eventService.updateEvent).toHaveBeenCalledWith({}, 'event-1', {
        name_en: 'Updated Event',
        date_sort: 20250501, // Converted to number
      });
    });

    it('should handle invalid date_sort string', async () => {
      const mockUpdateData = {
        name_en: 'Updated Event',
        date_sort: 'invalid-date', // Invalid string
      };
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('event-1');
      mockContext.req.json.mockResolvedValue(mockUpdateData);
      (eventService.updateEvent as any).mockResolvedValue(undefined);

      await updateEvent(mockContext as any);

      expect(eventService.updateEvent).toHaveBeenCalledWith({}, 'event-1', {
        name_en: 'Updated Event',
        date_sort: NaN, // Converted to NaN
      });
    });
  });

  describe('deleteEvent', () => {
    it('should delete event successfully', async () => {
      const mockContext = createMockContext();
      mockContext.req.param.mockReturnValue('event-1');
      (eventService.deleteEvent as any).mockResolvedValue(undefined);

      await deleteEvent(mockContext as any);

      expect(eventService.deleteEvent).toHaveBeenCalledWith({}, 'event-1');
      expect(recordLog).toHaveBeenCalledWith(mockContext, {
        action: 'DELETE_EVENT',
        targetType: 'event',
        targetId: 'event-1',
      });
      expect(jsonSuccess).toHaveBeenCalledWith(
        mockContext,
        '展会已删除',
        undefined,
        204
      );
    });
  });
});
