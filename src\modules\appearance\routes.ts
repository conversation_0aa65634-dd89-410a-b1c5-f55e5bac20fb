// 本文件自动迁移生成，后续可按需整理格式
import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import { appearanceSchema } from './schema';
import { getDB } from '@/infrastructure';
import { paginatedResult } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const pubAppearances = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI ----------
const listRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '参展记录查询',
  tags: ['Appearances'],
  deprecated: true,
  request: {
    query: z.object({
      circle_id: z.string().optional(),
      event_id: z.string().optional(),
      page: z.string().optional().openapi({ example: '1' }),
      pageSize: z.string().optional().openapi({ example: '50' }),
    }),
  },
  responses: {
    200: {
      description: '参展记录',
      content: {
        'application/json': { schema: paginatedResult(appearanceSchema) },
      },
    },
  },
});

const detailRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '参展记录详情',
  tags: ['Appearances'],
  request: { params: z.object({ id: z.string() }) },
  responses: {
    200: {
      description: '详情',
      content: { 'application/json': { schema: appearanceSchema } },
    },
    404: { description: 'Not Found' },
  },
});

const createRouteDef = createRoute({
  method: 'post',
  path: '/',
  summary: '创建参展记录',
  tags: ['Appearances'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: appearanceSchema
            .partial()
            .omit({ created_at: true, updated_at: true }),
        },
      },
    },
  },
  responses: {
    200: {
      description: 'success',
      content: {
        'application/json': { schema: z.object({ success: z.boolean() }) },
      },
    },
  },
});

const deleteRoute = createRoute({
  method: 'delete',
  path: '/{id}',
  summary: '删除参展记录',
  tags: ['Appearances'],
  request: { params: z.object({ id: z.string() }) },
  responses: {
    200: {
      description: 'success',
      content: {
        'application/json': { schema: z.object({ success: z.boolean() }) },
      },
    },
  },
});

// ---------- Handlers ----------
async function listHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const circle_id = c.req.query('circle_id');
  const event_id = c.req.query('event_id');

  const search = new URL(c.req.url).searchParams;
  const page = Math.max(Number(search.get('page') || '1'), 1);
  const pageSize = Math.max(Number(search.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  let baseSql = 'FROM appearances';
  const conditions: string[] = [];
  const params: (string | number)[] = [];

  if (circle_id) {
    conditions.push('circle_id = ?');
    params.push(circle_id);
  }
  if (event_id) {
    conditions.push('event_id = ?');
    params.push(event_id);
  }

  if (conditions.length) {
    baseSql += ' WHERE ' + conditions.join(' AND ');
  }

  // --- total count ---
  const totalRes = await db
    .prepare(`SELECT COUNT(*) AS total ${baseSql}`)
    .bind(...params)
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  // --- data ---
  const { results: items } = await db
    .prepare(`SELECT * ${baseSql} ORDER BY created_at DESC LIMIT ? OFFSET ?`)
    .bind(...params, pageSize, offset)
    .all();

  // 若通过 event_id 查询，提示已弃用（建议改用 /events/{id}/appearances）
  if (event_id) {
    c.header('Deprecation', 'true');
  }

  return c.json({ items, total, page, pageSize });
}

async function detailHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const id = c.req.param('id');
  const record = await db
    .prepare('SELECT * FROM appearances WHERE id = ?')
    .bind(id)
    .first();
  if (!record) return jsonError(c, 50001, 'Not Found', 404);
  return c.json(record);
}

async function createHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const body = await c.req.json();
  const { id, circle_id, event_id, artist_id, booth_id, path } = body;
  if (!booth_id) return jsonError(c, 50002, 'booth_id is required', 400);
  await db
    .prepare(
      'INSERT INTO appearances (id, circle_id, event_id, artist_id, booth_id, path) VALUES (?, ?, ?, ?, ?, ?)'
    )
    .bind(id, circle_id, event_id, artist_id, booth_id, path)
    .run();
  return c.json({ success: true });
}

async function deleteHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const id = c.req.param('id');
  await db.prepare('DELETE FROM appearances WHERE id = ?').bind(id).run();
  return c.json({ success: true });
}

// ---------- Register ----------
registerOpenApiRoute(pubAppearances, listRoute, listHandler);
registerOpenApiRoute(pubAppearances, detailRoute, detailHandler);
registerOpenApiRoute(pubAppearances, createRouteDef, createHandler);
registerOpenApiRoute(pubAppearances, deleteRoute, deleteHandler);

export { pubAppearances, pubAppearances as routes };
