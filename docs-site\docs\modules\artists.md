# 作者模块 (Artists Module)

## 概述

作者模块提供作者信息的管理和查询功能，支持多语言显示和作者详情管理。

## 功能特性

- ✅ 多语言作者信息 (en, zh, ja)
- ✅ 作者列表查询和分页
- ✅ 单个作者详情获取
- ✅ 作者描述多语言支持
- ✅ 作者管理 (CRUD操作)

## API端点

### 公开接口

#### GET /artists

**功能**: 获取作者列表

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `50`

**请求头**:

- `X-Locale` (推荐) - 指定显示语言
- `Accept-Language` - 标准语言头部

**响应格式**:

```json
{
  "items": [
    {
      "id": "artist-123",
      "name": "作者名称",
      "description": "作者描述（多语言）",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 50,
  "locale": "zh"
}
```

#### GET /artists/{id}

**功能**: 获取作者详情

**路径参数**:

- `id` (必需) - 作者ID

**查询参数**:

- `lang` (可选) - 指定语言，默认为请求头中的语言

**响应格式**:

```json
{
  "success": true,
  "data": {
    "id": "artist-123",
    "name": "作者名称",
    "description": "作者详细描述（多语言）",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "locale": "zh"
}
```

### 后台管理接口

#### GET /admin/artists

**功能**: 后台获取作者列表

**权限要求**: `admin` 或 `editor` 角色

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `50`

#### POST /admin/artists

**功能**: 创建新作者

**权限要求**: `admin` 或 `editor` 角色

**请求体**:

```json
{
  "name": "新作者名称",
  "description": "作者描述"
}
```

#### PUT /admin/artists/{id}

**功能**: 更新作者信息

**权限要求**: `admin` 或 `editor` 角色

#### DELETE /admin/artists/{id}

**功能**: 删除作者

**权限要求**: `admin` 角色

## 数据结构

### Artist Schema

```typescript
interface Artist {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}
```

### 多语言支持

作者描述支持多语言，通过 `artist_translations` 表存储：

```sql
CREATE TABLE artist_translations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  artist_id TEXT NOT NULL,
  locale TEXT NOT NULL,
  description TEXT,
  FOREIGN KEY (artist_id) REFERENCES artists(id),
  UNIQUE(artist_id, locale)
);
```

## 使用示例

### 获取作者列表

```bash
curl -X GET "https://api.example.com/artists?page=1&pageSize=20" \
  -H "X-Locale: zh"
```

### 获取作者详情

```bash
curl -X GET "https://api.example.com/artists/artist-123?lang=ja" \
  -H "X-Locale: ja"
```

### 创建新作者（需要认证）

```bash
curl -X POST "https://api.example.com/admin/artists" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "新作者",
    "description": "作者描述"
  }'
```

## 缓存策略

- **缓存键格式**: `artists:list:{page}:{pageSize}:{locale}`
- **TTL**: 30分钟
- **缓存更新**: 作者信息变更时自动清除相关缓存

## 性能优化

1. **分页查询**: 支持分页减少单次查询数据量
2. **多语言缓存**: 按语言分别缓存，避免重复查询
3. **索引优化**: 在 `name` 字段上建立索引提高查询性能

## 限制和注意事项

1. **作者名称**: 必须唯一，不能为空
2. **描述长度**: 最大支持 2000 字符
3. **多语言**: 目前支持 en、zh、ja 三种语言
4. **删除限制**: 如果作者有关联的出展记录，不能直接删除

## 未来规划

- [ ] 作者头像上传功能
- [ ] 作者社交媒体链接
- [ ] 作者作品集成
- [ ] 作者关注功能
- [ ] 作者统计信息

---

_最后更新: 2025-07-28_
