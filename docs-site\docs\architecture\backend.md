﻿# 后端架构（Backend Architecture）

Ayafeed API 采用 **分层 + DDD 实践** 的轻量架构。

```mermaid
graph TD
  req[HTTP Request] --> C(Controller)
  C --> S(Service)
  S --> R[Repository]
  R --> DB[(Cloudflare D1)]
  S --> INFRA[Infrastructure]
```

## 目录映射

| 层             | 目录                                                 | 说明                      |
| -------------- | ---------------------------------------------------- | ------------------------- |
| Controller     | `src/controllers/*`                                  | Hono 路由入口，解析参数   |
| Service        | `src/services/*` / `src/modules/*/service.ts`        | 纯业务逻辑，无 IO         |
| Repository     | `src/repositories/*` / `src/modules/*/repository.ts` | 直接访问数据库            |
| Infrastructure | `src/infrastructure/*`                               | Logger、Cache、第三方请求 |
| Schema         | `src/schemas/*`                                      | Zod / OpenAPI 类型        |

## 关注点分离

- **Controller** 仅做 `HTTP -> Service` 映射。
- **Service** 不依赖 Controller；通过依赖注入获取 `logger`, `cache`, `db`。
- **Repository** 封装 SQL，集中管理表结构变更。
- **Infrastructure** 可插拔实现，便于本地及云环境切换。

## 性能优化

- 查询均具备索引与分页。
- 热门查询使用 KV 缓存。
- 日志写入异步队列，减少阻塞。
