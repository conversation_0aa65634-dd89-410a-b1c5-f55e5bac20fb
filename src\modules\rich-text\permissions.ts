import type { EntityType, LanguageCode, ContentTypeConfig } from './schema';

/**
 * 富文本标签页权限管理器
 * 处理用户权限验证和访问控制
 */
export class RichTextPermissionManager {
  // ========================================
  // 权限定义
  // ========================================

  static readonly PERMISSIONS = {
    // 配置管理权限
    CONFIG_READ: 'rich_text:config:read',
    CONFIG_CREATE: 'rich_text:config:create',
    CONFIG_UPDATE: 'rich_text:config:update',
    CONFIG_DELETE: 'rich_text:config:delete',
    CONFIG_RESTORE: 'rich_text:config:restore',
    CONFIG_MANAGE_PRESETS: 'rich_text:config:manage_presets',

    // 内容管理权限
    CONTENT_READ: 'rich_text:content:read',
    CONTENT_CREATE: 'rich_text:content:create',
    CONTENT_UPDATE: 'rich_text:content:update',
    CONTENT_DELETE: 'rich_text:content:delete',

    // 批量操作权限
    BATCH_IMPORT: 'rich_text:batch:import',
    BATCH_EXPORT: 'rich_text:batch:export',
    BATCH_SYNC: 'rich_text:batch:sync',

    // 管理员权限
    ADMIN_FULL_ACCESS: 'rich_text:admin:full_access',
  } as const;

  static readonly ROLE_PERMISSIONS = {
    // 只读用户
    viewer: [this.PERMISSIONS.CONFIG_READ, this.PERMISSIONS.CONTENT_READ],

    // 内容编辑者
    editor: [
      this.PERMISSIONS.CONFIG_READ,
      this.PERMISSIONS.CONTENT_READ,
      this.PERMISSIONS.CONTENT_CREATE,
      this.PERMISSIONS.CONTENT_UPDATE,
    ],

    // 配置管理者
    manager: [
      this.PERMISSIONS.CONFIG_READ,
      this.PERMISSIONS.CONFIG_CREATE,
      this.PERMISSIONS.CONFIG_UPDATE,
      this.PERMISSIONS.CONFIG_DELETE,
      this.PERMISSIONS.CONFIG_RESTORE,
      this.PERMISSIONS.CONTENT_READ,
      this.PERMISSIONS.CONTENT_CREATE,
      this.PERMISSIONS.CONTENT_UPDATE,
      this.PERMISSIONS.CONTENT_DELETE,
      this.PERMISSIONS.BATCH_IMPORT,
      this.PERMISSIONS.BATCH_EXPORT,
    ],

    // 系统管理员
    admin: [
      this.PERMISSIONS.ADMIN_FULL_ACCESS,
      this.PERMISSIONS.CONFIG_MANAGE_PRESETS,
      this.PERMISSIONS.BATCH_SYNC,
    ],
  } as const;

  // ========================================
  // 权限检查方法
  // ========================================

  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(
    userPermissions: string[],
    requiredPermission: string
  ): boolean {
    // 管理员拥有所有权限
    if (userPermissions.includes(this.PERMISSIONS.ADMIN_FULL_ACCESS)) {
      return true;
    }

    return userPermissions.includes(requiredPermission);
  }

  /**
   * 检查用户是否有多个权限中的任意一个
   */
  static hasAnyPermission(
    userPermissions: string[],
    requiredPermissions: string[]
  ): boolean {
    return requiredPermissions.some((permission) =>
      this.hasPermission(userPermissions, permission)
    );
  }

  /**
   * 检查用户是否有所有指定权限
   */
  static hasAllPermissions(
    userPermissions: string[],
    requiredPermissions: string[]
  ): boolean {
    return requiredPermissions.every((permission) =>
      this.hasPermission(userPermissions, permission)
    );
  }

  // ========================================
  // 实体级权限检查
  // ========================================

  /**
   * 检查用户是否可以访问指定实体类型
   */
  static canAccessEntityType(
    userPermissions: string[],
    entityType: EntityType,
    action: 'read' | 'write'
  ): boolean {
    // 基础权限检查
    const basePermission =
      action === 'read'
        ? this.PERMISSIONS.CONFIG_READ
        : this.PERMISSIONS.CONFIG_UPDATE;

    if (!this.hasPermission(userPermissions, basePermission)) {
      return false;
    }

    // 实体特定权限检查
    const entityPermission = `rich_text:entity:${entityType}:${action}`;

    // 如果没有实体特定权限，则检查是否有管理员权限
    return (
      this.hasPermission(userPermissions, entityPermission) ||
      this.hasPermission(userPermissions, this.PERMISSIONS.ADMIN_FULL_ACCESS)
    );
  }

  /**
   * 检查用户是否可以访问指定语言
   */
  static canAccessLanguage(
    userPermissions: string[],
    languageCode: LanguageCode,
    action: 'read' | 'write'
  ): boolean {
    // 语言特定权限检查
    const languagePermission = `rich_text:language:${languageCode}:${action}`;

    return (
      this.hasPermission(userPermissions, languagePermission) ||
      this.hasPermission(userPermissions, this.PERMISSIONS.ADMIN_FULL_ACCESS)
    );
  }

  // ========================================
  // 操作级权限检查
  // ========================================

  /**
   * 检查配置操作权限
   */
  static canPerformConfigOperation(
    userPermissions: string[],
    operation: ConfigOperation,
    config?: ContentTypeConfig
  ): PermissionResult {
    switch (operation) {
      case 'read':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONFIG_READ
        );

      case 'create':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONFIG_CREATE
        );

      case 'update':
        if (config?.is_preset) {
          return this.checkPermission(
            userPermissions,
            this.PERMISSIONS.CONFIG_MANAGE_PRESETS
          );
        }
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONFIG_UPDATE
        );

      case 'delete':
        if (config?.is_preset) {
          return {
            allowed: false,
            reason: '预设配置不能删除',
            code: 'PRESET_CANNOT_DELETE',
          };
        }
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONFIG_DELETE
        );

      case 'restore':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONFIG_RESTORE
        );

      default:
        return {
          allowed: false,
          reason: '未知操作类型',
          code: 'UNKNOWN_OPERATION',
        };
    }
  }

  /**
   * 检查内容操作权限
   */
  static canPerformContentOperation(
    userPermissions: string[],
    operation: ContentOperation,
    entityType: EntityType,
    languageCode: LanguageCode
  ): PermissionResult {
    // 首先检查实体和语言访问权限
    const entityAccess = this.canAccessEntityType(
      userPermissions,
      entityType,
      operation === 'read' ? 'read' : 'write'
    );

    if (!entityAccess) {
      return {
        allowed: false,
        reason: `没有访问 ${entityType} 实体的权限`,
        code: 'ENTITY_ACCESS_DENIED',
      };
    }

    const languageAccess = this.canAccessLanguage(
      userPermissions,
      languageCode,
      operation === 'read' ? 'read' : 'write'
    );

    if (!languageAccess) {
      return {
        allowed: false,
        reason: `没有访问 ${languageCode} 语言的权限`,
        code: 'LANGUAGE_ACCESS_DENIED',
      };
    }

    // 检查具体操作权限
    switch (operation) {
      case 'read':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONTENT_READ
        );

      case 'create':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONTENT_CREATE
        );

      case 'update':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONTENT_UPDATE
        );

      case 'delete':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.CONTENT_DELETE
        );

      default:
        return {
          allowed: false,
          reason: '未知操作类型',
          code: 'UNKNOWN_OPERATION',
        };
    }
  }

  /**
   * 检查批量操作权限
   */
  static canPerformBatchOperation(
    userPermissions: string[],
    operation: BatchOperation
  ): PermissionResult {
    switch (operation) {
      case 'import':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.BATCH_IMPORT
        );

      case 'export':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.BATCH_EXPORT
        );

      case 'sync':
        return this.checkPermission(
          userPermissions,
          this.PERMISSIONS.BATCH_SYNC
        );

      default:
        return {
          allowed: false,
          reason: '未知批量操作类型',
          code: 'UNKNOWN_BATCH_OPERATION',
        };
    }
  }

  // ========================================
  // 数据过滤
  // ========================================

  /**
   * 根据用户权限过滤配置列表
   */
  static filterConfigsByPermissions(
    configs: ContentTypeConfig[],
    userPermissions: string[]
  ): ContentTypeConfig[] {
    return configs.filter((config) => {
      // 检查是否可以读取该配置
      const canRead = this.canPerformConfigOperation(
        userPermissions,
        'read',
        config
      );

      return canRead.allowed;
    });
  }

  /**
   * 根据用户权限过滤实体类型
   */
  static filterEntityTypesByPermissions(
    entityTypes: EntityType[],
    userPermissions: string[]
  ): EntityType[] {
    return entityTypes.filter((entityType) =>
      this.canAccessEntityType(userPermissions, entityType, 'read')
    );
  }

  /**
   * 根据用户权限过滤语言列表
   */
  static filterLanguagesByPermissions(
    languages: LanguageCode[],
    userPermissions: string[]
  ): LanguageCode[] {
    return languages.filter((language) =>
      this.canAccessLanguage(userPermissions, language, 'read')
    );
  }

  // ========================================
  // 辅助方法
  // ========================================

  /**
   * 基础权限检查
   */
  private static checkPermission(
    userPermissions: string[],
    requiredPermission: string
  ): PermissionResult {
    const allowed = this.hasPermission(userPermissions, requiredPermission);

    return {
      allowed,
      reason: allowed ? undefined : '权限不足',
      code: allowed ? undefined : 'INSUFFICIENT_PERMISSIONS',
    };
  }

  /**
   * 从角色获取权限列表
   */
  static getPermissionsFromRoles(roles: UserRole[]): string[] {
    const permissions = new Set<string>();

    for (const role of roles) {
      const rolePermissions = this.ROLE_PERMISSIONS[role];
      if (rolePermissions) {
        rolePermissions.forEach((permission) => permissions.add(permission));
      }
    }

    return Array.from(permissions);
  }

  /**
   * 创建权限上下文
   */
  static createPermissionContext(
    userPermissions: string[],
    entityType?: EntityType,
    languageCode?: LanguageCode
  ): PermissionContext {
    return {
      userPermissions,
      entityType,
      languageCode,
      canRead: (operation: string) =>
        this.hasPermission(userPermissions, operation),
      canWrite: (operation: string) =>
        this.hasPermission(userPermissions, operation),
      isAdmin: () =>
        this.hasPermission(userPermissions, this.PERMISSIONS.ADMIN_FULL_ACCESS),
    };
  }
}

// ========================================
// 类型定义
// ========================================

type ConfigOperation = 'read' | 'create' | 'update' | 'delete' | 'restore';
type ContentOperation = 'read' | 'create' | 'update' | 'delete';
type BatchOperation = 'import' | 'export' | 'sync';
type UserRole = 'viewer' | 'editor' | 'manager' | 'admin';

interface PermissionResult {
  allowed: boolean;
  reason?: string;
  code?: string;
}

interface PermissionContext {
  userPermissions: string[];
  entityType?: EntityType;
  languageCode?: LanguageCode;
  canRead: (operation: string) => boolean;
  canWrite: (operation: string) => boolean;
  isAdmin: () => boolean;
}
