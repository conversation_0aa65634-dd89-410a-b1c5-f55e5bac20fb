import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

import { FetchClient } from '@/infrastructure/third-party';

/**
 * FetchClient 单元测试
 */

describe('FetchClient', () => {
  const fetchClient = new FetchClient();
  let originalFetch: typeof globalThis.fetch;

  beforeEach(() => {
    originalFetch = globalThis.fetch;
  });

  afterEach(() => {
    // 恢复原始 fetch
    globalThis.fetch = originalFetch;
    vi.restoreAllMocks();
  });

  it('should parse JSON response', async () => {
    const mockResponse = {
      ok: true,
      text: vi.fn().mockResolvedValue('{"foo":"bar"}'),
    } as any;
    const fetchSpy = vi.fn().mockResolvedValue(mockResponse);
    // @ts-ignore
    globalThis.fetch = fetchSpy;

    const data = await fetchClient.request<{ foo: string }>({
      method: 'GET',
      url: 'https://api.example.com',
    });

    expect(fetchSpy).toHaveBeenCalled();
    expect(data).toEqual({ foo: 'bar' });
  });

  it('should return plain text when response is not JSON', async () => {
    const mockResponse = {
      ok: true,
      text: vi.fn().mockResolvedValue('plain-text'),
    } as any;
    const fetchSpy = vi.fn().mockResolvedValue(mockResponse);
    // @ts-ignore
    globalThis.fetch = fetchSpy;

    const data = await fetchClient.request<string>({
      method: 'GET',
      url: 'https://api.example.com',
    });

    expect(data).toBe('plain-text');
  });

  it('should throw error for non-ok response', async () => {
    const mockResponse = {
      ok: false,
      status: 500,
      text: vi.fn().mockResolvedValue('error'),
    } as any;
    const fetchSpy = vi.fn().mockResolvedValue(mockResponse);
    // @ts-ignore
    globalThis.fetch = fetchSpy;

    await expect(
      fetchClient.request({ method: 'GET', url: 'https://api.example.com' })
    ).rejects.toThrowError(/ThirdPartyClient/);
  });
});
