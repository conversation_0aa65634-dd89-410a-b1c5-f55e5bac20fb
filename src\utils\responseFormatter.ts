import type { Context } from 'hono';
import type { Locale } from '@/middlewares/locale';

/**
 * 标准化的API响应格式
 */
export interface StandardResponse<T = any> {
  success: boolean;
  data: T;
  locale: string;
  timestamp: string;
  meta?: Record<string, any>;
}

/**
 * 创建标准化的成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  locale: Locale,
  meta?: Record<string, any>
): StandardResponse<T> {
  return {
    success: true,
    data,
    locale,
    timestamp: new Date().toISOString(),
    ...(meta && { meta }),
  };
}

/**
 * 创建标准化的分页响应
 */
export function createPaginatedResponse<T>(
  items: T[],
  total: number,
  page: number,
  pageSize: number,
  locale: Locale
): StandardResponse<T[]> {
  return createSuccessResponse(items, locale, {
    total,
    page,
    pageSize,
    hasMore: items.length === pageSize && page * pageSize < total,
  });
}

/**
 * 发送标准化的JSON响应
 */
export function jsonWithLocale<T>(
  c: Context,
  data: T,
  meta?: Record<string, any>
): Response {
  const locale = (c.get('locale') as Locale) || 'en';
  return c.json(createSuccessResponse(data, locale, meta));
}

/**
 * 发送标准化的分页JSON响应
 */
export function jsonPaginatedWithLocale<T>(
  c: Context,
  items: T[],
  total: number,
  page: number,
  pageSize: number
): Response {
  const locale = (c.get('locale') as Locale) || 'en';
  return c.json(createPaginatedResponse(items, total, page, pageSize, locale));
}
