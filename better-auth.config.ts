/**
 * Better Auth CLI configuration file for Cloudflare D1
 *
 * Docs: https://www.better-auth.com/docs/concepts/cli
 */
import { drizzle } from 'drizzle-orm/d1';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { betterAuth } from 'better-auth';
import { betterAuthOptions } from './src/lib/better-auth/options';

// 为 CLI 生成使用模拟的 D1 数据库
const mockD1 = {
  prepare: () => ({
    bind: () => ({
      run: async () => ({ success: true }),
      all: async () => ({ results: [] }),
      first: async () => null,
    }),
  }),
  batch: async () => [{ success: true }],
  exec: async () => ({ results: [] }),
} as any;

const db = drizzle(mockD1);

export const auth: ReturnType<typeof betterAuth> = betterAuth({
  ...betterAuthOptions,
  database: drizzleAdapter(db, { provider: 'sqlite' }), // D1 基于 SQLite
  baseURL: process.env.BETTER_AUTH_URL || 'http://localhost:8787',
  secret: process.env.BETTER_AUTH_SECRET || 'cli-secret',
});
