import { z } from '@hono/zod-openapi';

// 后台统计响应 Schema
export const statsResponseSchema = z.object({
  totals: z.object({
    circles: z.number().openapi({ example: 123 }),
    artists: z.number().openapi({ example: 456 }),
    events: z.number().openapi({ example: 78 }),
  }),
  year: z.number().openapi({ example: 2025 }),
  eventsByMonth: z.array(
    z.object({
      month: z.string().openapi({ example: '01' }),
      count: z.number().openapi({ example: 12 }),
    })
  ),
});

export type StatsResponse = z.infer<typeof statsResponseSchema>;
