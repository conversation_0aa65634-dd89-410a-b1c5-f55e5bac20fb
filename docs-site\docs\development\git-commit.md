﻿# Git Commit Message Guide

遵循 [Conventional Commits](https://www.conventionalcommits.org/zh-hans/) 规范，并结合仓库 COMMIT RULE：

- 类型（type）
  - feat: 新功能
  - fix: Bug 修复
  - docs: 文档变更
  - style: 格式调整，不影响逻辑
  - refactor: 重构，无功能变化
  - test: 测试相关
  - chore: 构建/脚手架/依赖更新
- 作用域（scope）可选，例如 uth、docs。
- Header 不得超过 72 字符，其余部分建议 72~100 字符换行。

示例：

`
feat(auth): 新增 JWT 登录接口

- 支持基于邮箱 + 密码登录
- 返回 AccessToken 与 RefreshToken

BREAKING CHANGE: 移除旧 /login/basic 接口
`
