import type { EntityType, LanguageCode, ContentTypeConfig } from './schema';
import type {
  ContentTypeConfigRepository,
  RichTextContentRepository,
} from './repository-new';
// import { PresetProtectionManager } from './preset-manager';
import { RichTextI18nManager } from './i18n';

/**
 * 批量操作服务
 * 处理配置和内容的批量操作
 */
export class BatchOperationsService {
  constructor(
    private configRepo: ContentTypeConfigRepository,
    private contentRepo: RichTextContentRepository
  ) {}

  // ========================================
  // 配置批量操作
  // ========================================

  /**
   * 批量导入配置
   */
  async importConfigs(
    entityType: EntityType,
    languageCode: LanguageCode,
    configs: ImportConfigData[],
    options: ImportOptions = {}
  ): Promise<ImportResult> {
    const results: ImportResult = {
      success: [],
      failed: [],
      skipped: [],
      summary: {
        total: configs.length,
        imported: 0,
        failed: 0,
        skipped: 0,
      },
    };

    // 获取现有配置
    const existingConfigs = await this.configRepo.getAllConfigs(
      entityType,
      languageCode,
      true // 包含已删除的
    );

    for (const configData of configs) {
      try {
        const importResult = await this.importSingleConfig(
          entityType,
          languageCode,
          configData,
          existingConfigs,
          options
        );

        if (importResult.action === 'IMPORTED') {
          results.success.push({
            key: configData.key,
            action: importResult.action,
            config: importResult.config!,
          });
          results.summary.imported++;
        } else if (importResult.action === 'SKIPPED') {
          results.skipped.push({
            key: configData.key,
            reason: importResult.reason!,
          });
          results.summary.skipped++;
        }
      } catch (error) {
        results.failed.push({
          key: configData.key,
          error: error instanceof Error ? error.message : '未知错误',
        });
        results.summary.failed++;
      }
    }

    return results;
  }

  /**
   * 批量导出配置
   */
  async exportConfigs(
    entityType: EntityType,
    languageCode: LanguageCode,
    options: ExportOptions = {}
  ): Promise<ExportResult> {
    const configs = await this.configRepo.getAllConfigs(
      entityType,
      languageCode,
      options.includeDeleted || false
    );

    const exportData: ExportConfigData[] = configs
      .filter((config) => {
        if (options.excludePresets && config.is_preset) {
          return false;
        }
        if (options.activeOnly && !config.is_active) {
          return false;
        }
        return true;
      })
      .map((config) => ({
        key: config.key,
        label: config.label,
        placeholder: config.placeholder || '',
        icon: config.icon || '',
        sort_order: config.sort_order,
        is_active: config.is_active,
        is_preset: config.is_preset,
      }));

    return {
      entity_type: entityType,
      language_code: languageCode,
      exported_at: new Date().toISOString(),
      configs: exportData,
      metadata: {
        total_count: exportData.length,
        active_count: exportData.filter((c) => c.is_active).length,
        preset_count: exportData.filter((c) => c.is_preset).length,
      },
    };
  }

  /**
   * 跨语言同步配置
   */
  async syncConfigsAcrossLanguages(
    entityType: EntityType,
    sourceLanguage: LanguageCode,
    targetLanguages: LanguageCode[],
    options: SyncOptions = {}
  ): Promise<SyncResult> {
    const sourceConfigs = await this.configRepo.getActiveConfigs(
      entityType,
      sourceLanguage
    );

    const results: SyncResult = {
      source_language: sourceLanguage,
      target_languages: targetLanguages,
      synced: [],
      failed: [],
      summary: {
        total_configs: sourceConfigs.length,
        total_targets: targetLanguages.length,
        synced_count: 0,
        failed_count: 0,
      },
    };

    for (const targetLang of targetLanguages) {
      try {
        const syncResult = await this.syncConfigsToLanguage(
          entityType,
          sourceConfigs,
          targetLang,
          options
        );

        results.synced.push({
          language: targetLang,
          configs: syncResult.configs,
          created_count: syncResult.created_count,
          updated_count: syncResult.updated_count,
        });

        results.summary.synced_count += syncResult.configs.length;
      } catch (error) {
        results.failed.push({
          language: targetLang,
          error: error instanceof Error ? error.message : '未知错误',
        });
        results.summary.failed_count++;
      }
    }

    return results;
  }

  // ========================================
  // 内容批量操作
  // ========================================

  /**
   * 批量导入内容
   */
  async importContents(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contents: Record<string, string>,
    _options: ContentImportOptions = {}
  ): Promise<ContentImportResult> {
    const results: ContentImportResult = {
      entity_type: entityType,
      entity_id: entityId,
      language_code: languageCode,
      success: [],
      failed: [],
      summary: {
        total: Object.keys(contents).length,
        imported: 0,
        failed: 0,
      },
    };

    for (const [contentType, content] of Object.entries(contents)) {
      try {
        const result = await this.contentRepo.upsertContent(
          entityType,
          entityId,
          languageCode,
          contentType,
          content
        );

        results.success.push({
          content_type: contentType,
          content_id: result.id,
          action: 'IMPORTED',
        });
        results.summary.imported++;
      } catch (error) {
        results.failed.push({
          content_type: contentType,
          error: error instanceof Error ? error.message : '未知错误',
        });
        results.summary.failed++;
      }
    }

    return results;
  }

  /**
   * 批量导出内容
   */
  async exportContents(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode
  ): Promise<ContentExportResult> {
    const contents = await this.contentRepo.getContentsByEntity(
      entityType,
      entityId,
      languageCode
    );

    const exportData: Record<string, string> = {};
    for (const content of contents) {
      exportData[content.content_type] = content.content;
    }

    return {
      entity_type: entityType,
      entity_id: entityId,
      language_code: languageCode,
      exported_at: new Date().toISOString(),
      contents: exportData,
      metadata: {
        content_count: contents.length,
        content_types: Object.keys(exportData),
      },
    };
  }

  /**
   * 跨实体复制内容
   */
  async copyContentsAcrossEntities(
    sourceEntityType: EntityType,
    sourceEntityId: string,
    targetEntityType: EntityType,
    targetEntityId: string,
    languageCode: LanguageCode,
    options: CopyOptions = {}
  ): Promise<CopyResult> {
    const sourceContents = await this.contentRepo.getContentsByEntity(
      sourceEntityType,
      sourceEntityId,
      languageCode
    );

    const results: CopyResult = {
      source: { entity_type: sourceEntityType, entity_id: sourceEntityId },
      target: { entity_type: targetEntityType, entity_id: targetEntityId },
      language_code: languageCode,
      copied: [],
      failed: [],
      summary: {
        total: sourceContents.length,
        copied: 0,
        failed: 0,
      },
    };

    for (const sourceContent of sourceContents) {
      try {
        // 检查目标实体类型是否支持该内容类型
        const targetConfigs = await this.configRepo.getActiveConfigs(
          targetEntityType,
          languageCode
        );

        const hasMatchingConfig = targetConfigs.some(
          (config) => config.key === sourceContent.content_type
        );

        if (!hasMatchingConfig && !options.createMissingConfigs) {
          results.failed.push({
            content_type: sourceContent.content_type,
            error: '目标实体类型不支持此内容类型',
          });
          results.summary.failed++;
          continue;
        }

        const result = await this.contentRepo.upsertContent(
          targetEntityType,
          targetEntityId,
          languageCode,
          sourceContent.content_type,
          sourceContent.content
        );

        results.copied.push({
          content_type: sourceContent.content_type,
          content_id: result.id,
        });
        results.summary.copied++;
      } catch (error) {
        results.failed.push({
          content_type: sourceContent.content_type,
          error: error instanceof Error ? error.message : '未知错误',
        });
        results.summary.failed++;
      }
    }

    return results;
  }

  // ========================================
  // 辅助方法
  // ========================================

  private async importSingleConfig(
    entityType: EntityType,
    languageCode: LanguageCode,
    configData: ImportConfigData,
    existingConfigs: ContentTypeConfig[],
    options: ImportOptions
  ): Promise<SingleImportResult> {
    // 检查是否已存在
    const existing = existingConfigs.find((c) => c.key === configData.key);

    if (existing && !options.overwriteExisting) {
      return {
        action: 'SKIPPED',
        reason: '配置已存在且未启用覆盖模式',
      };
    }

    // 创建或更新配置
    const configToSave = {
      entity_type: entityType,
      language_code: languageCode,
      key: configData.key,
      label: configData.label,
      placeholder: configData.placeholder,
      icon: configData.icon,
      sort_order: configData.sort_order,
      is_active: configData.is_active,
      is_preset: configData.is_preset || false,
    };

    let result: ContentTypeConfig;

    if (existing) {
      result = await this.configRepo.updateConfig(existing.id, configToSave);
    } else {
      result = await this.configRepo.createConfig(configToSave);
    }

    return {
      action: 'IMPORTED',
      config: result,
    };
  }

  private async syncConfigsToLanguage(
    entityType: EntityType,
    sourceConfigs: ContentTypeConfig[],
    targetLanguage: LanguageCode,
    options: SyncOptions
  ): Promise<LanguageSyncResult> {
    const targetConfigs = await this.configRepo.getAllConfigs(
      entityType,
      targetLanguage,
      true
    );

    const results: LanguageSyncResult = {
      configs: [],
      created_count: 0,
      updated_count: 0,
    };

    for (const sourceConfig of sourceConfigs) {
      if (sourceConfig.is_preset && !options.includePresets) {
        continue; // 跳过预设配置
      }

      const existing = targetConfigs.find((c) => c.key === sourceConfig.key);

      // 生成目标语言的配置
      const crossLangConfigs = RichTextI18nManager.generateCrossLanguageConfigs(
        {
          entity_type: entityType,
          language_code: sourceConfig.language_code,
          key: sourceConfig.key,
          icon: sourceConfig.icon,
          sort_order: sourceConfig.sort_order,
          is_active: sourceConfig.is_active,
        }
      );

      const targetConfig = crossLangConfigs.find(
        (c) => c.language_code === targetLanguage
      );
      if (!targetConfig) continue;

      let result: ContentTypeConfig;

      if (existing) {
        result = await this.configRepo.updateConfig(existing.id, targetConfig);
        results.updated_count++;
      } else {
        result = await this.configRepo.createConfig(targetConfig);
        results.created_count++;
      }

      results.configs.push(result);
    }

    return results;
  }
}

// ========================================
// 类型定义
// ========================================

interface ImportConfigData {
  key: string;
  label: string;
  placeholder: string;
  icon: string;
  sort_order: number;
  is_active: boolean;
  is_preset?: boolean;
}

interface ExportConfigData extends ImportConfigData {
  is_preset: boolean;
}

interface ImportOptions {
  overwriteExisting?: boolean;
  skipPresets?: boolean;
}

interface ExportOptions {
  includeDeleted?: boolean;
  excludePresets?: boolean;
  activeOnly?: boolean;
}

interface SyncOptions {
  includePresets?: boolean;
  overwriteExisting?: boolean;
}

interface ContentImportOptions {
  overwriteExisting?: boolean;
}

interface CopyOptions {
  createMissingConfigs?: boolean;
  overwriteExisting?: boolean;
}

interface ImportResult {
  success: Array<{
    key: string;
    action: string;
    config: ContentTypeConfig;
  }>;
  failed: Array<{
    key: string;
    error: string;
  }>;
  skipped: Array<{
    key: string;
    reason: string;
  }>;
  summary: {
    total: number;
    imported: number;
    failed: number;
    skipped: number;
  };
}

interface ExportResult {
  entity_type: EntityType;
  language_code: LanguageCode;
  exported_at: string;
  configs: ExportConfigData[];
  metadata: {
    total_count: number;
    active_count: number;
    preset_count: number;
  };
}

interface SyncResult {
  source_language: LanguageCode;
  target_languages: LanguageCode[];
  synced: Array<{
    language: LanguageCode;
    configs: ContentTypeConfig[];
    created_count: number;
    updated_count: number;
  }>;
  failed: Array<{
    language: LanguageCode;
    error: string;
  }>;
  summary: {
    total_configs: number;
    total_targets: number;
    synced_count: number;
    failed_count: number;
  };
}

interface ContentImportResult {
  entity_type: EntityType;
  entity_id: string;
  language_code: LanguageCode;
  success: Array<{
    content_type: string;
    content_id: string;
    action: string;
  }>;
  failed: Array<{
    content_type: string;
    error: string;
  }>;
  summary: {
    total: number;
    imported: number;
    failed: number;
  };
}

interface ContentExportResult {
  entity_type: EntityType;
  entity_id: string;
  language_code: LanguageCode;
  exported_at: string;
  contents: Record<string, string>;
  metadata: {
    content_count: number;
    content_types: string[];
  };
}

interface CopyResult {
  source: { entity_type: EntityType; entity_id: string };
  target: { entity_type: EntityType; entity_id: string };
  language_code: LanguageCode;
  copied: Array<{
    content_type: string;
    content_id: string;
  }>;
  failed: Array<{
    content_type: string;
    error: string;
  }>;
  summary: {
    total: number;
    copied: number;
    failed: number;
  };
}

interface SingleImportResult {
  action: 'IMPORTED' | 'SKIPPED';
  config?: ContentTypeConfig;
  reason?: string;
}

interface LanguageSyncResult {
  configs: ContentTypeConfig[];
  created_count: number;
  updated_count: number;
}
