import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createContentService } from '../service';

// Mock dependencies
const mockDB = {} as any;
const mockR2 = {
  put: vi.fn().mockResolvedValue(undefined),
} as any;

describe('Rich Text Image Upload', () => {
  let contentService: ReturnType<typeof createContentService>;

  beforeEach(() => {
    vi.clearAllMocks();
    contentService = createContentService(mockDB);
  });

  describe('uploadImage', () => {
    it('should upload valid image file successfully', async () => {
      // 创建模拟的图片文件（确保大小超过100字节）
      const mockFile = new File(['x'.repeat(200)], 'test.jpg', {
        type: 'image/jpeg',
      });

      const result = await contentService.uploadImage(mockFile, mockR2);

      expect(result).toHaveProperty('url');
      expect(result.url).toMatch(/^\/images\/content\/\d+_[a-f0-9-]+\.jpg$/);
      expect(mockR2.put).toHaveBeenCalledTimes(1);
    });

    it('should reject invalid file type', async () => {
      const mockFile = new File(['fake content'], 'test.txt', {
        type: 'text/plain',
      });

      await expect(
        contentService.uploadImage(mockFile, mockR2)
      ).rejects.toThrow('不支持的文件类型');
    });

    it('should reject oversized file', async () => {
      // 创建一个超过5MB的模拟文件
      const mockFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', {
        type: 'image/jpeg',
      });

      await expect(
        contentService.uploadImage(mockFile, mockR2)
      ).rejects.toThrow('文件大小超过限制');
    });

    it('should reject file with invalid extension', async () => {
      const mockFile = new File(['fake content'], 'test.exe', {
        type: 'image/jpeg', // MIME类型正确但扩展名错误
      });

      await expect(
        contentService.uploadImage(mockFile, mockR2)
      ).rejects.toThrow('不支持的文件扩展名');
    });

    it('should reject too small file', async () => {
      const mockFile = new File(['x'], 'tiny.jpg', {
        type: 'image/jpeg',
      });

      await expect(
        contentService.uploadImage(mockFile, mockR2)
      ).rejects.toThrow('文件大小过小');
    });

    it('should handle different image formats', async () => {
      const formats = [
        { name: 'test.png', type: 'image/png' },
        { name: 'test.webp', type: 'image/webp' },
        { name: 'test.svg', type: 'image/svg+xml' },
      ];

      for (const format of formats) {
        const mockFile = new File(['x'.repeat(200)], format.name, {
          type: format.type,
        });

        const result = await contentService.uploadImage(mockFile, mockR2);
        expect(result.url).toContain('/images/content/');
      }
    });
  });
});
