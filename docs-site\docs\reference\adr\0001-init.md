﻿# ADR 0001: 技术栈与架构基线

## 状态

Accepted – 2025-07-18

## 上下文

项目需要在 Cloudflare 边缘运行，具备高并发、高可维护性，同时开发团队规模 ≤ 5。

## 决策

1. **运行时**：采用 Cloudflare Workers + D1 数据库。
2. **框架**：选用 Hono（轻量、边缘友好、Zod 集成）。
3. **语言**：TypeScript，ESM 模式。
4. **架构**：Controller → Service → Repository 三层 + Infrastructure 注入。
5. **CI / CD**：GitHub Actions + Wrangler Deploy。
6. **API 契约**：Zod Schema 生成 OpenAPI，自动类型同步。

## 后果

- 本地开发需 Node 20+ 与 Wrangler CLI。
- 迁移到其他平台（如 Vercel Edge）成本低。
- OpenAPI 与代码同步，减少漂移风险。

## 备选方案

- Express.js + Docker – 打包体积大，不适合边缘
- Remix Workers – 重 Web，后端 API 不合适
