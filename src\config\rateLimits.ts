import {
  createRateLimit,
  createRoleBasedRateLimit,
} from '../middlewares/rateLimit';

/**
 * 速率限制配置中心
 * 统一管理所有API的速率限制策略
 */

// 时间窗口常量（毫秒）
export const TIME_WINDOWS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
} as const;

// 认证相关接口的速率限制
export const authRateLimits = {
  // 敏感操作：登录、注册、重置密码
  sensitive: createRateLimit(5, TIME_WINDOWS.MINUTE),

  // 状态检查：/auth/me 等频繁调用的接口
  status: createRateLimit(30, TIME_WINDOWS.MINUTE),

  // 登出操作
  logout: createRateLimit(10, TIME_WINDOWS.MINUTE),
} as const;

// 管理员接口的基于角色的速率限制
export const adminRateLimit = createRoleBasedRateLimit(
  {
    admin: 120, // 管理员：每分钟120次
    editor: 60, // 编辑者：每分钟60次
    user: 30, // 用户：每分钟30次
    viewer: 30, // 查看者：每分钟30次（向后兼容）
    anonymous: 10, // 匿名用户：每分钟10次
  },
  TIME_WINDOWS.MINUTE
);

// 公开API的基于角色的速率限制
export const publicApiRateLimit = createRoleBasedRateLimit(
  {
    admin: 300, // 管理员：每分钟300次
    editor: 200, // 编辑者：每分钟200次
    user: 100, // 用户：每分钟100次
    viewer: 100, // 查看者：每分钟100次（向后兼容）
    anonymous: 60, // 匿名用户：每分钟60次
  },
  TIME_WINDOWS.MINUTE
);

// 搜索API的特殊限制（防止滥用）
export const searchRateLimit = createRoleBasedRateLimit(
  {
    admin: 100, // 管理员：每分钟100次搜索
    editor: 60, // 编辑者：每分钟60次搜索
    user: 30, // 用户：每分钟30次搜索
    viewer: 30, // 查看者：每分钟30次搜索（向后兼容）
    anonymous: 20, // 匿名用户：每分钟20次搜索
  },
  TIME_WINDOWS.MINUTE
);

// 图片上传的严格限制
export const imageUploadRateLimit = createRoleBasedRateLimit(
  {
    admin: 50, // 管理员：每分钟50次上传
    editor: 30, // 编辑者：每分钟30次上传
    user: 10, // 用户：每分钟10次上传
    viewer: 10, // 查看者：每分钟10次上传（向后兼容）
    anonymous: 5, // 匿名用户：每分钟5次上传
  },
  TIME_WINDOWS.MINUTE
);

// 数据修改操作的限制
export const dataModificationRateLimit = createRoleBasedRateLimit(
  {
    admin: 100, // 管理员：每分钟100次修改
    editor: 50, // 编辑者：每分钟50次修改
    viewer: 10, // 查看者：每分钟10次修改
    anonymous: 5, // 匿名用户：每分钟5次修改
  },
  TIME_WINDOWS.MINUTE
);

/**
 * 速率限制策略映射
 * 用于在app.ts中统一应用
 */
export const rateLimitStrategies = {
  // 认证相关
  'auth.sensitive': authRateLimits.sensitive,
  'auth.status': authRateLimits.status,
  'auth.logout': authRateLimits.logout,

  // 管理员接口
  admin: adminRateLimit,

  // 公开API
  public: publicApiRateLimit,

  // 特殊功能
  search: searchRateLimit,
  imageUpload: imageUploadRateLimit,
  dataModification: dataModificationRateLimit,
} as const;

/**
 * 获取路径对应的速率限制策略
 */
export const getRateLimitForPath = (path: string) => {
  // 认证相关路径
  if (
    path.includes('/auth/login') ||
    path.includes('/auth/register') ||
    path.includes('/auth/reset-password')
  ) {
    return rateLimitStrategies['auth.sensitive'];
  }

  if (path.includes('/auth/me')) {
    return rateLimitStrategies['auth.status'];
  }

  if (path.includes('/auth/logout')) {
    return rateLimitStrategies['auth.logout'];
  }

  // 管理员接口
  if (path.startsWith('/admin')) {
    return rateLimitStrategies['admin'];
  }

  // 搜索接口
  if (path.includes('/search')) {
    return rateLimitStrategies['search'];
  }

  // 图片上传
  if (path.includes('/images/upload')) {
    return rateLimitStrategies['imageUpload'];
  }

  // 数据修改操作（POST, PUT, PATCH, DELETE）
  // 这个需要在中间件中根据HTTP方法判断

  // 默认公开API限制
  return rateLimitStrategies['public'];
};
