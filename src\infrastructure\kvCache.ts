// KVNamespace 类型现在在全局作用域中可用

import type { Cache } from './cache';

/**
 * 基于 Cloudflare KV 的 Cache 实现。
 *
 * 注意：TTL 由 `expirationTtl` 控制，单位秒。
 */
export class KVCache implements Cache {
  constructor(private readonly kv: KVNamespace) {}

  async get<T = unknown>(key: string): Promise<T | null> {
    const data = await this.kv.get(key);
    if (data === null) return null;
    try {
      return JSON.parse(data) as T;
    } catch {
      // 如果不是 JSON 格式，直接返回字符串
      return data as unknown as T;
    }
  }

  async set<T = unknown>(key: string, value: T, ttl = 0): Promise<void> {
    const str =
      typeof value === 'string' ? (value as string) : JSON.stringify(value);
    if (ttl > 0) {
      await this.kv.put(key, str, { expirationTtl: ttl });
    } else {
      await this.kv.put(key, str);
    }
  }

  async delete(key: string): Promise<void> {
    await this.kv.delete(key);
  }
}
