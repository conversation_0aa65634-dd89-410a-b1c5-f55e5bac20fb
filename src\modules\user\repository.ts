import type { D1Database } from '@cloudflare/workers-types';

import type { User } from './schema';
import { D1UserRepository } from '@/infrastructure/db/userRepository';

/**
 * UserRepository 定义了用户相关的持久化操作接口
 */
export interface UserRepository {
  /** 列出所有用户 */
  list(): Promise<User[]>;
  /** 通过 ID 获取用户 */
  findById(id: string): Promise<User | null>;
  /** 创建用户 */
  create(data: CreateUserData): Promise<User>;
  /** 更新用户 */
  update(id: string, changes: UpdateUserData): Promise<User | null>;
  /** 更新密码 */
  updatePassword(id: string, hashedPassword: string): Promise<void>;
  /** 删除用户（会级联删除会话与密钥） */
  delete(id: string): Promise<void>;
}

export interface CreateUserData {
  id: string;
  username: string;
  role: string;
  hashedPassword: string;
}

export interface UpdateUserData {
  username?: string;
  role?: string;
}

/**
 * 工厂：根据运行环境返回具体仓库实现
 */
export function createUserRepository(db: D1Database): UserRepository {
  // 目前仅 D1
  return new D1UserRepository(db);
}
