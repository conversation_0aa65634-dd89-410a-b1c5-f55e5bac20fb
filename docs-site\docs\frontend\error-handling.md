# 错误处理指南

> ⚠️ **目标**: 构建健壮的错误处理机制，提供良好的用户体验

## 🎯 错误类型概览

### API错误分类

```typescript
// src/types/errors.ts
export interface ApiError {
  code: number;
  message: string;
  details?: any;
  timestamp: string;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}
```

### 错误码映射

```typescript
// src/utils/errorMapping.ts
export const ERROR_CODE_MAP = {
  // 认证相关 (10xxx)
  10001: { type: ErrorType.AUTHENTICATION_ERROR, message: '用户名或密码错误' },
  10002: { type: ErrorType.AUTHENTICATION_ERROR, message: 'Token已过期' },
  10003: { type: ErrorType.AUTHENTICATION_ERROR, message: '无效的Token' },

  // 权限相关 (20xxx)
  20001: { type: ErrorType.AUTHORIZATION_ERROR, message: '权限不足' },
  20002: { type: ErrorType.AUTHORIZATION_ERROR, message: '需要管理员权限' },

  // 验证相关 (30xxx)
  30001: { type: ErrorType.VALIDATION_ERROR, message: '参数缺失或无效' },
  30002: { type: ErrorType.VALIDATION_ERROR, message: '用户名已存在' },
  30003: { type: ErrorType.VALIDATION_ERROR, message: '邮箱格式无效' },

  // 资源相关 (60xxx)
  60001: { type: ErrorType.NOT_FOUND_ERROR, message: '用户不存在' },
  60002: { type: ErrorType.NOT_FOUND_ERROR, message: '资源不存在' },
  60003: { type: ErrorType.NOT_FOUND_ERROR, message: '事件不存在' },

  // 服务器相关 (50xxx)
  50001: { type: ErrorType.SERVER_ERROR, message: '内部服务器错误' },
  50002: { type: ErrorType.SERVER_ERROR, message: '数据库连接失败' },
} as const;

export function getErrorInfo(code: number) {
  return (
    ERROR_CODE_MAP[code as keyof typeof ERROR_CODE_MAP] || {
      type: ErrorType.UNKNOWN_ERROR,
      message: '未知错误',
    }
  );
}
```

## 🚀 统一错误处理

### 1. 错误处理服务

```typescript
// src/services/errorHandler.ts
import { getErrorInfo, ErrorType } from '@/utils/errorMapping';
import { useTranslation } from '@/hooks/useTranslation';

export interface ProcessedError {
  type: ErrorType;
  message: string;
  code?: number;
  field?: string;
  shouldRetry: boolean;
  shouldRedirect?: string;
}

export class ErrorHandlerService {
  // 处理API错误
  static processApiError(error: any): ProcessedError {
    // 网络错误
    if (!error.response && error.code === 'NETWORK_ERROR') {
      return {
        type: ErrorType.NETWORK_ERROR,
        message: '网络连接失败，请检查网络设置',
        shouldRetry: true,
      };
    }

    // HTTP状态码错误
    const status = error.status || error.response?.status;
    const data = error.data || error.response?.data;

    switch (status) {
      case 400:
        return this.handleValidationError(data);
      case 401:
        return {
          type: ErrorType.AUTHENTICATION_ERROR,
          message: '登录已过期，请重新登录',
          shouldRetry: false,
          shouldRedirect: '/login',
        };
      case 403:
        return {
          type: ErrorType.AUTHORIZATION_ERROR,
          message: '权限不足，无法执行此操作',
          shouldRetry: false,
        };
      case 404:
        return {
          type: ErrorType.NOT_FOUND_ERROR,
          message: '请求的资源不存在',
          shouldRetry: false,
        };
      case 422:
        return this.handleValidationError(data);
      case 500:
        return {
          type: ErrorType.SERVER_ERROR,
          message: '服务器内部错误，请稍后重试',
          shouldRetry: true,
        };
      default:
        return this.handleBusinessError(data);
    }
  }

  // 处理业务错误
  private static handleBusinessError(data: any): ProcessedError {
    if (data?.code) {
      const errorInfo = getErrorInfo(data.code);
      return {
        type: errorInfo.type,
        message: data.message || errorInfo.message,
        code: data.code,
        shouldRetry: false,
      };
    }

    return {
      type: ErrorType.UNKNOWN_ERROR,
      message: data?.message || '未知错误',
      shouldRetry: false,
    };
  }

  // 处理验证错误
  private static handleValidationError(data: any): ProcessedError {
    if (data?.errors && Array.isArray(data.errors)) {
      const firstError = data.errors[0];
      return {
        type: ErrorType.VALIDATION_ERROR,
        message: firstError.message,
        field: firstError.field,
        shouldRetry: false,
      };
    }

    return {
      type: ErrorType.VALIDATION_ERROR,
      message: data?.message || '输入数据无效',
      shouldRetry: false,
    };
  }

  // 记录错误
  static logError(error: ProcessedError, context?: any) {
    console.error('Application Error:', {
      ...error,
      context,
      timestamp: new Date().toISOString(),
    });

    // 在生产环境中可以发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送到Sentry、LogRocket等服务
    }
  }
}
```

### 2. 错误状态管理

```typescript
// src/stores/error.ts
import { create } from 'zustand';
import {
  ErrorHandlerService,
  type ProcessedError,
} from '@/services/errorHandler';

interface ErrorState {
  errors: ProcessedError[];
  isShowingError: boolean;

  // Actions
  addError: (error: any, context?: any) => void;
  removeError: (index: number) => void;
  clearErrors: () => void;
  handleApiError: (error: any, context?: any) => ProcessedError;
}

export const useErrorStore = create<ErrorState>((set, get) => ({
  errors: [],
  isShowingError: false,

  addError: (error: any, context?: any) => {
    const processedError = ErrorHandlerService.processApiError(error);
    ErrorHandlerService.logError(processedError, context);

    set((state) => ({
      errors: [...state.errors, processedError],
      isShowingError: true,
    }));

    // 自动处理重定向
    if (processedError.shouldRedirect) {
      setTimeout(() => {
        window.location.href = processedError.shouldRedirect!;
      }, 2000);
    }
  },

  removeError: (index: number) => {
    set((state) => ({
      errors: state.errors.filter((_, i) => i !== index),
      isShowingError: state.errors.length > 1,
    }));
  },

  clearErrors: () => {
    set({ errors: [], isShowingError: false });
  },

  handleApiError: (error: any, context?: any) => {
    const processedError = ErrorHandlerService.processApiError(error);
    get().addError(error, context);
    return processedError;
  },
}));
```

### 3. 错误边界组件

```typescript
// src/components/ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { ErrorHandlerService, ErrorType } from '@/services/errorHandler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误
    ErrorHandlerService.logError({
      type: ErrorType.UNKNOWN_ERROR,
      message: error.message,
      shouldRetry: false,
    }, { errorInfo, stack: error.stack });

    // 调用自定义错误处理
    this.props.onError?.(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">
                  出现了一些问题
                </h3>
              </div>
            </div>
            <div className="mb-4">
              <p className="text-sm text-gray-500">
                应用程序遇到了意外错误。我们已经记录了这个问题，请稍后重试。
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => window.location.reload()}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-blue-700"
              >
                刷新页面
              </button>
              <button
                onClick={() => window.history.back()}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md text-sm font-medium hover:bg-gray-400"
              >
                返回上页
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## 🎨 用户界面组件

### 1. 错误提示组件

```typescript
// src/components/ErrorToast.tsx
import React, { useEffect } from 'react';
import { useErrorStore } from '@/stores/error';
import { ErrorType } from '@/utils/errorMapping';

export function ErrorToast() {
  const { errors, removeError, isShowingError } = useErrorStore();

  if (!isShowingError || errors.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {errors.map((error, index) => (
        <ErrorToastItem
          key={index}
          error={error}
          onClose={() => removeError(index)}
        />
      ))}
    </div>
  );
}

interface ErrorToastItemProps {
  error: ProcessedError;
  onClose: () => void;
}

function ErrorToastItem({ error, onClose }: ErrorToastItemProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 5000);

    return () => clearTimeout(timer);
  }, [onClose]);

  const getErrorIcon = () => {
    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
        return '🌐';
      case ErrorType.AUTHENTICATION_ERROR:
        return '🔒';
      case ErrorType.AUTHORIZATION_ERROR:
        return '⛔';
      case ErrorType.VALIDATION_ERROR:
        return '⚠️';
      case ErrorType.NOT_FOUND_ERROR:
        return '🔍';
      case ErrorType.SERVER_ERROR:
        return '🔧';
      default:
        return '❌';
    }
  };

  const getErrorColor = () => {
    switch (error.type) {
      case ErrorType.VALIDATION_ERROR:
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case ErrorType.NETWORK_ERROR:
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-red-50 border-red-200 text-red-800';
    }
  };

  return (
    <div className={`max-w-sm w-full border rounded-lg p-4 shadow-lg ${getErrorColor()}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="text-lg">{getErrorIcon()}</span>
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium">{error.message}</p>
          {error.field && (
            <p className="text-xs mt-1 opacity-75">字段: {error.field}</p>
          )}
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <span className="sr-only">关闭</span>
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {error.shouldRetry && (
        <div className="mt-3">
          <button
            onClick={() => window.location.reload()}
            className="text-xs bg-white bg-opacity-20 hover:bg-opacity-30 px-2 py-1 rounded"
          >
            重试
          </button>
        </div>
      )}
    </div>
  );
}
```

### 2. 表单错误显示

```typescript
// src/components/FormError.tsx
import React from 'react';
import { useErrorStore } from '@/stores/error';
import { ErrorType } from '@/utils/errorMapping';

interface FormErrorProps {
  field?: string;
  className?: string;
}

export function FormError({ field, className = '' }: FormErrorProps) {
  const { errors } = useErrorStore();

  const fieldErrors = errors.filter(
    error => error.type === ErrorType.VALIDATION_ERROR &&
             (!field || error.field === field)
  );

  if (fieldErrors.length === 0) {
    return null;
  }

  return (
    <div className={`text-red-600 text-sm mt-1 ${className}`}>
      {fieldErrors.map((error, index) => (
        <div key={index}>{error.message}</div>
      ))}
    </div>
  );
}
```

## 🔧 实际使用示例

### 1. API调用错误处理

```typescript
// src/hooks/useApiCall.ts
import { useState } from 'react';
import { useErrorStore } from '@/stores/error';

export function useApiCall<T = any>() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<T | null>(null);
  const { handleApiError } = useErrorStore();

  const execute = async (
    apiCall: () => Promise<T>,
    options?: {
      onSuccess?: (data: T) => void;
      onError?: (error: any) => void;
      showError?: boolean;
    }
  ) => {
    setLoading(true);

    try {
      const result = await apiCall();
      setData(result);
      options?.onSuccess?.(result);
      return result;
    } catch (error) {
      if (options?.showError !== false) {
        handleApiError(error, { apiCall: apiCall.name });
      }
      options?.onError?.(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return { execute, loading, data };
}

// 使用示例
function EventList() {
  const { execute, loading } = useApiCall();
  const [events, setEvents] = useState([]);

  const loadEvents = () => {
    execute(
      () => api.GET('/events'),
      {
        onSuccess: (response) => setEvents(response.data.items),
        onError: (error) => console.log('Failed to load events', error),
      }
    );
  };

  return (
    <div>
      <button onClick={loadEvents} disabled={loading}>
        {loading ? '加载中...' : '加载事件'}
      </button>
      {/* 事件列表渲染 */}
    </div>
  );
}
```

### 2. 表单提交错误处理

```typescript
// src/components/LoginForm.tsx
import React, { useState } from 'react';
import { useApiCall } from '@/hooks/useApiCall';
import { FormError } from '@/components/FormError';
import { authService } from '@/services/auth';

export function LoginForm() {
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
  });
  const { execute, loading } = useApiCall();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    await execute(
      () => authService.login(credentials),
      {
        onSuccess: () => {
          // 登录成功，跳转到首页
          window.location.href = '/';
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="username">用户名</label>
        <input
          id="username"
          type="text"
          value={credentials.username}
          onChange={(e) => setCredentials(prev => ({
            ...prev,
            username: e.target.value
          }))}
          className="w-full px-3 py-2 border rounded"
        />
        <FormError field="username" />
      </div>

      <div>
        <label htmlFor="password">密码</label>
        <input
          id="password"
          type="password"
          value={credentials.password}
          onChange={(e) => setCredentials(prev => ({
            ...prev,
            password: e.target.value
          }))}
          className="w-full px-3 py-2 border rounded"
        />
        <FormError field="password" />
      </div>

      <button
        type="submit"
        disabled={loading}
        className="w-full bg-blue-500 text-white py-2 px-4 rounded disabled:opacity-50"
      >
        {loading ? '登录中...' : '登录'}
      </button>

      <FormError />
    </form>
  );
}
```

## 🔍 调试和监控

### 错误监控集成

```typescript
// src/utils/errorMonitoring.ts
export function setupErrorMonitoring() {
  // 全局错误捕获
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    // 发送到监控服务
  });

  // Promise rejection捕获
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    // 发送到监控服务
  });
}
```

---

**下一步**: 查看 [常用示例](./common-examples.md) 获取更多实用代码片段 📝
