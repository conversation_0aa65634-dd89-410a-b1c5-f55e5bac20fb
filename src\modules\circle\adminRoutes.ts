import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import * as circleController from './controller';
import {
  circleSchema,
  circleCreateRequest,
  circleUpdateRequest,
} from './schema';
import {
  successResponse,
  paginatedResult,
  errorResponse,
} from '@/utils/schemas';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const routes = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI 定义 ----------
const getCirclesRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '社团列表',
  tags: ['Admin.Circles'],
  responses: {
    200: {
      description: '社团列表',
      content: {
        'application/json': { schema: paginatedResult(circleSchema) },
      },
    },
  },
});

const postCircleRoute = createRoute({
  method: 'post',
  path: '/',
  summary: '创建社团',
  tags: ['Admin.Circles'],
  request: {
    body: { content: { 'application/json': { schema: circleCreateRequest } } },
  },
  responses: {
    201: {
      description: '社团创建成功',
      content: { 'application/json': { schema: successResponse } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
    409: {
      description: 'Conflict',
      content: { 'application/json': { schema: errorResponse } },
    },
    422: { description: 'Validation Error' },
  },
});

const getCircleRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '社团详情',
  tags: ['Admin.Circles'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '社团详情',
      content: { 'application/json': { schema: circleSchema } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

const putCircleRoute = createRoute({
  method: 'put',
  path: '/{id}',
  summary: '更新社团',
  tags: ['Admin.Circles'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
    body: { content: { 'application/json': { schema: circleUpdateRequest } } },
  },
  responses: {
    200: {
      description: '社团已保存',
      content: { 'application/json': { schema: successResponse } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
    422: { description: 'Validation Error' },
  },
});

const deleteCircleRoute = createRoute({
  method: 'delete',
  path: '/{id}',
  summary: '删除社团',
  tags: ['Admin.Circles'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '社团已删除',
      content: {
        'application/json': {
          schema: successResponse.pick({ message: true }),
        },
      },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// ---------- 路由注册 ----------
registerOpenApiRoute(routes, getCirclesRoute, circleController.listCircles);
registerOpenApiRoute(routes, postCircleRoute, circleController.createCircle);
registerOpenApiRoute(routes, getCircleRoute, circleController.getCircle);
registerOpenApiRoute(routes, putCircleRoute, circleController.updateCircle);
registerOpenApiRoute(routes, deleteCircleRoute, circleController.deleteCircle);

export { routes };
