import { z } from '@hono/zod-openapi';

// 作者实体 Schema
export const artistSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  name: z.string().openapi({ example: 'Alice' }),
  urls: z.string().nullable().optional(),
  created_at: z.string().openapi({ example: '2024-01-01T00:00:00Z' }),
  updated_at: z.string().openapi({ example: '2024-01-01T00:00:00Z' }),
  description: z.string().nullable().optional(),
});

export type Artist = z.infer<typeof artistSchema>;
