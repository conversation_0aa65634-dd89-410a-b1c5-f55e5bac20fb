﻿# 贡献指南（Contribution Guide）

感谢对 Ayafeed API 的兴趣！请遵循以下流程以确保高质量贡献。

## 1. 开发环境准备

### 前置要求

- Node.js 18+
- pnpm 8+
- Cloudflare 账户（用于 D1 数据库）

### 项目设置

```bash
# 1. Fork 并克隆项目
git clone https://github.com/YOUR_USERNAME/ayafeed-api.git
cd ayafeed-api

# 2. 安装依赖
pnpm install

# 3. 配置环境
cp wrangler.example.jsonc wrangler.jsonc
# 编辑 wrangler.jsonc 中的 account_id 等配置

# 4. 初始化数据库
wrangler d1 create ayafeed-dev
wrangler d1 execute ayafeed-dev --file=db/schema.sql
wrangler d1 execute ayafeed-dev --file=db/seeds/000_base.sql
```

## 2. 分支管理

- 从 `main` 分支创建功能分支
- 分支命名规范：
  - `feat/功能名称` - 新功能
  - `fix/问题描述` - 修复bug
  - `docs/文档更新` - 文档更新
  - `refactor/重构描述` - 代码重构

## 3. 代码规范

### 模块化开发

- 新功能应遵循 Feature-First 架构
- 在 `src/modules/` 下创建独立模块
- 每个模块包含：controller、service、schema、routes、repository

### 代码风格

- 使用 ESLint + Prettier 进行代码格式化
- 提交前运行 `pnpm lint` 和 `pnpm format`
- 遵循 TypeScript 最佳实践

### 提交规范

- 遵循 [Conventional Commits](./git-commit.md)
- 使用 `pnpm cz` 进行交互式提交
- 提交信息格式：`type(scope): description`

## 4. 测试要求

### 测试覆盖率

- 新代码必须包含单元测试
- 维持整体覆盖率：Lines/Statements≥95%、Functions≥80%、Branches≥70%
- 运行测试：`pnpm test --coverage`

### 测试类型

- **单元测试**: 测试独立的函数和类
- **集成测试**: 测试模块间的交互
- **API测试**: 测试完整的请求-响应流程

## 5. API 变更流程

### OpenAPI 规范更新

```bash
# 修改 schema 或路由后，必须执行：
pnpm gen:api

# 这会更新：
# - docs-site/static/openapi.json
# - src/api-types.d.ts
```

### 文档同步

- API 变更时同步更新模块文档
- 更新相关的使用示例
- 确保文档链接有效

## 6. Pull Request 流程

### PR 检查清单

- [ ] 代码通过所有测试
- [ ] 代码风格检查通过
- [ ] OpenAPI 规范已更新（如适用）
- [ ] 文档已更新
- [ ] 提交信息符合规范

### PR 模板

```markdown
## 变更描述

简要描述本次变更的内容和目的

## 变更类型

- [ ] 新功能
- [ ] Bug修复
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化

## 测试

- [ ] 已添加单元测试
- [ ] 已添加集成测试
- [ ] 手动测试通过

## 相关Issue

Closes #issue_number
```

## 7. 代码审查

### 审查要点

- 代码逻辑正确性
- 性能影响评估
- 安全性考虑
- 可维护性
- 文档完整性

### 反馈处理

- 及时响应审查意见
- 详细解释设计决策
- 必要时进行代码调整

## 8. 发布流程

### 版本管理

- 使用语义化版本控制
- 主要版本：破坏性变更
- 次要版本：新功能
- 补丁版本：Bug修复

### 变更日志

- 更新 CHANGELOG.md
- 记录重要变更和影响
- 提供迁移指南（如需要）

## 9. 社区参与

### 讨论渠道

- GitHub Issues：问题报告和功能请求
- GitHub Discussions：技术讨论
- Pull Requests：代码审查

### 行为准则

- 保持友善和专业
- 尊重不同观点
- 建设性地提供反馈

---

_最后更新: 2025-07-28_
