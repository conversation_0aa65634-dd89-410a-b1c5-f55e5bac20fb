# Schema 归属规范

> **目的**：在模块化代码结构下，统一 Zod/OpenAPI schema 的存放规则，避免 "到处都是 common.ts" 或 "随意跨模块引用" 的混乱。本文适用于 Ayafeed API Backend `src/` 目录。

---

## 术语说明

| 名称                            | 解释                                                                                       |
| ------------------------------- | ------------------------------------------------------------------------------------------ |
| **模块 (Module)**               | `src/modules/<name>` 目录下的业务单元，封装路由、service、repository、tests 等完整闭环功能 |
| **Schema**                      | 基于 `@hono/zod-openapi` 定义的请求 / 响应 / DTO 类型                                      |
| **公共 Schema (Common Schema)** | 在多个模块之间共享、与任何单一业务无直接耦合的类型定义                                     |

---

## 放置规则

1. **默认放模块**  
   绝大多数 schema 应紧挨其使用场景，放置于 `src/modules/<name>/schema.ts`。这样可:
   - 直观定位: 查看模块即可掌握完整契约
   - 明确所有权: 避免跨模块修改造成破坏

2. **何时放公共**  
   满足以下全部条件时，才应放入 `src/schemas/common/`:
   - 被 **≥2 个模块** 引用；
   - 内容与具体业务弱耦合，仅描述通用概念 (如 `SuccessResponse`, `ErrorResponse` 等)；
   - 中长期稳定，不会频繁调整字段；

3. **禁止跨模块直接 import**  
   模块 A **不得**直接 `import {...} from '@/modules/b/schema'`。如需复用，请提炼为公共 schema，再通过 `@/schemas/common` 引入。

4. **命名与导出**
   - 公共目录使用 **目录导出模式**：`src/schemas/common/index.ts`，外部统一 `import {...} from '@/schemas/common'`；
   - 若未来出现多文件，可在子目录内细分，但仍由 `index.ts` 统一 re-export。

---

## 迁移步骤

1. 创建目录 `src/schemas/common` (已完成)。
2. 将原 `src/schemas/common.ts` 移动到 `src/schemas/common/index.ts`。
3. 检查其他可能符合公共标准的 schema，如分页、通用枚举等，逐步迁移。
4. 执行 `pnpm lint --fix && pnpm typecheck && pnpm test`，确保无引用错误。

---

## ESLint & CI 约束

- ESLint 规则 (TODO)：禁止 `@/modules/*/schema` 被其他模块导入；
- CI 在 `pnpm lint` & `pnpm typecheck` 阶段运行该规则；
- 如需豁免，请在 PR 描述中说明理由并由 Reviewer 认定。

---

## 常见问答

**Q: 我需要在两个模块中共享某个枚举，应该怎么做？**  
A: 提取到 `src/schemas/common/<yourFile>.ts` 并在 `index.ts` re-export，然后两个模块统一从 `@/schemas/common` 引入。

**Q: 发现公共 schema 需要业务字段扩展怎么办？**  
A: 先在 Issue 中讨论，确认是否应在对应模块内派生子类型，或调整公共定义；避免直接修改导致其他模块 break。
