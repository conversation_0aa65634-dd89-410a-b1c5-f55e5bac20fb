import type { D1Database } from '@cloudflare/workers-types';
import { describe, it, expect } from 'vitest';

import { getStats } from '@/modules/stats/service';

function createMockDB(): D1Database {
  // 预设数据
  const totals = { circles: 3, artists: 5, events: 8 };
  const monthlyRows = [
    { month: '01', count: 2 },
    { month: '04', count: 1 },
  ];

  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      const buildResponse = () => ({
        // 模拟 .bind() 可链式调用
        bind: (..._args: any[]) => buildResponse(),
        first: async () => {
          if (upper.includes('COUNT(*)') && upper.includes('FROM CIRCLES')) {
            return { total: totals.circles };
          }
          if (upper.includes('COUNT(*)') && upper.includes('FROM ARTISTS')) {
            return { total: totals.artists };
          }
          if (upper.includes('COUNT(*)') && upper.includes('FROM EVENTS')) {
            return { total: totals.events };
          }
          return null;
        },
        all: async () => {
          if (upper.includes('FROM EVENTS') && upper.includes('GROUP BY')) {
            return { results: monthlyRows };
          }
          return { results: [] };
        },
        run: async () => ({ success: true }),
      });

      return buildResponse();
    },
  } as unknown as D1Database;
}

describe('statsService', () => {
  it('getStats should return totals and eventsByMonth', async () => {
    const db = createMockDB();
    const res = await getStats(db, 2025);
    expect(res.year).toBe(2025);
    expect(res.totals).toEqual({ circles: 3, artists: 5, events: 8 });
    expect(res.eventsByMonth.length).toBe(12);
    const jan = res.eventsByMonth.find((m) => m.month === '01');
    const apr = res.eventsByMonth.find((m) => m.month === '04');
    expect(jan?.count).toBe(2);
    expect(apr?.count).toBe(1);
  });
});
