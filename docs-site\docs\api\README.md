# Ayafeed API 文档索引

> 本节汇总后端接口相关的所有文档、示例与工具链，帮助前端 / 第三方开发者快速接入。

## 目录

| 类别 | 文档                                | 说明                              |
| ---- | ----------------------------------- | --------------------------------- |
| 概览 | `README.md` (当前)                  | 快速上手、目录导航                |
| 规范 | [OpenAPI 自动化指南](./openapi.md)  | 如何生成 / 校验 specification     |
| 规范 | [错误码对照表](./error-codes.md)    | 业务错误码区段及含义              |
| 规范 | [请求与返回格式](./request-spec.md) | 统一响应包装、分页、Header 约定   |
| 规范 | `../static/openapi.json`            | 最新 OpenAPI JSON 文件 (自动生成) |

---

## 快速上手

### 1. 获取最新 OpenAPI

```bash
# 依赖 pnpm 工作区脚本
pnpm gen:api   # 生成 docs-site/static/openapi.json 与 src/api-types.d.ts
```

运行后将自动：

1. 分析 `src/modules/*/schema.ts` 中的 Zod 定义并输出 `docs-site/static/openapi.json`。
2. 使用 `openapi-typescript` 生成 **完全类型安全** 的 `src/api-types.d.ts`。

> **提示**：CI 会校验这两个文件是否过期，如未提交将阻塞合并。

### 2. 生成前端请求 Client

后端与前端约定采用 `openapi-typescript-fetch`：

```ts
// src/lib/api/client.ts
import { Fetcher } from 'openapi-typescript-fetch';
import type { paths } from '@/api-types';

export const createClient = (options: { baseUrl?: string } = {}) => {
  const fetcher = Fetcher.for<paths>();
  fetcher.configure({ baseUrl: options.baseUrl ?? '/api' });
  return fetcher; // 支持链式调用
};
```

使用示例：

```ts
import { createClient } from '@/lib/api/client';

const api = createClient({ baseUrl: '/api' });

// 基础请求
const { data: circlesResponse } = await api.GET('/circles');
// circlesResponse = { items: Circle[], total: number }

// 多语言请求（推荐使用 X-Locale 头部）
const { data: events } = await api.GET('/events', {
  headers: { 'X-Locale': 'zh' },
});

// 搜索API
const { data: searchResults } = await api.GET('/search', {
  params: { query: { q: 'Comiket', type: 'events' } },
  headers: { 'X-Locale': 'ja' },
});

// Feed API
const { data: feedData } = await api.GET('/feed', {
  params: { query: { page: '1', limit: '20', type: 'all' } },
  headers: { 'X-Locale': 'en' },
});
```

### 3. 分页与错误处理

- 所有列表接口返回 `{ items, total, page, pageSize }`，详见 [请求规范](./request-spec.md#3-接口清单)。
- 业务异常使用统一 `code / message`；可参考 [错误码对照表](./error-codes.md)。

---

## 更新流程

1. 修改或新增 **模块 Schema** / **路由定义** (在 `src/modules/*/schema.ts` 和 `src/modules/*/routes.ts`)。
2. 运行 `pnpm gen:api` 更新 OpenAPI 与类型文件。
3. 若新增错误码，请同时在 `src/schemas/common.ts` 枚举中补充并同步至 [错误码对照表](./error-codes.md)。
4. 提交时请确保 `docs-site/static/openapi.json`、`src/api-types.d.ts`、以及本目录下文档保持一致。

---

## 新增功能

### 社团搜索和筛选 (v0.4.2.6+)

- **增强的 `/circles` API**：
  - `search` - 支持社团名称和作者搜索
  - `category` - 支持按分类筛选（comic, game, music, novel, goods, other）
  - 数据库层分页，大幅提升性能
  - 支持组合查询（搜索 + 分类筛选）

- **使用示例**：

  ```bash
  # 基本分页
  GET /circles?page=1&pageSize=20

  # 搜索功能
  GET /circles?search=東方&page=1&pageSize=20

  # 分类筛选
  GET /circles?category=comic&page=1&pageSize=20

  # 组合查询
  GET /circles?search=愛好&category=comic&page=1&pageSize=20
  ```

### 多语言支持 (v0.4.3+)

- **新增API端点**：
  - `/search` - 多语言搜索API，支持事件和社团搜索
  - `/feed` - 多语言内容流API，支持分页和类型过滤

- **语言检测优先级**：`X-Locale` > Cookie > `Accept-Language` > 默认语言
- **标准化响应**：所有API响应包含 `locale` 字段和统一格式
- **缓存优化**：缓存键包含语言参数，确保多语言缓存隔离

---

_Last updated: 2025-07-29_
