---
sidebar_position: 2
title: 后端开发待办事项
description: 后端开发任务的看板式追踪与管理。
---

# 后端开发待办事项

> 本文件采用「看板 (Kanban)」视图持续跟踪任务状态，并辅以工作日志与历史归档。
>
> **维护约定**：
>
> 1. 任务在各状态列之间移动，一次只处理少量“进行中”任务。
> 2. 任务标题应清晰、可执行，并带有唯一 ID (如 `SEC-1`) 和责任标签 (`[BE]`/`[Test]`/`[DevOps]`)。
> 3. 已完成任务应在 PR 合并时关联 PR 链接，并定期归档至 CHANGELOG。

---

## 任务看板

### 🚧 进行中 (In Progress)

- **Epic: `#i18n` 国际化 (i18n)**
  - **阶段一（1周）：数据库改造**
    - [x] `I18N-DB-1`: 修改 `events` 表结构，添加多语言字段
    - [x] `I18N-DB-2`: 添加 `auth_user.locale` 字段，默认值改为 `'en'`
    - [ ] `I18N-DB-3`: 将现有日语内容迁移到 `*_ja` 字段，并补充英文翻译
  - **阶段二（2周）：后端改造**
    - [x] `I18N-BE-1`: 实现 i18n 相关类型定义
    - [x] `I18N-BE-2`: 实现 `locale` 中间件
    - [ ] `I18N-BE-3`: 修改 `/auth/me` 接口返回 `locale`
    - [x] `I18N-BE-4`: 改造 `events` 相关接口，支持国际化
    - [ ] `I18N-BE-5`: 实现管理接口的多语言验证
    - [ ] `I18N-BE-6`: 补充中文翻译数据
- **Epic: `#subscription` 订阅服务**
  - [ ] `SUB-1`: [BE] 设计订阅功能的数据模型与 API
  - [ ] `SUB-2`: [BE] 实现初版通知功能（邮件或站内信）

### 🔍 待审核/测试 (Review / Testing)

_（暂无）_

### 📝 待办 (Backlog)

- **Epic: `#security` 认证与安全**
  - [ ] `SEC-1`: [BE] 登录成功后重新生成会话 ID（防止 Session Fixation）
  - [ ] `SEC-2`: [BE] 服务端记录 `session_id ↔ user_id` 映射，支持审计与强制登出
  - [ ] `SEC-3`: [BE] 实现 CSRF Token 生命周期管理（自动刷新 / 30 min 失效）
  - [ ] `SEC-4`: [BE] 进行安全审计
  - [ ] `SEC-5`: [BE] 实现基于 Cookie 的会话存储与刷新策略
  - [ ] `SEC-6`: [BE] 验证并配置 Cookie 安全策略 (SameSite, Secure, HttpOnly)
  - [ ] `SEC-7`: [BE] 实现会话的 Idle / Absolute 过期策略
  - [ ] `SEC-8`: [BE] 实现 /auth/refresh 刷新端点
  - [ ] `SEC-9`: [BE] 评估并实现登录接口限速或 Captcha 策略
  - [ ] `SEC-10`: [BE] 设计并评估密钥轮换策略
  - [ ] `SEC-11`: [BE] 设计并实现会话审计日志
  - [ ] `SEC-12`: [BE] 检查并确认密码哈希算法符合行业标准
- **Epic: `#quality` 质量保证与测试**
  - [ ] `QA-1`: [Test] 修复并完善单元/集成测试（目标：≥90% 通过率）
- **Epic: `#docs` 文档与接口**
  - [ ] `DOCS-1`: [BE] 补全 OpenAPI 生成脚本与文档同步
- **Epic: `#ops` CI/CD 与运维**
  - [ ] `OPS-3`: [DevOps] 完善 CI/CD 流程（最小化交付阻力）
  - [ ] `OPS-4`: [DevOps] 接入基础日志、监控、告警系统
- **Epic: `#perf` 缓存与性能**
  - [ ] `PERF-1`: [BE] 在 Worker 侧封装 KV 读取，引入本地 LRU 缓存 (2–5 s)，减少 KV/D1 访问
  - [ ] `PERF-2`: [BE] 实现热门社团推荐接口的缓存策略
  - [ ] `PERF-3`: [BE] 设计并实现 SSR 首屏数据预取及缓存策略
- **Epic: `#logging` 日志服务**
  - [ ] `LOG-1`: [BE] 扩展日志查询参数：日期范围、关键字、模糊搜索
  - [ ] `LOG-2`: [BE] 实现管理后台日志 UI：表格视图、列筛选、JSON 展开
  - [ ] `LOG-3`: [BE] 实现日志导出 / 删除接口（RBAC 权限）
  - [ ] `LOG-4`: [BE] 实现 logs 表数据归档至 Cloudflare R2 / ClickHouse
  - [ ] `LOG-5`: [BE] 添加日志表 (created_at, id) 复合索引
- **Epic: `#i18n` 国际化 (i18n)**
  - [ ] `I18N-1`: [BE] 调研适用于 Cloudflare Workers 的 Node.js i18n 库
  - [ ] `I18N-2`: [BE] 设计翻译文件结构与基础键值
  - [ ] `I18N-3`: [BE] 实现 `i18nService` 与 `localeDetection` 中间件
  - [ ] `I18N-4`: [BE] 重构错误响应与成功消息的本地化
  - [ ] `I18N-5`: [BE] 更新 OpenAPI 规范与文档
- **Epic: `#search` 搜索服务**
  - [ ] `SEARCH-1`: [BE] 设计搜索索引与实现搜索 API
- **Epic: `#subscription` 订阅服务**
  - [ ] `SUB-1`: [BE] 设计订阅功能的数据模型与 API
  - [ ] `SUB-2`: [BE] 实现初版通知功能（邮件或站内信）

### 🧊 Icebox（后续版本）

- [ ] `ICE-1`: [API] REST 版本化：对外路径添加 `/v1/`
- [ ] `ICE-2`: [API] Deprecated 策略与版本迁移指南
- [ ] `ICE-3`: [SDK] 自动生成前端 SDK
- [ ] `ICE-4`: [DevOps] Cloudflare Workers Staging 环境搭建
- [ ] `ICE-5`: [Ops] Prometheus / Grafana 指标监控
- [ ] `ICE-6`: [Ops] OpenTelemetry + Jaeger 追踪
- [ ] `ICE-7`: [Perf] 热点查询分析与优化
- [ ] `ICE-8`: [DB] 多数据库驱动支持
- [ ] `REFACTOR-2`: [BE] 净化 `auth` 模块类型问题（移除 `as any`，统一命名，并添加 Zod 校验）- 与 Security Epic 重构一并处理

### ✅ 已完成

> 已完成任务请定期移至 CHANGELOG ，避免看板膨胀。

- **v0.4.5**
  - [x] **允许边缘代码使用 `any` 类型**：更新 ESLint 配置，对核心代码强制 `no-explicit-any`，为测试和脚本代码创建豁免
  - [x] **重新评估 `/appearances` 接口定位**：统一入口为 `/events/{id}/appearances`，弃用旧接口
  - [x] **TypeScript 配置分层**：实现 `tsconfig` 多配置管理，核心代码保持严格检查
  - [x] **类型安全性修复**：
    - [x] `REFACTOR-1`: [BE] 修复 `event` 模块 Service 层返回类型
  - [x] **配置优化**：
    - [x] `OPS-1`: [DevOps] 调整 ESLint / Prettier 规则：扩展 lint-staged 匹配
    - [x] `OPS-2`: [DevOps] 在 `tsconfig.json` 暂时调整 `strict` 选项，按目录分层
    - [x] `DEBT-1`: [BE] 技术债清单：为每个 `any` 添加 TODO 注释

### 🗄️ Archive

> 已全部迁移至 CHANGELOG

## 🚀 后端正式上线前工作量估算 (2025-07-22)

| 任务               | 预计人天 |
| ------------------ | -------- |
| 认证与安全加固     | 6        |
| 质量保证与测试     | 3        |
| CI/CD 与运维       | 2        |
| 日志服务 MVP       | 3        |
| 国际化支持         | 2        |
| 搜索服务基础实现   | 2        |
| 订阅服务基础实现   | 2        |
| 缓存优化与性能调优 | 3        |
| **合计**           | **23**   |

> 备注：最乐观 18 人天，最保守 28 人天。考虑到新增的搜索、订阅功能以及更多的安全任务，总工作量相应增加。

---

## 工作日志

### 2025-07-22

- **架构重构与文档优化**（预估 8h，实际 10h）：
  - 调整 ESLint 规则，允许基础设施层通过类型导入引用模块 schema（1h）
  - 删除错误创建的公共 schema 文件（0.5h）
  - 恢复所有导入路径到正确的模块化结构（2h）
  - 更新 DDD 实践文档，添加模块化设计说明（1h）
  - 集成 Redocly 展示 API 文档（1h）
  - 添加 API 文档生成脚本（1h）
  - 优化文档站点配置（0.5h）
  - 新增 Stylelint 配置并同步更新相关脚本（1h）
  - 调整 pre-commit 钩子配置（0.5h）
  - 更新 ESLint 配置（0.5h）
  - 升级依赖并更新 lock 文件（0.5h）
  - 补充后端开发文档（0.5h）

### 2025-07-20

- **云端 → 本地同步**：合并本周远端改动至本地主干，预估 **31 h / 1 人（≈ 4 个工作日）**：
  - 新增 `auth` 模块及测试
  - 聚合后台路由并补充 `adminRoutes.ts`
  - 删除废弃 controllers，完成 feature-first 迁移
  - 升级 ESLint 与 `tsconfig` 配置
  - 改造 OpenAPI 生成脚本
  - 优化 CI 流程
  - 更新文档与测试用例

### 2025-07-19

- 将 todo-backend.md 重构为看板视图，任务以里程碑聚合分类
