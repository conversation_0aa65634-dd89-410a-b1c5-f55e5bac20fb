import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import * as statsController from './controller';
import { statsResponseSchema } from './schema';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const routes = new OpenAPIHono<HonoApp>();

const getStatsRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '后台统计数据',
  tags: ['Admin.Stats'],
  request: {
    query: z.object({
      year: z.string().optional().openapi({ example: '2025' }),
    }),
  },
  responses: {
    200: {
      description: '统计数据',
      content: { 'application/json': { schema: statsResponseSchema } },
    },
  },
});

registerOpenApiRoute(routes, getStatsRoute, statsController.listStats);

export { routes };
