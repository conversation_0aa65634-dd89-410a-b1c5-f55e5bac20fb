import { OpenAPIHono, createRoute } from '@hono/zod-openapi';

import * as logController from './controller';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const routes = new OpenAPIHono<HonoApp>();

const getLogsRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '查询操作日志',
  tags: ['Admin.Logs'],
  responses: { 200: { description: '日志列表' } },
});

registerOpenApiRoute(routes, getLogsRoute, logController.listLogs);

export { routes };
