# Dependencies
node_modules/
**/node_modules/**

# Build outputs
dist/
build/
**/dist/**
**/build/**

# Coverage reports
coverage/
**/coverage/**

# Cloudflare Workers
.wrangler/
**/.wrangler/**

# Docusaurus
docs-site/build/
docs-site/.docusaurus/
**/docs-site/build/**
**/docs-site/.docusaurus/**

# Generated files
src/api-types.d.ts
openapi.json
scripts/@/api-types.d.ts

# Lock files
pnpm-lock.yaml
package-lock.json
yarn.lock

# Config files that should maintain their format
*.config.js
*.config.ts
*.config.mjs
wrangler.jsonc
wrangler.example.jsonc

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Cache
.cache/
.parcel-cache/
.eslintcache

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Documentation build artifacts
*.tsbuildinfo
