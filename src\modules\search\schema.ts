import { z } from '@hono/zod-openapi';

/**
 * 搜索结果项的基础 schema
 */
export const searchResultItemSchema = z.object({
  type: z.enum(['event', 'circle']).openapi({
    description: '结果类型',
    example: 'event',
  }),
  id: z.string().openapi({
    description: '资源ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  }),
  name: z.string().openapi({
    description: '名称',
    example: 'Comiket 103',
  }),
  description: z.string().nullable().openapi({
    description: '描述',
    example: '世界最大的同人志即卖会',
  }),
  venue_name: z.string().nullable().optional().openapi({
    description: '场馆名称（仅事件类型）',
    example: '东京国际展示场',
  }),
  start_date: z.string().nullable().optional().openapi({
    description: '开始时间（仅事件类型）',
    example: '2024-12-30T10:00:00Z',
  }),
  image_url: z.string().nullable().optional().openapi({
    description: '图片URL',
    example: 'https://example.com/comiket103.jpg',
  }),
  rank: z.number().openapi({
    description: '搜索相关性评分',
    example: 0.8567,
  }),
});

/**
 * 搜索响应的 meta 信息
 */
export const searchMetaSchema = z.object({
  total: z.number().openapi({
    description: '总结果数',
    example: 15,
  }),
  query: z.string().openapi({
    description: '搜索关键词',
    example: 'Comiket',
  }),
  type: z.string().openapi({
    description: '搜索类型',
    example: 'events',
  }),
});

/**
 * 搜索响应 schema
 */
export const searchResponseSchema = z.object({
  success: z.boolean().openapi({ example: true }),
  data: z.array(searchResultItemSchema),
  locale: z.string().openapi({
    description: '响应语言',
    example: 'zh',
  }),
  timestamp: z.string().openapi({
    description: '响应时间戳',
    example: '2024-01-15T10:30:00.000Z',
  }),
  meta: searchMetaSchema,
});

export type SearchResultItem = z.infer<typeof searchResultItemSchema>;
export type SearchMeta = z.infer<typeof searchMetaSchema>;
export type SearchResponse = z.infer<typeof searchResponseSchema>;
