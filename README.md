# Ayafeed API

> 🎌 现代化的同人展会信息管理平台 API

基于 Cloudflare Workers + D1 构建的高性能 API 服务，支持多语言、社团管理、展会信息等功能。

## ⚡ 快速开始

```bash
# 安装依赖
pnpm install

# 初始化数据库
wrangler d1 execute ayafeed-dev --file=db/schema.sql
wrangler d1 execute ayafeed-dev --file=db/seeds/000_base.sql

# 启动开发服务器
pnpm dev
```

📚 **详细文档**: [docs-site/docs/](./docs-site/docs/) | 🚀 **快速上手**: [安装指南](./docs-site/docs/getting-started/installation.md)

## 🏗️ 项目特性

- **🚀 高性能**: 基于 Cloudflare Workers 边缘计算
- **🌐 多语言**: 支持中文、日文、英文内容
- **🔐 安全认证**: JWT + RBAC 权限控制
- **📊 实时搜索**: 全文搜索 + 智能筛选
- **⭐ 收藏系统**: 完整的社团收藏功能，支持个性化排序
- **🎯 模块化**: Feature-First 架构设计
- **📝 类型安全**: 完整的 TypeScript 支持

## 📁 项目结构

```
src/
├── modules/           # 功能模块（Feature-First）
│   ├── auth/         # 认证模块
│   ├── bookmark/     # 收藏模块
│   ├── circle/       # 社团模块
│   ├── event/        # 事件模块
│   └── ...           # 其他模块
├── infrastructure/   # 基础设施抽象
├── middlewares/      # 全局中间件
└── utils/           # 工具函数

docs-site/           # 📚 完整文档站点
tests/              # 🧪 测试文件
db/                 # 🗄️ 数据库 Schema
```

## 🔧 开发命令

```bash
# 开发
pnpm dev              # 启动开发服务器
pnpm test             # 运行测试
pnpm lint             # 代码检查
pnpm format           # 代码格式化

# 部署
pnpm deploy           # 部署到生产环境
pnpm gen:api          # 生成 OpenAPI 规范

# 数据库
pnpm db:migrate       # 运行数据库迁移
pnpm db:seed          # 填充种子数据
```

## 📋 API 概览

```bash
# 公开接口
GET  /circles         # 获取社团列表 (支持搜索、筛选和个性化排序)
GET  /events/:id      # 获取展会详情
GET  /search          # 搜索内容
GET  /feed            # 获取内容流

# 认证接口
POST /auth/login      # 用户登录
POST /auth/register   # 用户注册

# 收藏接口 (需要认证)
POST /circles/{id}/bookmark        # 切换收藏状态
GET  /circles/{id}/bookmark/status # 查询收藏状态
GET  /user/bookmarks               # 获取收藏列表
GET  /user/bookmarks/stats         # 获取收藏统计

# 管理接口 (需要相应权限)
GET  /admin/events    # 展会管理 (admin/editor)
GET  /admin/circles   # 社团管理 (admin/editor)
GET  /admin/users     # 用户管理 (admin)
GET  /admin/stats     # 后台统计 (admin)
GET  /admin/logs      # 查看日志 (admin)
```

📖 **完整 API 文档**: [OpenAPI 规范](./docs-site/static/openapi.json)

## 🤝 贡献指南

我们欢迎所有形式的贡献！在开始之前，请：

1. 📖 阅读 [贡献指南](./docs-site/docs/development/contribution.md)
2. 🏗️ 了解 [架构设计](./docs-site/docs/architecture/)
3. 🧪 确保测试通过：`pnpm test`
4. 📝 更新相关文档

### 开发规范

- **模块化开发**: 遵循 Feature-First 架构
- **API 契约**: 修改后执行 `pnpm gen:api` 同步规范
- **测试覆盖**: 维持 95% 代码覆盖率
- **代码风格**: 使用 ESLint + Prettier

## 📚 文档导航

| 文档类型    | 链接                                          | 说明                 |
| ----------- | --------------------------------------------- | -------------------- |
| 🚀 快速开始 | [安装指南](./docs-site/docs/getting-started/) | 环境搭建和部署       |
| 💻 前端集成 | [前端文档](./docs-site/docs/frontend/)        | API 对接和客户端生成 |
| 🏗️ 架构设计 | [架构文档](./docs-site/docs/architecture/)    | 系统设计和最佳实践   |
| 📦 功能模块 | [模块文档](./docs-site/docs/modules/)         | 各模块详细说明       |
| 🛠️ 开发指南 | [开发文档](./docs-site/docs/development/)     | 贡献和开发规范       |

## 🔗 相关资源

- **📖 完整文档**: [docs-site/docs/](./docs-site/docs/)
- **🔧 GitHub 仓库**: [ayafeed/ayafeed-api](https://github.com/ayafeed/ayafeed-api)
- **📋 变更日志**: [CHANGELOG](./docs-site/docs/reference/changelog/)
- **🐛 问题反馈**: [GitHub Issues](https://github.com/ayafeed/ayafeed-api/issues)

## 📄 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件

---

**🎉 开始您的 Ayafeed API 之旅吧！**
