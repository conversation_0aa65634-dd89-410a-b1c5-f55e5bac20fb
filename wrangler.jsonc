{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "ayafeed-api",
  "main": "src/index.ts",
  "compatibility_date": "2025-07-09",
  "dev": {
    "port": 8787
  },
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "ayafeed-dev",
      "database_id": "4d029444-08c3-4d68-be62-0b80a270e1e6",
      "migrations_dir": "drizzle/migrations"
    },
  ],
  "r2_buckets": [
    {
      "binding": "R2",
      "bucket_name": "ayafeed-public-assets",
      //for prod env, use ayafeed-assets
    },
  ],
  "vars": {
    "JWT_SECRET": "ayafeed-jwt-secret",
    "BETTER_AUTH_URL":"http://localhost:8787",
    "BETTER_AUTH_SECRET":"KdCzGne09fxaM8s5qYIgMoky2Jo1T58X",
    "DATABASE_URL":"http://localhost:8787",
  },
  "kv_namespaces": [
    {
      "binding": "MY_KV_NAMESPACE",
      "id": "eba7dd21d4c549f480c1aa157c5197d8",
    },
  ],
  "env": {
    "staging": {
      "vars": {
        "JWT_SECRET": "ayafeed-jwt-secret-staging",
      },
    },
    "production": {
      "vars": {
        "JWT_SECRET": "ayafeed-jwt-secret-production",
      },
    },
  },
  "compatibility_flags": ["nodejs_compat"],
  // "vars": {
  //   "MY_VAR": "my-variable"
  // },
  // "kv_namespaces": [
  //   {
  //     "binding": "MY_KV_NAMESPACE",
  //     "id": "eba7dd21d4c549f480c1aa157c5197d8"
  //   }
  // ],
  // "r2_buckets": [
  //   {
  //     "binding": "MY_BUCKET",
  //     "bucket_name": "my-bucket"
  //   }
  // ],
  // "d1_databases": [
  //   {
  //     "binding": "MY_DB",
  //     "database_name": "my-database",
  //     "database_id": ""
  //   }
  // ],
  // "ai": {
  //   "binding": "AI"
  // },
  // "observability": {
  //   "enabled": true,
  //   "head_sampling_rate": 1
  // }
}
