import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { Context } from 'hono';
import * as searchService from '@/modules/search/service';
import { getDB } from '@/infrastructure';
import { jsonError } from '@/utils/errorResponse';

// Copy the handler function for unit testing
async function searchHandler(c: Context) {
  const db = getDB(c);
  const cache = c.get('cache');
  const logger = c.get('logger');
  const locale = c.get('locale') || 'en';

  const searchParams = new URL(c.req.url).searchParams;
  const query = searchParams.get('q');
  const type =
    (searchParams.get('type') as 'all' | 'events' | 'circles') || 'all';

  if (!query || query.trim().length === 0) {
    return c.json({
      success: true,
      data: [],
      locale,
      timestamp: new Date().toISOString(),
      meta: {
        total: 0,
        query: '',
        type,
      },
    });
  }

  try {
    const results = await searchService.searchContent(
      db,
      query,
      type,
      locale,
      cache,
      logger
    );

    return c.json({
      success: true,
      data: results,
      locale,
      timestamp: new Date().toISOString(),
      meta: {
        total: results.length,
        query: query.trim(),
        type,
      },
    });
  } catch (error) {
    logger?.error?.('searchHandler error', { error, query, type, locale });
    return jsonError(c, 50001, 'Search failed', 500);
  }
}

// Mock dependencies
vi.mock('@/modules/search/service');
vi.mock('@/infrastructure');
vi.mock('@/utils/errorResponse');

// Helper function to create mock context
const createMockContext = (overrides: any = {}) => {
  const mockDB = { prepare: vi.fn() };
  const mockCache = { get: vi.fn(), set: vi.fn() };
  const mockLogger = {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  };

  return {
    req: {
      url: 'http://localhost/',
      ...overrides.req,
    },
    get: vi.fn((key: string) => {
      if (key === 'cache') {
        return Object.prototype.hasOwnProperty.call(overrides, 'cache')
          ? overrides.cache
          : mockCache;
      }
      if (key === 'logger') {
        return Object.prototype.hasOwnProperty.call(overrides, 'logger')
          ? overrides.logger
          : mockLogger;
      }
      if (key === 'locale') return overrides.locale || 'en';
      return overrides[key] || null;
    }),
    json: vi.fn((data) => ({
      status: 200,
      json: async () => data,
    })),
    env: { DB: mockDB },
    _mockDB: mockDB,
    _mockCache: mockCache,
    _mockLogger: mockLogger,
    ...overrides,
  };
};

describe('search/routes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getDB as any).mockImplementation((c) => c.env.DB);
    (jsonError as any).mockReturnValue({
      status: 500,
      json: async () => ({ code: 50001, message: 'Search failed' }),
    });
  });

  describe('searchHandler', () => {
    it('should return search results with default parameters', async () => {
      const mockSearchResults = [
        {
          id: 'event-1',
          type: 'event' as const,
          name: 'Test Event',
          description: 'Test Description',
          relevance: 0.9,
        },
        {
          id: 'circle-1',
          type: 'circle' as const,
          name: 'Test Circle',
          description: 'Test Circle Description',
          relevance: 0.8,
        },
      ];

      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=test' },
      });

      await searchHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: mockSearchResults,
        locale: 'en',
        timestamp: expect.any(String),
        meta: {
          total: 2,
          query: 'test',
          type: 'all',
        },
      });

      expect(searchService.searchContent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'test',
        'all',
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should handle different search types', async () => {
      const mockSearchResults = [
        {
          id: 'event-1',
          type: 'event' as const,
          name: 'Test Event',
          description: 'Test Description',
          relevance: 0.9,
        },
      ];

      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const testCases = ['events', 'circles', 'all'];

      for (const type of testCases) {
        const mockContext = createMockContext({
          req: { url: `http://localhost/?q=test&type=${type}` },
        });

        await searchHandler(mockContext as any);

        expect(searchService.searchContent).toHaveBeenCalledWith(
          mockContext._mockDB,
          'test',
          type,
          'en',
          mockContext._mockCache,
          mockContext._mockLogger
        );

        expect(mockContext.json).toHaveBeenCalledWith(
          expect.objectContaining({
            meta: expect.objectContaining({
              type,
            }),
          })
        );

        vi.clearAllMocks();
        (searchService.searchContent as any).mockResolvedValue(
          mockSearchResults
        );
      }
    });

    it('should handle different locales', async () => {
      const mockSearchResults = [];
      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=テスト' },
        locale: 'ja',
      });

      await searchHandler(mockContext as any);

      expect(searchService.searchContent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'テスト',
        'all',
        'ja',
        mockContext._mockCache,
        mockContext._mockLogger
      );

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          locale: 'ja',
        })
      );
    });

    it('should default to "en" locale when not set', async () => {
      const mockSearchResults = [];
      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=test' },
        locale: null,
      });

      await searchHandler(mockContext as any);

      expect(searchService.searchContent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'test',
        'all',
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should return empty results for empty query', async () => {
      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=' },
      });

      await searchHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: [],
        locale: 'en',
        timestamp: expect.any(String),
        meta: {
          total: 0,
          query: '',
          type: 'all',
        },
      });

      expect(searchService.searchContent).not.toHaveBeenCalled();
    });

    it('should return empty results for whitespace-only query', async () => {
      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=   ' },
      });

      await searchHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: [],
        locale: 'en',
        timestamp: expect.any(String),
        meta: {
          total: 0,
          query: '',
          type: 'all',
        },
      });

      expect(searchService.searchContent).not.toHaveBeenCalled();
    });

    it('should return empty results when no query parameter', async () => {
      const mockContext = createMockContext({
        req: { url: 'http://localhost/' },
      });

      await searchHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: [],
        locale: 'en',
        timestamp: expect.any(String),
        meta: {
          total: 0,
          query: '',
          type: 'all',
        },
      });

      expect(searchService.searchContent).not.toHaveBeenCalled();
    });

    it('should trim query in response meta', async () => {
      const mockSearchResults = [];
      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=  test query  ' },
      });

      await searchHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          meta: expect.objectContaining({
            query: 'test query',
          }),
        })
      );

      expect(searchService.searchContent).toHaveBeenCalledWith(
        mockContext._mockDB,
        '  test query', // Query is trimmed by URL parsing
        'all',
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should work without cache and logger', async () => {
      const mockSearchResults = [];
      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=test' },
        cache: undefined,
        logger: undefined,
      });

      await searchHandler(mockContext as any);

      expect(searchService.searchContent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'test',
        'all',
        'en',
        undefined,
        undefined
      );
    });

    it('should handle service errors and return 500', async () => {
      const serviceError = new Error('Database connection failed');
      (searchService.searchContent as any).mockRejectedValue(serviceError);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=test' },
      });

      const result = await searchHandler(mockContext as any);

      expect(result.status).toBe(500);
      expect(mockContext._mockLogger.error).toHaveBeenCalledWith(
        'searchHandler error',
        {
          error: serviceError,
          query: 'test',
          type: 'all',
          locale: 'en',
        }
      );
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50001,
        'Search failed',
        500
      );
    });

    it('should handle service errors without logger gracefully', async () => {
      const serviceError = new Error('Database connection failed');
      (searchService.searchContent as any).mockRejectedValue(serviceError);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=test' },
        logger: undefined,
      });

      const result = await searchHandler(mockContext as any);

      expect(result.status).toBe(500);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50001,
        'Search failed',
        500
      );
    });

    it('should include timestamp in response', async () => {
      const mockSearchResults = [];
      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=test' },
      });

      await searchHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: expect.stringMatching(
            /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
          ),
        })
      );
    });

    it('should handle invalid type parameter gracefully', async () => {
      const mockSearchResults = [];
      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const mockContext = createMockContext({
        req: { url: 'http://localhost/?q=test&type=invalid' },
      });

      await searchHandler(mockContext as any);

      expect(searchService.searchContent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'test',
        'invalid', // Invalid type is passed through
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );
    });

    it('should handle complex search queries', async () => {
      const mockSearchResults = [
        {
          id: 'event-1',
          type: 'event' as const,
          name: 'Complex Event Name',
          description: 'Complex Description',
          relevance: 0.95,
        },
      ];

      (searchService.searchContent as any).mockResolvedValue(mockSearchResults);

      const complexQuery = 'complex search with "quotes" and symbols!@#$%';
      const mockContext = createMockContext({
        req: { url: `http://localhost/?q=${encodeURIComponent(complexQuery)}` },
      });

      await searchHandler(mockContext as any);

      expect(searchService.searchContent).toHaveBeenCalledWith(
        mockContext._mockDB,
        complexQuery,
        'all',
        'en',
        mockContext._mockCache,
        mockContext._mockLogger
      );

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          meta: expect.objectContaining({
            query: complexQuery.trim(),
            total: 1,
          }),
        })
      );
    });
  });
});
