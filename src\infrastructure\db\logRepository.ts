import type { D1Database } from '@cloudflare/workers-types';

import type {
  QueryOptions,
  PaginatedLogs,
  LogRepository,
} from '../../modules/log/repository';
import { Log } from '../../modules/log/schema';

/**
 * 基于 Cloudflare D1 的日志仓库实现
 */
export class D1LogRepository implements LogRepository {
  constructor(private readonly db: D1Database) {}

  async query(options: QueryOptions): Promise<PaginatedLogs> {
    const { page, pageSize, user_id, action, target_type } = options;
    const offset = (page - 1) * pageSize;

    const where: string[] = [];
    const params: (string | number | null)[] = [];

    if (user_id) {
      where.push('user_id = ?');
      params.push(user_id);
    }
    if (action) {
      where.push('action = ?');
      params.push(action);
    }
    if (target_type) {
      where.push('target_type = ?');
      params.push(target_type);
    }

    const whereSql = where.length ? 'WHERE ' + where.join(' AND ') : '';

    const bindParams = [...params, pageSize, offset] as (string | number)[];
    const { results } = await this.db
      .prepare(
        `SELECT * FROM logs ${whereSql} ORDER BY created_at DESC LIMIT ? OFFSET ?`
      )
      .bind(...bindParams)
      .all<Log>();

    const countRow = await this.db
      .prepare(`SELECT COUNT(*) AS total FROM logs ${whereSql}`)
      .bind(...params)
      .first<{ total: number }>();

    const total = countRow?.total ?? 0;
    return { items: results as Log[], total, page, pageSize };
  }
}
