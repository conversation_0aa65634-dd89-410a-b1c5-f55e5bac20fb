import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

import { MemoryCache } from '@/infrastructure/cache';

/**
 * MemoryCache 单元测试
 */

describe('MemoryCache', () => {
  let cache: MemoryCache;

  beforeEach(() => {
    cache = new MemoryCache();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should return null when key not found', async () => {
    const value = await cache.get('missing');
    expect(value).toBeNull();
  });

  it('should store and retrieve value without ttl', async () => {
    await cache.set('foo', { bar: 'baz' });
    const value = await cache.get<{ bar: string }>('foo');
    expect(value).toEqual({ bar: 'baz' });
  });

  it('should expire value after ttl seconds', async () => {
    await cache.set('key', 'value', 1); // 1 秒
    // 立即应可读到
    expect(await cache.get('key')).toBe('value');

    // 向前推进 1000ms
    vi.advanceTimersByTime(1000);

    // 值应过期并被删除
    expect(await cache.get('key')).toBeNull();
  });

  it('should delete value', async () => {
    await cache.set('del', 'toDelete');
    await cache.delete('del');
    expect(await cache.get('del')).toBeNull();
  });
});
