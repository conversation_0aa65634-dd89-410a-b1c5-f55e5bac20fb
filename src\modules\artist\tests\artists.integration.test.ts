import { describe, it, expect } from 'vitest';

import app from '@/app';

// 构造 mock D1 数据库
const mockDB = {
  prepare: () => ({
    all: async () => ({ results: [{ id: '1', name: 'Artist1' }] }),
    first: async () => ({ total: 1 }),
    bind: () => ({
      all: async () => ({ results: [{ id: '1', name: 'Artist1' }] }),
      first: async () => ({ id: '1', name: 'Artist1' }),
      run: async () => ({ success: true }),
    }),
  }),
};

// @ts-ignore
const Request = globalThis.Request;

function withEnv(url: string, env: any) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base), env);
}

describe('Artists API', () => {
  it('should return all artists', async () => {
    const res = await withEnv('/artists', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(Array.isArray(data.items)).toBe(true);
    expect(data.page).toBe(1);
    expect(data.pageSize).toBe(50);
    expect(data.total).toBe(1);
  });

  it('should return artist detail', async () => {
    const res = await withEnv('/artists/1', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(data.id).toBe('1');
  });

  it('should return 404 for not found artist', async () => {
    const res = await withEnv('/api/artists/not-exist-id', { DB: mockDB });
    expect(res.status).toBe(404);
  });
});
