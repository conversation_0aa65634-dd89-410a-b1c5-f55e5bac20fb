import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { D1Database } from '@cloudflare/workers-types';
import * as userService from '@/modules/user/service';
import bcrypt from 'bcryptjs';

// Mock bcrypt
vi.mock('bcryptjs', () => ({
  default: {
    hash: vi.fn(),
  },
}));

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-123'),
}));

// Mock the user repository
const mockRepo = {
  list: vi.fn(),
  findById: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  updatePassword: vi.fn(),
  delete: vi.fn(),
};

vi.mock('@/modules/user/repository', () => ({
  createUserRepository: vi.fn(() => mockRepo),
}));

describe('user/service', () => {
  let mockDB: D1Database;
  let mockCache: any;
  let mockLogger: any;

  beforeEach(() => {
    mockDB = {} as D1Database;
    mockCache = {
      get: vi.fn(),
      set: vi.fn(),
    };
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    };

    vi.clearAllMocks();
    (bcrypt.hash as any).mockResolvedValue('hashed-password');
  });

  describe('listUsers', () => {
    it('should list users and cache result when cache is available', async () => {
      const mockUsers = [
        { id: 'user-1', username: 'user1', role: 'viewer' },
        { id: 'user-2', username: 'user2', role: 'admin' },
      ];

      mockCache.get.mockResolvedValue(null); // Cache miss
      mockRepo.list.mockResolvedValue(mockUsers);

      const result = await userService.listUsers(mockDB, mockCache, mockLogger);

      expect(result).toEqual(mockUsers);
      expect(mockCache.get).toHaveBeenCalledWith('users_all');
      expect(mockCache.set).toHaveBeenCalledWith('users_all', mockUsers, 300);
      expect(mockRepo.list).toHaveBeenCalled();
    });

    it('should return cached users when available', async () => {
      const cachedUsers = [
        { id: 'cached-1', username: 'cached1', role: 'viewer' },
      ];

      mockCache.get.mockResolvedValue(cachedUsers);

      const result = await userService.listUsers(mockDB, mockCache, mockLogger);

      expect(result).toEqual(cachedUsers);
      expect(mockCache.get).toHaveBeenCalledWith('users_all');
      expect(mockCache.set).not.toHaveBeenCalled();
      expect(mockRepo.list).not.toHaveBeenCalled();
    });

    it('should work without cache', async () => {
      const mockUsers = [{ id: 'user-1', username: 'user1', role: 'viewer' }];

      mockRepo.list.mockResolvedValue(mockUsers);

      const result = await userService.listUsers(mockDB, null, mockLogger);

      expect(result).toEqual(mockUsers);
      expect(mockRepo.list).toHaveBeenCalled();
    });

    it('should work without logger', async () => {
      const mockUsers = [{ id: 'user-1', username: 'user1', role: 'viewer' }];

      mockRepo.list.mockResolvedValue(mockUsers);

      const result = await userService.listUsers(mockDB, mockCache, null);

      expect(result).toEqual(mockUsers);
      expect(mockRepo.list).toHaveBeenCalled();
    });
  });

  describe('getUser', () => {
    it('should get user by id', async () => {
      const mockUser = { id: 'user-1', username: 'testuser', role: 'viewer' };
      mockRepo.findById.mockResolvedValue(mockUser);

      const result = await userService.getUser(mockDB, 'user-1');

      expect(result).toEqual(mockUser);
      expect(mockRepo.findById).toHaveBeenCalledWith('user-1');
    });

    it('should return null when user not found', async () => {
      mockRepo.findById.mockResolvedValue(null);

      const result = await userService.getUser(mockDB, 'nonexistent');

      expect(result).toBeNull();
      expect(mockRepo.findById).toHaveBeenCalledWith('nonexistent');
    });

    it('should handle repository errors', async () => {
      const repoError = new Error('Repository error');
      mockRepo.findById.mockRejectedValue(repoError);

      await expect(userService.getUser(mockDB, 'user-1')).rejects.toThrow(
        'Repository error'
      );
    });
  });

  describe('createUser', () => {
    it('should create user with all fields', async () => {
      const input = {
        username: 'newuser',
        password: 'password123',
        role: 'admin' as const,
      };

      const mockCreatedUser = {
        id: 'mock-uuid-123',
        username: 'newuser',
        role: 'admin',
      };

      mockRepo.create.mockResolvedValue(mockCreatedUser);

      const result = await userService.createUser(mockDB, input);

      expect(result).toEqual(mockCreatedUser);
      expect(bcrypt.hash).toHaveBeenCalledWith('password123', 12);
      expect(mockRepo.create).toHaveBeenCalledWith({
        id: 'mock-uuid-123',
        username: 'newuser',
        role: 'admin',
        hashedPassword: 'hashed-password',
      });
    });

    it('should create user with default role when not specified', async () => {
      const input = {
        username: 'newuser',
        password: 'password123',
      };

      const mockCreatedUser = {
        id: 'mock-uuid-123',
        username: 'newuser',
        role: 'viewer',
      };

      mockRepo.create.mockResolvedValue(mockCreatedUser);

      const result = await userService.createUser(mockDB, input);

      expect(result).toEqual(mockCreatedUser);
      expect(mockRepo.create).toHaveBeenCalledWith({
        id: 'mock-uuid-123',
        username: 'newuser',
        role: 'viewer', // default role
        hashedPassword: 'hashed-password',
      });
    });

    it('should handle bcrypt hashing errors', async () => {
      const input = {
        username: 'newuser',
        password: 'password123',
      };

      const hashError = new Error('Hashing failed');
      (bcrypt.hash as any).mockRejectedValue(hashError);

      await expect(userService.createUser(mockDB, input)).rejects.toThrow(
        'Hashing failed'
      );
    });

    it('should handle repository creation errors', async () => {
      const input = {
        username: 'newuser',
        password: 'password123',
      };

      const repoError = new Error('Repository creation failed');
      mockRepo.create.mockRejectedValue(repoError);

      await expect(userService.createUser(mockDB, input)).rejects.toThrow(
        'Repository creation failed'
      );
    });
  });

  describe('updateUser', () => {
    it('should update user without password', async () => {
      const input = {
        username: 'updateduser',
        role: 'admin' as const,
      };

      const mockUpdatedUser = {
        id: 'user-1',
        username: 'updateduser',
        role: 'admin',
      };

      mockRepo.update.mockResolvedValue(mockUpdatedUser);

      const result = await userService.updateUser(mockDB, 'user-1', input);

      expect(result).toEqual(mockUpdatedUser);
      expect(mockRepo.update).toHaveBeenCalledWith('user-1', {
        username: 'updateduser',
        role: 'admin',
      });
      expect(mockRepo.updatePassword).not.toHaveBeenCalled();
    });

    it('should update user with password', async () => {
      const input = {
        username: 'updateduser',
        password: 'newpassword123',
      };

      const mockUpdatedUser = {
        id: 'user-1',
        username: 'updateduser',
        role: 'viewer',
      };

      mockRepo.update.mockResolvedValue(mockUpdatedUser);
      mockRepo.updatePassword.mockResolvedValue(undefined);

      const result = await userService.updateUser(mockDB, 'user-1', input);

      expect(result).toEqual(mockUpdatedUser);
      expect(mockRepo.update).toHaveBeenCalledWith('user-1', {
        username: 'updateduser',
        role: undefined,
      });
      expect(bcrypt.hash).toHaveBeenCalledWith('newpassword123', 12);
      expect(mockRepo.updatePassword).toHaveBeenCalledWith(
        'user-1',
        'hashed-password'
      );
    });

    it('should update only password', async () => {
      const input = {
        password: 'newpassword123',
      };

      const mockUpdatedUser = {
        id: 'user-1',
        username: 'existinguser',
        role: 'viewer',
      };

      mockRepo.update.mockResolvedValue(mockUpdatedUser);
      mockRepo.updatePassword.mockResolvedValue(undefined);

      const result = await userService.updateUser(mockDB, 'user-1', input);

      expect(result).toEqual(mockUpdatedUser);
      expect(mockRepo.update).toHaveBeenCalledWith('user-1', {
        username: undefined,
        role: undefined,
      });
      expect(mockRepo.updatePassword).toHaveBeenCalledWith(
        'user-1',
        'hashed-password'
      );
    });

    it('should handle password hashing errors', async () => {
      const input = {
        password: 'newpassword123',
      };

      const mockUpdatedUser = {
        id: 'user-1',
        username: 'existinguser',
        role: 'viewer',
      };

      mockRepo.update.mockResolvedValue(mockUpdatedUser);
      const hashError = new Error('Password hashing failed');
      (bcrypt.hash as any).mockRejectedValue(hashError);

      await expect(
        userService.updateUser(mockDB, 'user-1', input)
      ).rejects.toThrow('Password hashing failed');
    });

    it('should handle password update errors', async () => {
      const input = {
        password: 'newpassword123',
      };

      const mockUpdatedUser = {
        id: 'user-1',
        username: 'existinguser',
        role: 'viewer',
      };

      mockRepo.update.mockResolvedValue(mockUpdatedUser);
      const updateError = new Error('Password update failed');
      mockRepo.updatePassword.mockRejectedValue(updateError);

      await expect(
        userService.updateUser(mockDB, 'user-1', input)
      ).rejects.toThrow('Password update failed');
    });

    it('should return null when user not found', async () => {
      const input = {
        username: 'updateduser',
      };

      mockRepo.update.mockResolvedValue(null);

      const result = await userService.updateUser(mockDB, 'nonexistent', input);

      expect(result).toBeNull();
      expect(mockRepo.updatePassword).not.toHaveBeenCalled();
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      mockRepo.delete.mockResolvedValue(undefined);

      await userService.deleteUser(mockDB, 'user-1');

      expect(mockRepo.delete).toHaveBeenCalledWith('user-1');
    });

    it('should handle repository deletion errors', async () => {
      const deleteError = new Error('Deletion failed');
      mockRepo.delete.mockRejectedValue(deleteError);

      await expect(userService.deleteUser(mockDB, 'user-1')).rejects.toThrow(
        'Deletion failed'
      );
    });
  });
});
