﻿# 性能指南（Performance Guide）

## API 性能目标

- p95 响应 &lt; 200 ms
- 峰值 RPS 1 000

## 优化措施

1. **缓存**
   - 热门 GET 接口使用 Cloudflare KV，TTL 5分钟。
   - 多语言缓存隔离：缓存键包含语言参数 (`events:zh:all`)
   - 变化写操作后主动失效。
   - 搜索和Feed API缓存5分钟，提高响应速度。

2. **数据库索引**
   - `events.date_sort`、`appearances.circle_id` 均已建索引。
   - 多语言查询索引：`circle_translations(locale, name)`、`artist_translations(locale)`
   - `EXPLAIN` 计划定期审查慢查询。

3. **懒加载**
   - Controller 层按需选择 `fields`，避免过大 payload。

4. **压缩**
   - 开启 `Content-Encoding: br`。

5. **监控**
   - 使用 `console.time` + D1 query metrics，在 `stats` 表记录 slow query。

## 多语言性能优化 (v0.4.3+)

### 缓存策略

- **语言隔离缓存**：每种语言独立缓存，避免语言切换时的缓存失效
- **缓存命中率**：相同语言的请求缓存命中率可达80%+
- **缓存键设计**：`{module}:{locale}:{specific}` 格式，便于管理

### 搜索性能

- **索引优化**：为多语言字段建立复合索引
- **相关性评分**：优化搜索算法，提高匹配精度
- **结果限制**：搜索结果限制在50条以内，平衡性能和用户体验

### Feed性能

- **分页优化**：支持高效的分页查询
- **类型过滤**：支持按内容类型过滤，减少不必要的数据传输
- **混合排序**：events和circles混合排序时使用内存排序，限制数据量

### 最佳实践

1. **使用X-Locale头部**：比Accept-Language解析更快
2. **合理设置缓存时间**：5分钟TTL平衡实时性和性能
3. **数据库查询优化**：使用LEFT JOIN避免缺失翻译时的数据丢失
4. **响应格式标准化**：减少前端处理复杂度
