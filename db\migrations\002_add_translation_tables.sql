-- ----------------------------------------
-- 添加多语言翻译表支持
-- 为 circles 和 artists 添加翻译表
-- ----------------------------------------

-- 社团翻译表
DROP TABLE IF EXISTS circle_translations;
CREATE TABLE circle_translations (
  circle_id TEXT NOT NULL,                 -- 关联的社团ID
  locale TEXT NOT NULL,                    -- 语言代码 (zh, ja, en)
  name TEXT,                               -- 翻译后的社团名称
  description TEXT,                        -- 翻译后的描述
  tags TEXT,                               -- 翻译后的标签 (JSON数组)
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  -- 主键：社团ID + 语言代码
  PRIMARY KEY (circle_id, locale),
  
  -- 外键约束
  FOREIGN KEY (circle_id) REFERENCES circles(id) ON DELETE CASCADE,
  
  -- 检查约束
  CHECK(locale IN ('zh', 'ja', 'en'))
);

-- 艺术家翻译表
DROP TABLE IF EXISTS artist_translations;
CREATE TABLE artist_translations (
  artist_id TEXT NOT NULL,                 -- 关联的艺术家ID
  locale TEXT NOT NULL,                    -- 语言代码 (zh, ja, en)
  name TEXT,                               -- 翻译后的艺术家名称
  description TEXT,                        -- 翻译后的描述
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  -- 主键：艺术家ID + 语言代码
  PRIMARY KEY (artist_id, locale),
  
  -- 外键约束
  FOREIGN KEY (artist_id) REFERENCES artists(id) ON DELETE CASCADE,
  
  -- 检查约束
  CHECK(locale IN ('zh', 'ja', 'en'))
);

-- 索引优化
-- 社团翻译表索引
CREATE INDEX IF NOT EXISTS idx_circle_translations_circle_id 
ON circle_translations(circle_id);

CREATE INDEX IF NOT EXISTS idx_circle_translations_locale 
ON circle_translations(locale);

CREATE INDEX IF NOT EXISTS idx_circle_translations_name 
ON circle_translations(name);

-- 艺术家翻译表索引
CREATE INDEX IF NOT EXISTS idx_artist_translations_artist_id 
ON artist_translations(artist_id);

CREATE INDEX IF NOT EXISTS idx_artist_translations_locale 
ON artist_translations(locale);

CREATE INDEX IF NOT EXISTS idx_artist_translations_name 
ON artist_translations(name);
