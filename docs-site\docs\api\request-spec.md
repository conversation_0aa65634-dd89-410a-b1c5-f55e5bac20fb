# 后端接口与数据请求规范

> 本文档基于当前代码仓库（2025-06）整理，后续新增接口请同步维护。

## 1. 基础封装

项目统一通过 `src/lib/http.ts` 封装 `fetch`：

- 自动拼接 `baseURL`（默认 `http://localhost:8787`，可通过 `NEXT_PUBLIC_API_URL` 覆盖，推荐在 `.env.local` 中设置）。
- 请求异常时抛出自定义 `ApiError`（含 `code`、`message`）。
- 返回值统一解析为 **JSON** 并按范型参数推断类型。

```ts
const res = await http<User>('/users/1');
```

:::tip
所有直接调用后端的模块都应依赖此方法，避免在组件中出现裸 `fetch`。
:::

## 2. 通用响应格式

### 2.1 标准响应格式 (v0.4.3+)

新版API采用标准化响应格式，支持多语言和元数据：

```jsonc
{
  "success": true,                    // 请求是否成功
  "data": { ... },                   // 业务数据
  "locale": "zh",                    // 响应语言
  "timestamp": "2024-01-15T10:30:00.000Z", // 响应时间戳
  "meta": {                          // 元数据（可选）
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

### 2.2 兼容格式（旧版本）

后端已全面实施统一的“三段式”响应包装，部分接口仍遵循以下结构：

```jsonc
{
  "code": 0,          // 0 表示成功；>0 为业务错误码
  "data": { ... },    // 业务数据（失败时可省略或为空对象）
  "message": "OK"     // 人类可读信息
}
```

- 成功：`code = 0`，`data` 为具体内容。
- 失败：`code ≠ 0`，`message` 为错误描述；HTTP Status 与业务场景保持一致（如 422 / 400 / 401 / 403 / 409 / 500）。

### 2.1 错误码区段

| 区段  | 说明         | 示例                                  |
| ----- | ------------ | ------------------------------------- |
| 1xxxx | 认证 / 登录  | **10001** `InvalidUsernameOrPassword` |
| 2xxxx | 权限 / 角色  | **20001** `Forbidden`                 |
| 3xxxx | 资源状态冲突 | **30004** `AlreadyProcessed`          |
| 4xxxx | 参数校验     | **40002** `MissingField`              |
| 5xxxx | 服务器内部   | **50000** `UnknownError`              |

完整映射见 `src/errors.ts`，前端可根据 `code` 精确分支处理。

## 2.3 多语言请求头 (v0.4.3+)

API支持多语言响应，通过以下请求头控制语言：

### 语言检测优先级

1. **X-Locale** (推荐) - 自定义请求头，最高优先级
2. **Cookie** - `locale=zh` 格式
3. **Accept-Language** - 标准HTTP头部
4. **默认语言** - `en`

### 支持的语言

- `en` - English
- `zh` - 中文
- `ja` - 日本語

### 请求示例

```bash
# 使用 X-Locale 头部（推荐）
curl -H "X-Locale: zh" https://api.example.com/events

# 使用 Accept-Language 头部
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" https://api.example.com/events

# 使用 Cookie
curl -H "Cookie: locale=ja" https://api.example.com/events
```

### 响应头部

API会在响应中设置以下头部：

- `Content-Language` - 实际使用的语言
- `X-Response-Locale` - 响应语言（与Content-Language相同）
- `Vary` - `Accept-Language, X-Locale`（用于缓存控制）

## 2.4 成功响应 message 约定

后端在**写操作（POST / PUT / DELETE）**完成后，除返回常规 `code / data / message` 字段外，还会在 **HTTP Header** 附加 `X-Success-Message`，其值与响应体中的 `message` 字段相同，方便前端拦截全局提示。

后端统一通过 `jsonSuccess<T>(data, message?)` Helper 生成如下结构（TypeScript 伪代码）：

```ts
function jsonSuccess<T>(data: T, message = 'OK') {
  return c.json(
    {
      code: 0,
      data,
      message,
    },
    200,
    {
      'X-Success-Message': message,
    }
  );
}
```

### 2.2.1 常用成功提示文案

| 模块      | 方法   | 路径示例                       | 场景     | message                     |
| --------- | ------ | ------------------------------ | -------- | --------------------------- |
| Circles   | POST   | `/admin/circles`               | 新建社团 | "社团创建成功"              |
| Circles   | PUT    | `/admin/circles/:id`           | 编辑社团 | "社团已保存"                |
| Circles   | DELETE | `/admin/circles/:id`           | 删除社团 | "社团已删除"                |
| Events    | POST   | `/admin/events`                | 新建展会 | "展会创建成功"              |
| Events    | PUT    | `/admin/events/:id`            | 编辑展会 | "展会已保存"                |
| Events    | DELETE | `/admin/events/:id`            | 删除展会 | "展会已删除"                |
| Users     | POST   | `/admin/users`                 | 创建用户 | "用户创建成功"              |
| Users     | PUT    | `/admin/users/:id`             | 修改用户 | "用户信息已更新"            |
| Users     | DELETE | `/admin/users/:id`             | 删除用户 | "用户已删除"                |
| Bookmarks | POST   | `/circles/{circleId}/bookmark` | 切换收藏 | "已加入收藏" / "已取消收藏" |
| Auth      | POST   | `/auth/login`                  | 登录成功 | "登录成功"                  |
| Auth      | POST   | `/auth/logout`                 | 登出成功 | "已退出登录"                |

> 如果新增写操作接口，请更新此表以保持前后端文案一致。

## 3. 接口清单

| 模块 | 方法 | 路径                           | 查询/体参数                                   | 返回值 (`data`)                         | 封装函数            | React Hook          |
| ---- | ---- | ------------------------------ | --------------------------------------------- | --------------------------------------- | ------------------- | ------------------- |
| 展会 | GET  | `/events/{id}`                 | ‑                                             | `Event`                                 | `getEventDetail`    | `useEventDetail`    |
| 社团 | GET  | `/circles`                     | `page?`, `pageSize?`, `search?`, `category?`  | `{ items: Circle[]; total: number }`    | `getCircles`        | `useCircles`        |
| 社团 | GET  | `/events/{id}/appearances`     | `page?`, `pageSize?`, `keyword?`, `category?` | `{ items: CircleDto[]; total: number }` | `getCirclesOfEvent` | `useEventCircles`   |
| 社团 | POST | `/circles/{circleId}/bookmark` | ‑                                             | `{ isBookmarked: boolean }`             | `toggleBookmark`    | `useToggleBookmark` |

### 3.1 `/events/{id}`

- **描述**：获取展会（Event）详情。
- **示例**：

```ts
const { data: event } = useEventDetail('reitaisai-16');
```

### 3.2 `/circles`

- **描述**：获取社团列表，支持搜索和分页。
- **分页**：`page` 从 1 开始，`pageSize` 默认 50。
- **搜索**：`search` 支持按社团名称搜索。
- **分类筛选**：`category` 可传字符串，如 `comic`、`music` 等。
- **示例**：

```ts
// 基础查询
const { data: circlesRes } = await api.GET('/circles');

// 分页查询
const { data: circlesRes } = await api.GET('/circles', {
  params: { query: { page: 1, pageSize: 20 } },
});

// 搜索查询
const { data: circlesRes } = await api.GET('/circles', {
  params: { query: { search: '東方', category: 'comic' } },
});
```

**响应格式**：

```json
{
  "items": [
    {
      "id": "circle-123",
      "name": "サークル名",
      "category": "comic",
      "description": "サークル説明"
    }
  ],
  "total": 150
}
```

### 3.3 `/events/{id}/appearances`

- **描述**：查询指定展会下社团（Circle）列表。
- **分页**：`page` 从 1 开始，`pageSize` 默认 20。
- **分类筛选**：`category` 可传字符串数组或已逗号连接的字符串。
- **示例**：

```ts
const { data: circlesRes } = useEventCircles(id, {
  keyword: '少女',
  category: ['music', 'comic'],
});
```

### 3.4 `/circles/{circleId}/bookmark`

- **描述**：切换社团收藏状态，后端会返回最新状态。
- **权限**：⚠️ 仅登录用户可调用，需在 Cookie 中携带 `auth_session`；未登录时返回 **401 Unauthorized**（业务错误码 `20001`）。
- **响应码**：
  - 200 OK — 收藏状态已切换，返回 `{ isBookmarked: boolean }`
  - 401 Unauthorized — 未登录 / Cookie 失效
  - 404 Not Found — `circleId` 不存在
- **乐观更新**：`useToggleBookmark` 已内置乐观更新逻辑。

```ts
const { mutate: toggle } = useToggleBookmark();

toggle('circle-123');
```

### 3.4 `/appearances`

- **描述**：通用参展记录检索接口，支持同时按 `circle_id`、`event_id`、`page`、`pageSize` 等参数筛选。
- **重要变更**：若在查询中携带 `event_id`，后端将在响应 Header 中加入 `Deprecation: true`，提示改用推荐的 `/events/{id}/appearances` 路由。该兼容调用将在 4 周后移除。

```ts
// 示例：按 circle_id 检索
const res = await http('/appearances', {
  params: { circle_id: 'circle-123', page: 1 },
});

// 过渡期示例：按 event_id 检索（收到 Deprecation Header）
const res = await http('/appearances', {
  params: { event_id: 'reitaisai-16', page: 1 },
});
```

返回数据结构：`{ items: AppearanceDto[]; total: number; page: number; pageSize: number }`

`AppearanceDto` 字段：

| 字段      | 类型           | 说明           |
| --------- | -------------- | -------------- |
| id        | string         | 参展记录 ID    |
| circle_id | string         | 社团 ID        |
| event_id  | string         | 展会 ID        |
| artist_id | string \| null | 作者 ID        |
| booth_id  | string         | 摊位号         |
| path      | string \| null | 展馆平面图路径 |

### 3.6 `/circles/{id}/appearances`

- **描述**：获取指定社团的参展历史，按展会时间倒序。
- **分页**：同样使用 `page`、`pageSize` 参数，默认 20 条。

```ts
const { data: res } = await http('/circles/circle-123/appearances', {
  params: { page: 1 },
});
```

返回示例：

```jsonc
{
  "items": [
    {
      "event_id": "reitaisai-22",
      "event_name": "博丽神社例大祭22",
      "event_date": "2025-05-15",
      "booth_id": "A12b",
    },
  ],
  "total": 8,
  "page": 1,
  "pageSize": 20,
}
```

## 3.0 新增集中 DTO 说明

所有公开 DTO 已统一存放于 `src/schemas/` 目录，并由 `@hono/zod-openapi` 提供类型及 OpenAPI 元数据生成能力。常用对象：

| DTO 文件                      | 导出类型        | 用途示例                                      |
| ----------------------------- | --------------- | --------------------------------------------- |
| `artistSchema.ts`             | `Artist`        | `/artists`, `/admin/artists`（预留）          |
| `appearanceSchema.ts`         | `Appearance`    | `/appearances`, `/events/{id}/appearances` 等 |
| `statsSchema.ts`              | `StatsResponse` | `/admin/stats` 返回体                         |
| 其余（circle / event / user） | —               | 已于早期文档说明，此处不再赘述                |

### 3.0.1 ArtistDto 字段

| 字段        | 类型           | 说明                        |
| ----------- | -------------- | --------------------------- |
| id          | string         | 作者 ID                     |
| name        | string         | 作者昵称                    |
| urls        | string \| null | JSON 字符串，包含社交链接等 |
| description | string \| null | 介绍                        |
| created_at  | string         | 创建时间戳                  |
| updated_at  | string         | 更新时间戳                  |

### 3.0.2 StatsResponse 字段

```jsonc
{
  "totals": {
    "circles": 120,
    "artists": 300,
    "events": 25,
  },
  "year": 2025,
  "eventsByMonth": [
    { "month": "01", "count": 12 },
    { "month": "02", "count": 8 },
  ],
}
```

> 前端可直接使用 `StatsResponse['totals']` 生成看板指标。

## 4. React Query 使用约定

1. **统一 key**：`['event', id]`、`['circles', id, params]` 等，参数对象直接作为 key
   （v5 已支持深比较）。
2. **缓存时间**：查询默认 `staleTime = 5 min`，可在 Hook 内按需修改。
3. **错误处理**：全局处理放在 `src/components/react-query-provider.tsx`。
4. **占位数据**：列表接口采用 `placeholderData` 保留上一页，避免闪烁。

## 5. 新增API端点 (v0.4.3+)

### 5.1 搜索API

**端点**: `GET /search`

**功能**: 多语言搜索事件和社团

**查询参数**:

- `q` (必需) - 搜索关键词
- `type` (可选) - 搜索类型：`all`、`events`、`circles`，默认 `all`
- `page` (可选) - 页码，默认 `1`
- `limit` (可选) - 每页数量，默认 `20`

**响应示例**:

```json
{
  "success": true,
  "data": [
    {
      "type": "event",
      "id": "event-123",
      "name": "Comiket 103",
      "description": "世界最大的同人志即卖会",
      "venue_name": "东京国际展示场",
      "start_date": "2024-12-30T10:00:00Z",
      "image_url": "https://example.com/comiket103.jpg",
      "rank": 0.8567
    }
  ],
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "meta": {
    "total": 15,
    "query": "Comiket",
    "type": "events"
  }
}
```

### 5.2 Feed API

**端点**: `GET /feed`

**功能**: 获取多语言内容流

**查询参数**:

- `page` (可选) - 页码，默认 `1`
- `limit` (可选) - 每页数量，默认 `20`，最大 `100`
- `type` (可选) - 内容类型：`all`、`events`、`circles`，默认 `all`

**响应示例**:

```json
{
  "success": true,
  "data": [
    {
      "id": "feed-event-123",
      "type": "event",
      "content": {
        "id": "event-123",
        "name": "Comiket 103",
        "description": "世界最大的同人志即卖会",
        "start_date": "2024-12-30T10:00:00Z",
        "image_url": "https://example.com/comiket103.jpg"
      },
      "created_at": "2024-01-15T08:00:00Z"
    }
  ],
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "meta": {
    "total": 200,
    "page": 1,
    "limit": 20,
    "hasMore": true
  }
}
```

## 6. 未来规划

- ✅ 引入统一的 `success/data/locale/timestamp` 响应包裹。
- ✅ 使用 [Zod](https://zod.dev/) 对接口数据做运行时校验。
- ✅ 补充 E2E 与集成测试，确保接口契约。
- 🔄 完善多语言翻译表和内容管理
- 🔄 优化搜索算法和相关性评分

---

最后更新：`2025-07-28`
