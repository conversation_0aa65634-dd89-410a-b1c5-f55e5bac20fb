const widdershins = require('widdershins');
const fs = require('fs');
const path = require('path');

const options = {
  language_tabs: false,
  search: false,
  theme: 'darkula',
  user_templates: false,
  templateCallback: function (templateName, stage, data) {
    return data;
  },
  sample: true,
  schema: true,
  discovery: false,
  includes: [],
  omitHeader: true,
};

const inputFile = path.join(__dirname, '../static/openapi.json');
const outputDir = path.join(__dirname, '../docs/api-reference');

// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 读取 OpenAPI 文件
const openapi = require(inputFile);

// 生成文档
widdershins
  .convert(openapi, options)
  .then((markdown) => {
    // 写入主文档
    fs.writeFileSync(
      path.join(outputDir, 'index.md'),
      `---
id: api-reference
title: API 参考
sidebar_label: API 参考
---

${markdown}
`
    );

    console.log('API 文档生成完成！');
  })
  .catch((err) => {
    console.error('生成文档时出错：', err);
    process.exit(1);
  });
