---
sidebar_position: 1
title: 欢迎使用 Ayafeed API
description: 现代化的同人展会信息管理平台 API 文档
---

# 欢迎使用 Ayafeed API

> 🎌 现代化的同人展会信息管理平台 API

欢迎使用 **Ayafeed API** 文档！这是一个基于 Cloudflare Workers 构建的高性能 API 服务，专为同人展会信息管理而设计。

## 🎯 选择您的角色

### 👩‍💻 前端开发者

想要快速对接我们的 API？

👉 **[前端集成指南](./frontend/)** - 5分钟快速上手

**包含内容**：

- ⚡ [快速开始](./frontend/quick-start.md) - 5分钟完成API对接
- 🔧 [客户端生成](./frontend/client-generation.md) - 类型安全的客户端
- 🔐 [认证集成](./frontend/authentication.md) - 完整的用户认证流程
- 🌐 [多语言集成](./frontend/i18n-integration.md) - 国际化最佳实践

### 🏗️ 后端开发者

想要了解项目架构或参与开发？

👉 **[架构设计](./architecture/)** - 了解系统设计

👉 **[开发指南](./development/)** - 参与项目开发

### 🚀 新手用户

第一次使用？从这里开始：

👉 **[快速开始](./getting-started/)** - 环境搭建和基础概念

## 📚 文档导航

| 分类            | 文档                                           | 说明                |
| --------------- | ---------------------------------------------- | ------------------- |
| 🚀 **快速开始** | [项目概览](./getting-started/overview.md)      | 了解项目特性和架构  |
|                 | [环境准备](./getting-started/prerequisites.md) | 开发环境搭建        |
|                 | [安装部署](./getting-started/installation.md)  | 快速安装和配置      |
| 🔧 **API 文档** | [API 概览](./api/README.md)                    | 接口规范和使用说明  |
|                 | [OpenAPI 规范](./api/openapi.md)               | 完整的 API 规范文档 |
| 💻 **前端集成** | [前端指南](./frontend/README.md)               | 完整的前端集成指南  |
|                 | [快速开始](./frontend/quick-start.md)          | 5分钟快速上手       |
| 🏗️ **架构设计** | [系统架构](./architecture/system-design.md)    | 整体架构和设计决策  |
|                 | [后端架构](./architecture/backend.md)          | 后端技术栈和实现    |
| 📦 **功能模块** | [模块概览](./modules/README.md)                | 各功能模块详细说明  |
| 🛠️ **开发指南** | [贡献指南](./development/contribution.md)      | 如何参与项目开发    |
|                 | [测试指南](./development/testing.md)           | 测试最佳实践        |

## 🎯 项目特性

### ⚡ 高性能架构

- **边缘计算**: 基于 Cloudflare Workers，全球低延迟
- **无服务器**: 自动扩缩容，按需付费
- **模块化设计**: Feature-First 架构，清晰的业务边界

### 🌐 多语言支持

- **内容国际化**: 支持中文、日文、英文
- **智能检测**: 基于请求头、Cookie 和浏览器偏好
- **缓存优化**: 按语言分别缓存，提升性能

### 🔐 安全可靠

- **JWT 认证**: 安全的无状态认证
- **RBAC 权限**: 细粒度的角色权限控制
- **数据保护**: 完整的数据验证和安全策略

### 🔍 智能搜索

- **全文搜索**: 支持事件、社团、作者搜索
- **多维筛选**: 灵活的筛选条件组合
- **实时建议**: 搜索建议和自动补全

## 🚀 快速体验

```bash
# 1. 克隆项目
git clone https://github.com/ayafeed/ayafeed-api.git

# 2. 安装依赖
pnpm install

# 3. 启动开发服务器
pnpm dev

# 4. 访问 API
curl http://localhost:8787/circles
```

## 🔗 相关资源

- **🔧 GitHub 仓库**: [ayafeed/ayafeed-api](https://github.com/ayafeed/ayafeed-api)
- **📋 OpenAPI 规范**: [API 文档](./api/openapi.md)
- **📈 变更日志**: [CHANGELOG](./reference/changelog/CHANGELOG.md)
- **🐛 问题反馈**: [GitHub Issues](https://github.com/ayafeed/ayafeed-api/issues)

## 💡 需要帮助？

- 📖 查看 [常见问题](./getting-started/faq.md)
- 💬 提交 [GitHub Issue](https://github.com/ayafeed/ayafeed-api/issues)
- 📧 联系开发团队: <EMAIL>

---

**🎉 开始您的 Ayafeed API 之旅吧！**
