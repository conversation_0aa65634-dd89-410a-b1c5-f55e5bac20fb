import { Context } from 'hono';

import { UserCreateInput, UserUpdateInput } from './schema';
import * as userService from './service';
import { getDB } from '@/infrastructure';
import { C<PERSON>, Logger } from '@/infrastructure';
import { recordLog } from '@/utils/auditLog';
import { jsonError, jsonSuccess, validationError } from '@/utils/errorResponse';

/**
 * 用户列表
 */
export async function listUsers(c: Context) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');
  const users = await userService.listUsers(db, cache, logger);
  return c.json(users);
}

/**
 * 创建用户
 */
export async function createUser(c: Context) {
  const body: UserCreateInput = await c.req.json();
  if (!body.username || !body.password) {
    return validationError(c, {
      ...(body.username ? {} : { username: '必填字段' }),
      ...(body.password ? {} : { password: '必填字段' }),
    });
  }

  const db = getDB(c);
  try {
    const user = await userService.createUser(db, body);
    await recordLog(c, {
      action: 'CREATE_USER',
      targetType: 'user',
      targetId: user.id,
    });
    return jsonSuccess(c, '用户创建成功', user, 201);
  } catch (e: unknown) {
    if (e instanceof Error && /UNIQUE/.test(e.message)) {
      return jsonError(c, 10003, '唯一键冲突', 409);
    }
    throw e;
  }
}

/**
 * 获取用户详情
 */
export async function getUser(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const user = await userService.getUser(db, id);
  if (!user) return jsonError(c, 10002, '资源不存在', 404);
  return c.json(user);
}

/**
 * 更新用户
 */
export async function updateUser(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const body: UserUpdateInput = await c.req.json();

  if (
    body.username === undefined &&
    body.role === undefined &&
    body.password === undefined
  ) {
    return validationError(c, { message: '未提供有效字段' });
  }

  const updated = await userService.updateUser(db, id, body);
  await recordLog(c, {
    action: 'UPDATE_USER',
    targetType: 'user',
    targetId: id,
  });
  return jsonSuccess(c, '用户信息已更新', updated);
}

/**
 * 删除用户
 */
export async function deleteUser(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  await userService.deleteUser(db, id);
  await recordLog(c, {
    action: 'DELETE_USER',
    targetType: 'user',
    targetId: id,
  });
  return jsonSuccess(c, '用户已删除', undefined, 204);
}
