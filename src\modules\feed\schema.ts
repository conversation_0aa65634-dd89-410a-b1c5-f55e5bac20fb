import { z } from '@hono/zod-openapi';

/**
 * Feed 项内容 schema
 */
export const feedContentSchema = z.object({
  id: z.string().openapi({
    description: '资源ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  }),
  name: z.string().openapi({
    description: '名称',
    example: 'Comiket 103',
  }),
  description: z.string().nullable().openapi({
    description: '描述',
    example: '世界最大的同人志即卖会',
  }),
  start_date: z.string().nullable().optional().openapi({
    description: '开始时间（仅事件类型）',
    example: '2024-12-30T10:00:00Z',
  }),
  image_url: z.string().nullable().optional().openapi({
    description: '图片URL',
    example: 'https://example.com/comiket103.jpg',
  }),
});

/**
 * Feed 项 schema
 */
export const feedItemSchema = z.object({
  id: z.string().openapi({
    description: 'Feed项ID',
    example: 'feed-001',
  }),
  type: z.enum(['event', 'circle']).openapi({
    description: '内容类型',
    example: 'event',
  }),
  content: feedContentSchema,
  created_at: z.string().openapi({
    description: '创建时间',
    example: '2024-01-15T08:00:00Z',
  }),
});

/**
 * Feed 响应的 meta 信息
 */
export const feedMetaSchema = z.object({
  total: z.number().openapi({
    description: '总数量',
    example: 200,
  }),
  page: z.number().openapi({
    description: '当前页码',
    example: 1,
  }),
  limit: z.number().openapi({
    description: '每页数量',
    example: 20,
  }),
  hasMore: z.boolean().openapi({
    description: '是否有更多数据',
    example: true,
  }),
});

/**
 * Feed 响应 schema
 */
export const feedResponseSchema = z.object({
  success: z.boolean().openapi({ example: true }),
  data: z.array(feedItemSchema),
  locale: z.string().openapi({
    description: '响应语言',
    example: 'zh',
  }),
  timestamp: z.string().openapi({
    description: '响应时间戳',
    example: '2024-01-15T10:30:00.000Z',
  }),
  meta: feedMetaSchema,
});

export type FeedContent = z.infer<typeof feedContentSchema>;
export type FeedItem = z.infer<typeof feedItemSchema>;
export type FeedMeta = z.infer<typeof feedMetaSchema>;
export type FeedResponse = z.infer<typeof feedResponseSchema>;
