-- ========================================
-- 富文本标签页管理系统 - 数据库迁移脚本
-- 版本: v3.0.0
-- 支持: Events 和 Venues 多语言富文本标签页管理
-- ========================================

PRAGMA foreign_keys=OFF;

-- 备份现有数据（如果存在）
CREATE TABLE IF NOT EXISTS rich_text_contents_backup AS 
SELECT * FROM rich_text_contents WHERE 1=0;

INSERT INTO rich_text_contents_backup 
SELECT * FROM rich_text_contents;

-- 删除现有表和索引
DROP TABLE IF EXISTS rich_text_contents;
DROP INDEX IF EXISTS idx_rich_text_entity;
DROP INDEX IF EXISTS idx_rich_text_entity_content;
DROP INDEX IF EXISTS idx_rich_text_updated;

-- ========================================
-- 1. 内容类型配置表 (content_type_configs)
-- 支持多语言和实体类型的标签页配置管理
-- ========================================
DROP TABLE IF EXISTS content_type_configs;
CREATE TABLE content_type_configs (
  id TEXT PRIMARY KEY,                          -- UUID
  entity_type TEXT NOT NULL,                    -- 'event' | 'venue'
  language_code TEXT NOT NULL,                  -- 'en' | 'zh' | 'ja'
  key TEXT NOT NULL,                           -- 标签页键值 (如 'introduction', 'details')
  label TEXT NOT NULL,                         -- 显示名称 (如 'Introduction', '介绍')
  placeholder TEXT,                            -- 输入提示文本
  icon TEXT,                                   -- 图标名称 (Lucide 图标库)
  sort_order INTEGER NOT NULL DEFAULT 0,       -- 排序权重
  is_active BOOLEAN NOT NULL DEFAULT TRUE,     -- 启用状态
  is_preset BOOLEAN NOT NULL DEFAULT FALSE,    -- 预设保护标记
  
  -- 软删除字段
  deleted_at TEXT,                             -- 软删除时间戳
  deleted_by TEXT,                             -- 删除操作者
  
  -- 审计字段
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  -- 复合唯一约束：同一实体类型、语言、键值组合唯一
  UNIQUE(entity_type, language_code, key),
  
  -- 检查约束
  CHECK(entity_type IN ('event', 'venue')),
  CHECK(language_code IN ('en', 'zh', 'ja')),
  CHECK(length(key) >= 2 AND length(key) <= 50),
  CHECK(length(label) >= 1 AND length(label) <= 100),
  CHECK(sort_order >= 0),
  CHECK(key GLOB '[a-zA-Z0-9_-]*')             -- key 只能包含字母、数字、下划线、连字符
);

-- ========================================
-- 2. 富文本内容表 (rich_text_contents)
-- 存储实际的富文本内容数据
-- ========================================
CREATE TABLE rich_text_contents (
  id TEXT PRIMARY KEY,                          -- UUID
  entity_type TEXT NOT NULL,                    -- 'event' | 'venue'
  entity_id TEXT NOT NULL,                      -- 关联到具体的 Event 或 Venue ID
  language_code TEXT NOT NULL,                  -- 'en' | 'zh' | 'ja'
  content_type TEXT NOT NULL,                   -- 对应配置表的 key
  content TEXT NOT NULL,                        -- JSONB 格式的 Tiptap 内容
  
  -- 审计字段
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  -- 复合唯一约束：同一实体、语言、内容类型组合唯一
  UNIQUE(entity_type, entity_id, language_code, content_type),
  
  -- 检查约束
  CHECK(entity_type IN ('event', 'venue')),
  CHECK(language_code IN ('en', 'zh', 'ja')),
  CHECK(length(content_type) >= 2 AND length(content_type) <= 50),
  CHECK(length(content) <= 1000000)            -- 内容长度限制 1MB
);

-- ========================================
-- 3. 性能优化索引
-- ========================================

-- 配置表索引
-- 按实体类型和语言查询活跃配置
CREATE INDEX idx_config_entity_lang_active 
ON content_type_configs(entity_type, language_code, is_active, sort_order);

-- 按键值查询配置
CREATE INDEX idx_config_key_lookup 
ON content_type_configs(entity_type, language_code, key) 
WHERE deleted_at IS NULL;

-- 按预设标记查询
CREATE INDEX idx_config_preset 
ON content_type_configs(entity_type, language_code, is_preset) 
WHERE deleted_at IS NULL;

-- 软删除查询索引
CREATE INDEX idx_config_deleted 
ON content_type_configs(deleted_at, entity_type, language_code) 
WHERE deleted_at IS NOT NULL;

-- 内容表索引
-- 按实体和语言查询内容
CREATE INDEX idx_content_entity_lang 
ON rich_text_contents(entity_type, entity_id, language_code);

-- 按内容类型查询
CREATE INDEX idx_content_type_lookup 
ON rich_text_contents(entity_type, entity_id, language_code, content_type);

-- 按更新时间查询最近修改的内容
CREATE INDEX idx_content_updated 
ON rich_text_contents(updated_at DESC);

-- 按实体类型分组查询
CREATE INDEX idx_content_entity_type 
ON rich_text_contents(entity_type, updated_at DESC);

PRAGMA foreign_keys=ON;

-- ========================================
-- 4. 预设标签页数据初始化
-- ========================================

-- Events 实体预设标签页（三语言）
INSERT INTO content_type_configs (
  id, entity_type, language_code, key, label, placeholder, icon, 
  sort_order, is_active, is_preset
) VALUES 
-- 英语预设
('preset-event-en-intro', 'event', 'en', 'introduction', 'Introduction', 'Enter event introduction...', 'info', 0, TRUE, TRUE),
-- 中文预设  
('preset-event-zh-intro', 'event', 'zh', 'introduction', '介绍', '输入活动介绍...', 'info', 0, TRUE, TRUE),
-- 日语预设
('preset-event-ja-intro', 'event', 'ja', 'introduction', '紹介', 'イベント紹介を入力...', 'info', 0, TRUE, TRUE);

-- Venues 实体预设标签页（三语言）
INSERT INTO content_type_configs (
  id, entity_type, language_code, key, label, placeholder, icon, 
  sort_order, is_active, is_preset
) VALUES 
-- 英语预设
('preset-venue-en-overview', 'venue', 'en', 'overview', 'Overview', 'Enter venue overview...', 'map-pin', 0, TRUE, TRUE),
-- 中文预设
('preset-venue-zh-overview', 'venue', 'zh', 'overview', '概览', '输入场馆概览...', 'map-pin', 0, TRUE, TRUE),
-- 日语预设
('preset-venue-ja-overview', 'venue', 'ja', 'overview', '概要', '会場概要を入力...', 'map-pin', 0, TRUE, TRUE);

-- ========================================
-- 5. 数据迁移完成标记
-- ========================================

-- 创建迁移记录表（如果不存在）
CREATE TABLE IF NOT EXISTS migration_history (
  id TEXT PRIMARY KEY,
  migration_name TEXT NOT NULL UNIQUE,
  executed_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  description TEXT
);

-- 记录此次迁移
INSERT OR REPLACE INTO migration_history (id, migration_name, description) 
VALUES (
  'migration-001-rich-text-tabs',
  '001_rich_text_tabs_migration.sql',
  '富文本标签页管理系统 v3.0.0 - 多语言和实体类型支持'
);

-- 迁移完成提示
SELECT 'Rich Text Tabs Migration v3.0.0 completed successfully!' as status;
