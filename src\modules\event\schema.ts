import { z } from '@hono/zod-openapi';

// 展会实体 Schema（多语言支持，关联venue）
export const eventSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),

  /* ---------- 展会名称 ---------- */
  name_en: z.string().openapi({ example: 'Reitaisai 22' }),
  name_ja: z.string().openapi({ example: '第二十二回博麗神社例大祭' }),
  name_zh: z.string().openapi({ example: '第二十二回博丽神社例大祭' }),

  /* ---------- 展会日期（字符串展示用） ---------- */
  date_en: z.string().openapi({ example: 'May 3, 2025 (Sat) 10:30 – 15:30' }),
  date_ja: z
    .string()
    .openapi({ example: '2025年5月3日(土・祝) 10:30 – 15:30' }),
  date_zh: z.string().openapi({ example: '2025年5月3日(周六) 10:30 – 15:30' }),

  /* ---------- 排序 / 图片 ---------- */
  date_sort: z.number().optional().openapi({ example: 20250503 }),
  image_url: z.string().nullable().optional(),

  /* ---------- 场馆关联 ---------- */
  venue_id: z.string().openapi({ example: 'tokyo-big-sight' }),

  /* ---------- 网址 / 时间戳 ---------- */
  url: z.string().nullable().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

export type Event = z.infer<typeof eventSchema>;

// 创建请求体（不包含 id/时间戳，由后端生成）
export const eventCreateRequest = eventSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export type EventCreateInput = z.infer<typeof eventCreateRequest>;

// 更新请求体（全部字段可选）
export const eventUpdateRequest = eventCreateRequest.partial();

export type EventUpdateInput = z.infer<typeof eventUpdateRequest>;

// 兼容旧版本的Event Schema（包含venue字段）
export const legacyEventSchema = eventSchema.extend({
  // 兼容旧版本的venue字段
  venue_name: z.string().optional(),
  venue_address: z.string().optional(),
  venue_lat: z.number().optional(),
  venue_lng: z.number().optional(),

  // 关联的venue对象
  venue: z
    .object({
      id: z.string(),
      name: z.string(),
      address: z.string().nullable().optional(),
      description: z.string().nullable().optional(),
      lat: z.number(),
      lng: z.number(),
      capacity: z.number().nullable().optional(),
      website_url: z.string().nullable().optional(),
      phone: z.string().nullable().optional(),
      facilities: z.string().nullable().optional(),
      transportation: z.string().nullable().optional(),
      parking_info: z.string().nullable().optional(),
    })
    .optional(),
});

export type LegacyEvent = z.infer<typeof legacyEventSchema>;

// 多语言事件输入 Schema（用于后台管理）
export const multilingualEventInputSchema = z.object({
  id: z.string().optional(),

  // 名称 - 三种语言
  name_en: z.string().min(1, '英文名称必填'),
  name_ja: z.string().min(1, '日文名称必填'),
  name_zh: z.string().min(1, '中文名称必填'),

  // 日期 - 三种语言
  date_en: z.string().min(1, '英文日期必填'),
  date_ja: z.string().min(1, '日文日期必填'),
  date_zh: z.string().min(1, '中文日期必填'),

  // 排序日期（自动生成）
  date_sort: z.number().optional(),

  // 图片路径
  image_url: z.string().optional(),

  // 场馆关联
  venue_id: z.string().min(1, '场馆必选'),

  // 官方网站
  url: z.preprocess(
    (v) => (typeof v === 'string' && v.trim() === '' ? undefined : v),
    z.string().url('需要合法 URL').optional()
  ),
});

export type MultilingualEventInput = z.infer<
  typeof multilingualEventInputSchema
>;
