import { describe, it, expect, beforeEach, vi, type Mock } from 'vitest';
import { RichTextTabsService } from '../service-new';
import { RichTextTabsCache } from '../cache';
import type {
  ContentTypeConfigRepository,
  RichTextContentRepository,
} from '../repository-new';
import type {
  EntityType,
  LanguageCode,
  ContentTypeConfig,
  CreateConfigRequest,
} from '../schema';

// Mock repositories
const mockConfigRepo: ContentTypeConfigRepository = {
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  restore: vi.fn(),
  findById: vi.fn(),
  getActiveConfigs: vi.fn(),
  getAllConfigs: vi.fn(),
  reorder: vi.fn(),
  batchUpdateStatus: vi.fn(),
};

const mockContentRepo: RichTextContentRepository = {
  create: vi.fn(),
  update: vi.fn(),
  upsert: vi.fn(),
  delete: vi.fn(),
  findById: vi.fn(),
  getByEntity: vi.fn(),
  batchUpsert: vi.fn(),
};

// Mock cache
const mockCache = {
  getActiveConfigs: vi.fn(),
  setActiveConfigs: vi.fn(),
  getAllConfigs: vi.fn(),
  setAllConfigs: vi.fn(),
  getEntityTabs: vi.fn(),
  setEntityTabs: vi.fn(),
  invalidateConfigCache: vi.fn(),
  invalidateContentCache: vi.fn(),
} as unknown as RichTextTabsCache;

describe('RichTextTabsService', () => {
  let service: RichTextTabsService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new RichTextTabsService(
      mockConfigRepo,
      mockContentRepo,
      mockCache
    );
  });

  describe('getActiveConfigs', () => {
    const entityType: EntityType = 'event';
    const languageCode: LanguageCode = 'en';

    it('should return cached configs when available', async () => {
      const cachedConfigs: ContentTypeConfig[] = [
        {
          id: 'config-1',
          entity_type: 'event',
          language_code: 'en',
          key: 'introduction',
          label: 'Introduction',
          placeholder: 'Enter introduction...',
          icon: 'info',
          sort_order: 0,
          is_active: true,
          is_preset: true,
          deleted_at: null,
          deleted_by: null,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];

      (mockCache.getActiveConfigs as Mock).mockResolvedValue(cachedConfigs);

      const result = await service.getActiveConfigs(entityType, languageCode);

      expect(result).toEqual(cachedConfigs);
      expect(mockCache.getActiveConfigs).toHaveBeenCalledWith(
        entityType,
        languageCode
      );
      expect(mockConfigRepo.getActiveConfigs).not.toHaveBeenCalled();
    });

    it('should fetch from repository and cache when not cached', async () => {
      const dbConfigs: ContentTypeConfig[] = [
        {
          id: 'config-1',
          entity_type: 'event',
          language_code: 'en',
          key: 'introduction',
          label: 'Introduction',
          placeholder: 'Enter introduction...',
          icon: 'info',
          sort_order: 0,
          is_active: true,
          is_preset: true,
          deleted_at: null,
          deleted_by: null,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];

      (mockCache.getActiveConfigs as Mock).mockResolvedValue(null);
      (mockConfigRepo.getActiveConfigs as Mock).mockResolvedValue(dbConfigs);

      const result = await service.getActiveConfigs(entityType, languageCode);

      expect(result).toEqual(dbConfigs);
      expect(mockCache.getActiveConfigs).toHaveBeenCalledWith(
        entityType,
        languageCode
      );
      expect(mockConfigRepo.getActiveConfigs).toHaveBeenCalledWith(
        entityType,
        languageCode
      );
      expect(mockCache.setActiveConfigs).toHaveBeenCalledWith(
        entityType,
        languageCode,
        dbConfigs
      );
    });

    it('should work without cache', async () => {
      const serviceWithoutCache = new RichTextTabsService(
        mockConfigRepo,
        mockContentRepo
      );
      const dbConfigs: ContentTypeConfig[] = [];

      (mockConfigRepo.getActiveConfigs as Mock).mockResolvedValue(dbConfigs);

      const result = await serviceWithoutCache.getActiveConfigs(
        entityType,
        languageCode
      );

      expect(result).toEqual(dbConfigs);
      expect(mockConfigRepo.getActiveConfigs).toHaveBeenCalledWith(
        entityType,
        languageCode
      );
    });
  });

  describe('createConfig', () => {
    it('should create config and invalidate cache', async () => {
      const createRequest: CreateConfigRequest = {
        entity_type: 'event',
        language_code: 'en',
        key: 'details',
        label: 'Details',
        placeholder: 'Enter details...',
        icon: 'file-text',
        sort_order: 1,
        is_active: true,
      };

      const createdConfig: ContentTypeConfig = {
        id: 'config-2',
        ...createRequest,
        is_preset: false,
        deleted_at: null,
        deleted_by: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      (mockConfigRepo.create as Mock).mockResolvedValue(createdConfig);

      const result = await service.createConfig(createRequest);

      expect(result).toEqual(createdConfig);
      expect(mockConfigRepo.create).toHaveBeenCalledWith(createRequest);
      expect(mockCache.invalidateConfigCache).toHaveBeenCalledWith(
        createRequest.entity_type,
        createRequest.language_code
      );
    });
  });

  describe('updateConfig', () => {
    it('should update config and invalidate cache', async () => {
      const configId = 'config-1';
      const updateData = {
        label: 'Updated Label',
        is_active: false,
      };

      const updatedConfig: ContentTypeConfig = {
        id: configId,
        entity_type: 'event',
        language_code: 'en',
        key: 'introduction',
        label: 'Updated Label',
        placeholder: 'Enter introduction...',
        icon: 'info',
        sort_order: 0,
        is_active: false,
        is_preset: true,
        deleted_at: null,
        deleted_by: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T01:00:00Z',
      };

      (mockConfigRepo.update as Mock).mockResolvedValue(updatedConfig);

      const result = await service.updateConfig(configId, updateData);

      expect(result).toEqual(updatedConfig);
      expect(mockConfigRepo.update).toHaveBeenCalledWith(configId, updateData);
      expect(mockCache.invalidateConfigCache).toHaveBeenCalledWith(
        updatedConfig.entity_type,
        updatedConfig.language_code
      );
    });
  });

  describe('deleteConfig', () => {
    it('should delete config and invalidate cache', async () => {
      const configId = 'config-1';
      const deletedBy = 'user-123';

      const deletedConfig: ContentTypeConfig = {
        id: configId,
        entity_type: 'event',
        language_code: 'en',
        key: 'introduction',
        label: 'Introduction',
        placeholder: 'Enter introduction...',
        icon: 'info',
        sort_order: 0,
        is_active: true,
        is_preset: false,
        deleted_at: '2024-01-01T02:00:00Z',
        deleted_by: deletedBy,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T02:00:00Z',
      };

      (mockConfigRepo.delete as Mock).mockResolvedValue(deletedConfig);

      const result = await service.deleteConfig(configId, deletedBy);

      expect(result).toBe(true);
      expect(mockConfigRepo.delete).toHaveBeenCalledWith(configId, deletedBy);
      expect(mockCache.invalidateConfigCache).toHaveBeenCalledWith(
        deletedConfig.entity_type,
        deletedConfig.language_code
      );
    });

    it('should return false when config not found', async () => {
      const configId = 'non-existent';
      const deletedBy = 'user-123';

      (mockConfigRepo.delete as Mock).mockResolvedValue(null);

      const result = await service.deleteConfig(configId, deletedBy);

      expect(result).toBe(false);
      expect(mockConfigRepo.delete).toHaveBeenCalledWith(configId, deletedBy);
      expect(mockCache.invalidateConfigCache).not.toHaveBeenCalled();
    });
  });

  describe('getEntityTabs', () => {
    it('should return cached entity tabs when available', async () => {
      const entityType: EntityType = 'event';
      const entityId = 'event-123';
      const languageCode: LanguageCode = 'en';

      const cachedTabs = {
        entity_type: entityType,
        entity_id: entityId,
        language_code: languageCode,
        tabs: [],
      };

      (mockCache.getEntityTabs as Mock).mockResolvedValue(cachedTabs);

      const result = await service.getEntityTabs(
        entityType,
        entityId,
        languageCode
      );

      expect(result).toEqual(cachedTabs);
      expect(mockCache.getEntityTabs).toHaveBeenCalledWith(
        entityType,
        entityId,
        languageCode,
        false
      );
    });

    it('should fetch and combine configs with content when not cached', async () => {
      const entityType: EntityType = 'event';
      const entityId = 'event-123';
      const languageCode: LanguageCode = 'en';

      const configs: ContentTypeConfig[] = [
        {
          id: 'config-1',
          entity_type: 'event',
          language_code: 'en',
          key: 'introduction',
          label: 'Introduction',
          placeholder: 'Enter introduction...',
          icon: 'info',
          sort_order: 0,
          is_active: true,
          is_preset: true,
          deleted_at: null,
          deleted_by: null,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];

      const contents = [
        {
          id: 'content-1',
          entity_type: 'event' as EntityType,
          entity_id: 'event-123',
          language_code: 'en' as LanguageCode,
          content_type: 'introduction',
          content: '{"type":"doc","content":[]}',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];

      (mockCache.getEntityTabs as Mock).mockResolvedValue(null);
      (mockConfigRepo.getActiveConfigs as Mock).mockResolvedValue(configs);
      (mockContentRepo.getByEntity as Mock).mockResolvedValue(contents);

      const result = await service.getEntityTabs(
        entityType,
        entityId,
        languageCode
      );

      expect(result.entity_type).toBe(entityType);
      expect(result.entity_id).toBe(entityId);
      expect(result.language_code).toBe(languageCode);
      expect(result.tabs).toHaveLength(1);
      expect(result.tabs[0].config).toEqual(configs[0]);
      expect(result.tabs[0].content).toEqual(contents[0]);

      expect(mockCache.setEntityTabs).toHaveBeenCalledWith(
        entityType,
        entityId,
        languageCode,
        result,
        false
      );
    });
  });

  describe('upsertContent', () => {
    it('should upsert content and invalidate cache', async () => {
      const entityType: EntityType = 'event';
      const entityId = 'event-123';
      const languageCode: LanguageCode = 'en';
      const contentType = 'introduction';
      const content = '{"type":"doc","content":[]}';

      const upsertedContent = {
        id: 'content-1',
        entity_type: entityType,
        entity_id: entityId,
        language_code: languageCode,
        content_type: contentType,
        content: content,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      (mockContentRepo.upsert as Mock).mockResolvedValue(upsertedContent);

      const result = await service.upsertContent(
        entityType,
        entityId,
        languageCode,
        contentType,
        content
      );

      expect(result).toEqual(upsertedContent);
      expect(mockContentRepo.upsert).toHaveBeenCalledWith({
        entity_type: entityType,
        entity_id: entityId,
        language_code: languageCode,
        content_type: contentType,
        content: content,
      });
      expect(mockCache.invalidateContentCache).toHaveBeenCalledWith(
        entityType,
        entityId,
        languageCode,
        contentType
      );
    });
  });

  describe('batchUpsertContents', () => {
    it('should batch upsert contents and invalidate cache', async () => {
      const entityType: EntityType = 'event';
      const entityId = 'event-123';
      const languageCode: LanguageCode = 'en';
      const contents = {
        introduction: '{"type":"doc","content":[]}',
        details: '{"type":"doc","content":[]}',
      };

      const upsertedContents = [
        {
          id: 'content-1',
          entity_type: entityType,
          entity_id: entityId,
          language_code: languageCode,
          content_type: 'introduction',
          content: contents.introduction,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'content-2',
          entity_type: entityType,
          entity_id: entityId,
          language_code: languageCode,
          content_type: 'details',
          content: contents.details,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];

      (mockContentRepo.batchUpsert as Mock).mockResolvedValue(upsertedContents);

      const result = await service.batchUpsertContents(
        entityType,
        entityId,
        languageCode,
        contents
      );

      expect(result).toEqual(upsertedContents);
      expect(mockContentRepo.batchUpsert).toHaveBeenCalledWith([
        {
          entity_type: entityType,
          entity_id: entityId,
          language_code: languageCode,
          content_type: 'introduction',
          content: contents.introduction,
        },
        {
          entity_type: entityType,
          entity_id: entityId,
          language_code: languageCode,
          content_type: 'details',
          content: contents.details,
        },
      ]);
      expect(mockCache.invalidateContentCache).toHaveBeenCalledWith(
        entityType,
        entityId,
        languageCode
      );
    });
  });
});
