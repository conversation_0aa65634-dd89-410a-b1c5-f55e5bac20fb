import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { D1Database } from '@cloudflare/workers-types';
import { D1CircleRepository } from '@/infrastructure/db/circleRepository';

// Mock uuid
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-123'),
}));

// Mock D1 Database
const createMockD1Database = () => {
  const mockPreparedStatement = {
    bind: vi.fn().mockReturnThis(),
    all: vi.fn(),
    first: vi.fn(),
    run: vi.fn(),
  };

  return {
    prepare: vi.fn().mockReturnValue(mockPreparedStatement),
    _mockPreparedStatement: mockPreparedStatement,
  } as unknown as D1Database & { _mockPreparedStatement: any };
};

describe('D1CircleRepository', () => {
  let mockDb: D1Database & { _mockPreparedStatement: any };
  let repository: D1CircleRepository;

  beforeEach(() => {
    mockDb = createMockD1Database();
    repository = new D1CircleRepository(mockDb);
    vi.clearAllMocks();
  });

  describe('list', () => {
    it('should return list of circles ordered by name', async () => {
      const mockCircles = [
        {
          id: 'circle-1',
          name: 'Circle A',
          urls: '{"author":"Author A","twitter_url":null,"pixiv_url":null,"web_url":null}',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'circle-2',
          name: 'Circle B',
          urls: null,
          created_at: '2024-01-02T00:00:00Z',
          updated_at: '2024-01-02T00:00:00Z',
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockCircles,
      });

      const result = await repository.list();

      expect(mockDb.prepare).toHaveBeenCalledWith(
        'SELECT * FROM circles ORDER BY name ASC'
      );
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('circle-1');
      expect(result[1].id).toBe('circle-2');
    });

    it('should handle empty results', async () => {
      mockDb._mockPreparedStatement.all.mockResolvedValue({ results: [] });

      const result = await repository.list();

      expect(result).toEqual([]);
    });
  });

  describe('findById', () => {
    it('should return circle when found', async () => {
      const mockCircle = {
        id: 'circle-1',
        name: 'Test Circle',
        urls: '{"author":"Test Author","twitter_url":null,"pixiv_url":null,"web_url":null}',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockDb._mockPreparedStatement.first.mockResolvedValue(mockCircle);

      const result = await repository.findById('circle-1');

      expect(mockDb.prepare).toHaveBeenCalledWith(
        'SELECT * FROM circles WHERE id = ?'
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'circle-1'
      );
      expect(result).toEqual(mockCircle);
    });

    it('should return null when circle not found', async () => {
      mockDb._mockPreparedStatement.first.mockResolvedValue(null);

      const result = await repository.findById('nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create circle with all fields', async () => {
      const input: any = {
        name: 'New Circle',
        author: 'Test Author',
        twitter_url: 'https://twitter.com/test',
        pixiv_url: 'https://pixiv.net/test',
        web_url: 'https://test.com',
      };

      const mockCreatedCircle = {
        id: 'mock-uuid-123',
        name: 'New Circle',
        urls: '{"author":"Test Author","twitter_url":"https://twitter.com/test","pixiv_url":"https://pixiv.net/test","web_url":"https://test.com"}',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockDb._mockPreparedStatement.run.mockResolvedValue({ success: true });
      mockDb._mockPreparedStatement.first.mockResolvedValue(mockCreatedCircle);

      const result = await repository.create(input);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        'INSERT INTO circles (id, name, urls) VALUES (?, ?, ?)'
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'mock-uuid-123',
        'New Circle',
        '{"author":"Test Author","twitter_url":"https://twitter.com/test","pixiv_url":"https://pixiv.net/test","web_url":"https://test.com"}'
      );
      expect(result).toEqual(mockCreatedCircle);
    });

    it('should create circle with minimal fields', async () => {
      const input: any = {
        name: 'Minimal Circle',
      };

      const mockCreatedCircle = {
        id: 'mock-uuid-123',
        name: 'Minimal Circle',
        urls: '{"author":null,"twitter_url":null,"pixiv_url":null,"web_url":null}',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockDb._mockPreparedStatement.run.mockResolvedValue({ success: true });
      mockDb._mockPreparedStatement.first.mockResolvedValue(mockCreatedCircle);

      const result = await repository.create(input);

      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'mock-uuid-123',
        'Minimal Circle',
        '{"author":null,"twitter_url":null,"pixiv_url":null,"web_url":null}'
      );
      expect(result).toEqual(mockCreatedCircle);
    });
  });

  describe('update', () => {
    it('should update name only', async () => {
      const input: any = {
        name: 'Updated Circle Name',
      };

      const mockUpdatedCircle = {
        id: 'circle-1',
        name: 'Updated Circle Name',
        urls: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockDb._mockPreparedStatement.run.mockResolvedValue({ success: true });
      mockDb._mockPreparedStatement.first.mockResolvedValue(mockUpdatedCircle);

      const result = await repository.update('circle-1', input);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        'UPDATE circles SET name = ? WHERE id = ?'
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'Updated Circle Name',
        'circle-1'
      );
      expect(result).toEqual(mockUpdatedCircle);
    });

    it('should update URLs when circle exists with existing URLs', async () => {
      const input: any = {
        author: 'Updated Author',
        twitter_url: 'https://twitter.com/updated',
      };

      const existingCircle = {
        id: 'circle-1',
        name: 'Test Circle',
        urls: '{"author":"Original Author","twitter_url":"https://twitter.com/original","pixiv_url":"https://pixiv.net/original","web_url":"https://original.com"}',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockUpdatedCircle = {
        ...existingCircle,
        urls: '{"author":"Updated Author","twitter_url":"https://twitter.com/updated","pixiv_url":"https://pixiv.net/original","web_url":"https://original.com"}',
      };

      // First call for findById in update method
      mockDb._mockPreparedStatement.first
        .mockResolvedValueOnce(existingCircle)
        .mockResolvedValueOnce(mockUpdatedCircle); // Second call for final return

      mockDb._mockPreparedStatement.run.mockResolvedValue({ success: true });

      const result = await repository.update('circle-1', input);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        'UPDATE circles SET urls = ? WHERE id = ?'
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        '{"author":"Updated Author","twitter_url":"https://twitter.com/updated","pixiv_url":"https://pixiv.net/original","web_url":"https://original.com"}',
        'circle-1'
      );
      expect(result).toEqual(mockUpdatedCircle);
    });

    it('should update URLs when circle exists with null URLs', async () => {
      const input: any = {
        author: 'New Author',
        web_url: 'https://new.com',
      };

      const existingCircle = {
        id: 'circle-1',
        name: 'Test Circle',
        urls: null, // No existing URLs
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockUpdatedCircle = {
        ...existingCircle,
        urls: '{"author":"New Author","twitter_url":null,"pixiv_url":null,"web_url":"https://new.com"}',
      };

      // First call for findById in update method
      mockDb._mockPreparedStatement.first
        .mockResolvedValueOnce(existingCircle)
        .mockResolvedValueOnce(mockUpdatedCircle); // Second call for final return

      mockDb._mockPreparedStatement.run.mockResolvedValue({ success: true });

      const result = await repository.update('circle-1', input);

      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        '{"author":"New Author","twitter_url":null,"pixiv_url":null,"web_url":"https://new.com"}',
        'circle-1'
      );
      expect(result).toEqual(mockUpdatedCircle);
    });

    it('should return null when circle not found during URL update', async () => {
      const input: any = {
        author: 'New Author',
      };

      // Circle not found
      mockDb._mockPreparedStatement.first.mockResolvedValue(null);

      const result = await repository.update('nonexistent', input);

      expect(result).toBeNull();
      expect(mockDb._mockPreparedStatement.run).not.toHaveBeenCalled();
    });

    it('should update multiple fields including URLs', async () => {
      const input: any = {
        name: 'Updated Name',
        author: 'Updated Author',
        pixiv_url: 'https://pixiv.net/updated',
      };

      const existingCircle = {
        id: 'circle-1',
        name: 'Original Name',
        urls: '{"author":"Original Author","twitter_url":null,"pixiv_url":null,"web_url":null}',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockUpdatedCircle = {
        ...existingCircle,
        name: 'Updated Name',
        urls: '{"author":"Updated Author","twitter_url":null,"pixiv_url":"https://pixiv.net/updated","web_url":null}',
      };

      mockDb._mockPreparedStatement.first
        .mockResolvedValueOnce(existingCircle)
        .mockResolvedValueOnce(mockUpdatedCircle);

      mockDb._mockPreparedStatement.run.mockResolvedValue({ success: true });

      const result = await repository.update('circle-1', input);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        'UPDATE circles SET name = ?, urls = ? WHERE id = ?'
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'Updated Name',
        '{"author":"Updated Author","twitter_url":null,"pixiv_url":"https://pixiv.net/updated","web_url":null}',
        'circle-1'
      );
      expect(result).toEqual(mockUpdatedCircle);
    });

    it('should handle empty update input', async () => {
      const input: any = {};

      const existingCircle = {
        id: 'circle-1',
        name: 'Test Circle',
        urls: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockDb._mockPreparedStatement.first.mockResolvedValue(existingCircle);

      const result = await repository.update('circle-1', input);

      // Should not call UPDATE since no fields to update
      expect(mockDb._mockPreparedStatement.run).not.toHaveBeenCalled();
      expect(result).toEqual(existingCircle);
    });
  });

  describe('delete', () => {
    it('should delete circle by id', async () => {
      mockDb._mockPreparedStatement.run.mockResolvedValue({ success: true });

      await repository.delete('circle-1');

      expect(mockDb.prepare).toHaveBeenCalledWith(
        'DELETE FROM circles WHERE id = ?'
      );
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        'circle-1'
      );
      expect(mockDb._mockPreparedStatement.run).toHaveBeenCalled();
    });
  });
});
