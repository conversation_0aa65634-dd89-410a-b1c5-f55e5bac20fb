import { Context } from 'hono';
import { v4 as uuid } from 'uuid';

import { getDB } from './db';
import type { Logger } from './logger';

/**
 * 基于 Cloudflare D1 数据库的 Logger，实现持久化审计日志。
 *
 * 适配 Better Auth 用户结构：
 * - 用户信息从 Better Auth 的 user 对象获取 (id, email, name)
 * - username 字段使用 name 或 email 作为显示名称
 * - `message` 作为 action 字段存储，例如 CREATE_EVENT
 * - `meta` 可包含 targetType / targetId 及其它任意键值
 */
export class D1Logger implements Logger {
  constructor(private readonly c: Context) {}

  private async write(message: string, meta?: Record<string, unknown>) {
    // 获取当前登录用户 - 适配 Better Auth
    const auth = this.c.get('auth') as
      | { user?: { id: string; email: string; name?: string } }
      | undefined;
    const user = auth?.user;
    if (!user) return; // 未登录跳过记录

    const { targetType, targetId, ...rest } = (meta || {}) as any;
    const metaStr = Object.keys(rest).length ? JSON.stringify(rest) : null;

    // 生成用户名：优先使用 name，否则使用 email
    const username = user.name || user.email;

    const db = getDB(this.c);
    const id = uuid();
    await db
      .prepare(
        'INSERT INTO logs (id, user_id, username, action, target_type, target_id, meta) VALUES (?, ?, ?, ?, ?, ?, ?)'
      )
      .bind(id, user.id, username, message, targetType, targetId, metaStr)
      .run();
  }

  info(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
  warn(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
  error(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
  debug(message: string, meta?: Record<string, unknown>) {
    return this.write(message, meta);
  }
}
