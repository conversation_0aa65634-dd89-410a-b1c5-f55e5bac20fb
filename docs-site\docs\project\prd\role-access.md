# 角色与权限接入说明（Role Guard）

> 面向：前端开发
> 目的：配合后端的 `roleGuard` 中间件，实现多角色 UI 与权限控制。
> 更新时间：2025-07-11

---

## 1. 角色枚举

| 角色   | 说明       | 典型权限                 |
| ------ | ---------- | ------------------------ |
| admin  | 超级管理员 | 所有接口读写、用户管理   |
| editor | 内容编辑   | 新建/编辑 Event & Circle |
| viewer | 只读访客   | 仅查看公开接口           |

> 后端 JWT 的 `role` 字段即上表值。
> 未登录请求视为 `viewer` 角色，仅能访问公开只读接口。

---

## 2. Access / Refresh Token 过期策略

| Token         | 过期时间 | 刷新方式                                      | 吊销机制                       |
| ------------- | -------- | --------------------------------------------- | ------------------------------ |
| Access Token  | 15 min   | 请求返回 401 时，前端自动调用 `/auth/refresh` | `ver` 字段版本号不匹配即失效   |
| Refresh Token | 14 d     | 登录或 `/auth/refresh` 时重置滑动窗口         | 服务端删除 Cookie / 版本号递增 |

---

## 3. 角色枚举与存储映射

数据库 `users.role` 字段使用 ENUM(`admin`,`editor`,`viewer`) 与下表一一对应。

| 枚举值 | 前端常量    | 权限等级 |
| ------ | ----------- | -------- |
| admin  | ROLE_ADMIN  | 3        |
| editor | ROLE_EDITOR | 2        |
| viewer | ROLE_VIEWER | 1        |

> 后续新增角色需：① 迁移数据库枚举 → ② 更新 `roleGuard` → ③ 修改此文档。

---

## 4. 典型页面 – 角色访问表

| 页面 / 模块                   | viewer | editor | admin |
| ----------------------------- | ------ | ------ | ----- |
| 展会列表 `/events`            | ✅     | ✅     | ✅    |
| 展会编辑 `/admin/events/edit` | ❌     | ✅     | ✅    |
| 社团管理 `/admin/circles`     | ❌     | ✅     | ✅    |
| 用户管理 `/admin/users`       | ❌     | ❌     | ✅    |
| 统计面板 `/admin/stats`       | ❌     | ❌     | ✅    |
| 操作日志 `/admin/logs`        | ❌     | ❌     | ✅    |

---

## 2. 后端约束

1. 登录 `/auth/login` 返回示例：
   ```jsonc
   {
     "code": 0,
     "data": {
       "accessToken": "<jwt>",
       "user": { "id": "u1", "username": "alice", "role": "editor" },
     },
     "message": "登录成功",
   }
   ```
2. 受限接口：
   | PATH | 允许角色 |
   | ----------------------- | ------------------- |
   | 全部 /admin/events/_ | admin, editor |
   | 全部 /admin/circles/_ | admin, editor |
   | 全部 /admin/users/_ | admin |
   | 全部 /admin/logs/_ | admin |
   | 全部 /admin/stats/\* | admin |

   未授权访问返回：

   ```jsonc
   { "code": 20001, "message": "权限不足" }
   ```

---

## 3. 前端实现步骤

### 3.1 登录时保存角色

```ts
const resp = await request<{ role: 'admin' | 'editor' | 'viewer' }>(
  '/auth/login',
  {
    method: 'POST',
    body: JSON.stringify({ username, password }),
    skipAuth: true,
  }
);
setUser({ role: resp.role });
```

### 3.2 页面、菜单守卫

```tsx
// 仅 admin 可见的菜单
{
  user.role === 'admin' && <MenuItem to="/users">用户管理</MenuItem>;
}

// React Router v7 路由守卫
const PrivateRoute = ({ allow }: { allow: UserRole[] }) =>
  allow.includes(user.role) ? <Outlet /> : <Navigate to="/403" />;
```

### 3.3 刷新后重新拉取角色

```ts
const { data: profile } = await request('/auth/profile');
setUser({ role: profile.role });
```

---

## 4. 403 处理

在 `utils/request.ts` 中：

```ts
if (res.status === 403) {
  notification.error({ message: '权限不足' });
  router.replace('/403');
  return Promise.reject(err);
}
```

---

## 5. 常见问题

1. **角色改变后不生效？**
   - 修改角色后端会重新签发 JWT，前端重新登录或刷新即可。
2. **viewer 访问 admin 路由？**
   - 后端会 403，前端可提前在路由守卫阻断。

如有疑问，请在 #frontend 频道联系后端。
