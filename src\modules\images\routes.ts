import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import * as imageController from './controller';
import { imageSchema, imageListQuery, imageBatchQuery } from './schema';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

// 公共 Images 只读接口
const pubImages = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI Routes ----------

// 获取资源的图片列表
const getImagesByResourceRoute = createRoute({
  method: 'get',
  path: '/{category}/{resourceId}',
  summary: '获取资源的图片列表',
  tags: ['Images'],
  request: {
    params: z.object({
      category: z.enum(['event', 'circle', 'venue']).openapi({
        example: 'event',
        description: '资源分类',
      }),
      resourceId: z.string().openapi({
        example: 'resource-uuid-789',
        description: '资源ID',
      }),
    }),
    query: imageListQuery,
  },
  responses: {
    200: {
      description: '图片列表',
      content: {
        'application/json': {
          schema: z.object({
            code: z.number().openapi({ example: 0 }),
            message: z.string().openapi({ example: 'OK' }),
            data: z.object({
              images: z.array(imageSchema),
              pagination: z.object({
                page: z.number().openapi({ example: 1 }),
                pageSize: z.number().openapi({ example: 20 }),
                total: z.number().openapi({ example: 100 }),
                totalPages: z.number().openapi({ example: 5 }),
              }),
            }),
          }),
        },
      },
    },
  },
});

// 获取单个图片信息
const getImageByIdRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '获取单个图片信息',
  tags: ['Images'],
  request: {
    params: z.object({
      id: z.string().openapi({
        example: 'uuid-123',
        description: '图片ID',
      }),
    }),
  },
  responses: {
    200: {
      description: '图片信息',
      content: {
        'application/json': {
          schema: z.object({
            code: z.number().openapi({ example: 0 }),
            message: z.string().openapi({ example: 'OK' }),
            data: imageSchema,
          }),
        },
      },
    },
    404: {
      description: '图片不存在',
    },
  },
});

// 批量查询图片
const getBatchImagesRoute = createRoute({
  method: 'get',
  path: '/batch',
  summary: '批量查询图片',
  tags: ['Images'],
  request: {
    query: imageBatchQuery,
  },
  responses: {
    200: {
      description: '批量查询结果',
      content: {
        'application/json': {
          schema: z.object({
            code: z.number().openapi({ example: 200 }),
            message: z.string().openapi({ example: '批量查询成功' }),
            data: z.record(z.string(), imageSchema.nullable()).openapi({
              example: {
                event1: {
                  id: 'img-456',
                  variant: 'medium',
                  file_path: '/images/events/event1/poster_medium.jpg',
                },
                event2: {
                  id: 'img-789',
                  variant: 'medium',
                  file_path: '/images/events/event2/poster_medium.jpg',
                },
                event3: null,
              },
            }),
          }),
        },
      },
    },
    400: {
      description: '参数错误',
    },
  },
});

// 直接访问图片文件
const serveImageRoute = createRoute({
  method: 'get',
  path: '/{id}/file',
  summary: '直接访问图片文件',
  tags: ['Images'],
  request: {
    params: z.object({
      id: z.string().openapi({
        example: 'uuid-123',
        description: '图片ID',
      }),
    }),
  },
  responses: {
    200: {
      description: '图片文件',
      content: {
        'image/*': {
          schema: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
    404: {
      description: '图片不存在',
    },
  },
});

// 注册路由 - 注意顺序：更具体的路由要先注册
registerOpenApiRoute(
  pubImages,
  getBatchImagesRoute,
  imageController.getBatchImages
);
registerOpenApiRoute(pubImages, serveImageRoute, imageController.serveImage);
registerOpenApiRoute(
  pubImages,
  getImageByIdRoute,
  imageController.getImageById
);
registerOpenApiRoute(
  pubImages,
  getImagesByResourceRoute,
  imageController.getImagesByResource
);

export { pubImages as routes };
