import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { Context } from 'hono';
import { getDB } from '@/infrastructure';
import { jsonError } from '@/utils/errorResponse';

// Copy the handler functions for unit testing
async function listHandler(c: Context) {
  const db = getDB(c);
  const circle_id = c.req.query('circle_id');
  const event_id = c.req.query('event_id');

  const search = new URL(c.req.url).searchParams;
  const page = Math.max(Number(search.get('page') || '1'), 1);
  const pageSize = Math.max(Number(search.get('pageSize') || '50'), 1);
  const offset = (page - 1) * pageSize;

  let baseSql = 'FROM appearances';
  const conditions: string[] = [];
  const params: (string | number)[] = [];

  if (circle_id) {
    conditions.push('circle_id = ?');
    params.push(circle_id);
  }
  if (event_id) {
    conditions.push('event_id = ?');
    params.push(event_id);
  }

  if (conditions.length) {
    baseSql += ' WHERE ' + conditions.join(' AND ');
  }

  // --- total count ---
  const totalRes = await db
    .prepare(`SELECT COUNT(*) AS total ${baseSql}`)
    .bind(...params)
    .first<{ total: number }>();
  const total = totalRes?.total || 0;

  // --- data ---
  const { results: items } = await db
    .prepare(`SELECT * ${baseSql} ORDER BY created_at DESC LIMIT ? OFFSET ?`)
    .bind(...params, pageSize, offset)
    .all();

  // 若通过 event_id 查询，提示已弃用（建议改用 /events/{id}/appearances）
  if (event_id) {
    c.header('Deprecation', 'true');
  }

  return c.json({ items, total, page, pageSize });
}

async function detailHandler(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  const record = await db
    .prepare('SELECT * FROM appearances WHERE id = ?')
    .bind(id)
    .first();
  if (!record) return jsonError(c, 50001, 'Not Found', 404);
  return c.json(record);
}

async function createHandler(c: Context) {
  const db = getDB(c);
  const body = await c.req.json();
  const { id, circle_id, event_id, artist_id, booth_id, path } = body;
  if (!booth_id) return jsonError(c, 50002, 'booth_id is required', 400);
  await db
    .prepare(
      'INSERT INTO appearances (id, circle_id, event_id, artist_id, booth_id, path) VALUES (?, ?, ?, ?, ?, ?)'
    )
    .bind(id, circle_id, event_id, artist_id, booth_id, path)
    .run();
  return c.json({ success: true });
}

async function deleteHandler(c: Context) {
  const db = getDB(c);
  const id = c.req.param('id');
  await db.prepare('DELETE FROM appearances WHERE id = ?').bind(id).run();
  return c.json({ success: true });
}

// Mock dependencies
vi.mock('@/infrastructure');
vi.mock('@/utils/errorResponse');

// Helper function to create mock context
const createMockContext = (overrides: any = {}) => {
  const mockDB = {
    prepare: vi.fn().mockReturnValue({
      bind: vi.fn().mockReturnThis(),
      first: vi.fn(),
      all: vi.fn(),
      run: vi.fn(),
    }),
  };

  const defaultReq = {
    url: 'http://localhost/',
    query: vi.fn(() => null), // Default to return null for all queries
    param: vi.fn(),
    json: vi.fn(),
  };

  return {
    req: {
      ...defaultReq,
      ...overrides.req,
      // Ensure query function is always available
      query: overrides.req?.query || defaultReq.query,
    },
    json: vi.fn((data) => ({
      status: 200,
      json: async () => data,
    })),
    header: vi.fn(),
    env: { DB: mockDB },
    _mockDB: mockDB,
    ...overrides,
  };
};

describe('appearance/routes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getDB as any).mockImplementation((c) => c.env.DB);
    (jsonError as any).mockReturnValue({
      status: 404,
      json: async () => ({ code: 50001, message: 'Not Found' }),
    });
  });

  describe('listHandler', () => {
    it('should return paginated appearances with default parameters', async () => {
      const mockAppearances = [
        { id: '1', circle_id: 'c1', event_id: 'e1', booth_id: 'A01a' },
        { id: '2', circle_id: 'c2', event_id: 'e1', booth_id: 'A01b' },
      ];

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/',
          query: vi.fn(() => null),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 2 });
      mockContext._mockDB
        .prepare()
        .all.mockResolvedValue({ results: mockAppearances });

      await listHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        items: mockAppearances,
        total: 2,
        page: 1,
        pageSize: 50,
      });

      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT COUNT(*) AS total FROM appearances'
      );
      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT * FROM appearances ORDER BY created_at DESC LIMIT ? OFFSET ?'
      );
    });

    it('should handle custom page and pageSize parameters', async () => {
      const mockAppearances = [
        { id: '3', circle_id: 'c3', event_id: 'e2', booth_id: 'B01a' },
      ];

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/?page=2&pageSize=10',
          query: vi.fn(() => null),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 15 });
      mockContext._mockDB
        .prepare()
        .all.mockResolvedValue({ results: mockAppearances });

      await listHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        items: mockAppearances,
        total: 15,
        page: 2,
        pageSize: 10,
      });

      // Check that bind was called with correct offset (page 2, pageSize 10 = offset 10)
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(10, 10);
    });

    it('should filter by circle_id', async () => {
      const mockAppearances = [
        { id: '1', circle_id: 'c1', event_id: 'e1', booth_id: 'A01a' },
      ];

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/?circle_id=c1',
          query: vi.fn((key) => (key === 'circle_id' ? 'c1' : null)),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 1 });
      mockContext._mockDB
        .prepare()
        .all.mockResolvedValue({ results: mockAppearances });

      await listHandler(mockContext as any);

      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT COUNT(*) AS total FROM appearances WHERE circle_id = ?'
      );
      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT * FROM appearances WHERE circle_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith('c1');
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'c1',
        50,
        0
      );
    });

    it('should filter by event_id and set deprecation header', async () => {
      const mockAppearances = [
        { id: '1', circle_id: 'c1', event_id: 'e1', booth_id: 'A01a' },
      ];

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/?event_id=e1',
          query: vi.fn((key) => (key === 'event_id' ? 'e1' : null)),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 1 });
      mockContext._mockDB
        .prepare()
        .all.mockResolvedValue({ results: mockAppearances });

      await listHandler(mockContext as any);

      expect(mockContext.header).toHaveBeenCalledWith('Deprecation', 'true');
      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT COUNT(*) AS total FROM appearances WHERE event_id = ?'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith('e1');
    });

    it('should filter by both circle_id and event_id', async () => {
      const mockAppearances = [
        { id: '1', circle_id: 'c1', event_id: 'e1', booth_id: 'A01a' },
      ];

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/?circle_id=c1&event_id=e1',
          query: vi.fn((key) => {
            if (key === 'circle_id') return 'c1';
            if (key === 'event_id') return 'e1';
            return null;
          }),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 1 });
      mockContext._mockDB
        .prepare()
        .all.mockResolvedValue({ results: mockAppearances });

      await listHandler(mockContext as any);

      expect(mockContext.header).toHaveBeenCalledWith('Deprecation', 'true');
      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT COUNT(*) AS total FROM appearances WHERE circle_id = ? AND event_id = ?'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'c1',
        'e1'
      );
    });

    it('should handle zero total count', async () => {
      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/',
          query: vi.fn(() => null),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 0 });
      mockContext._mockDB.prepare().all.mockResolvedValue({ results: [] });

      await listHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        items: [],
        total: 0,
        page: 1,
        pageSize: 50,
      });
    });

    it('should handle null total count', async () => {
      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/',
          query: vi.fn(() => null),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue(null);
      mockContext._mockDB.prepare().all.mockResolvedValue({ results: [] });

      await listHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({
        items: [],
        total: 0,
        page: 1,
        pageSize: 50,
      });
    });

    it('should enforce minimum page value of 1', async () => {
      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/?page=0',
          query: vi.fn(() => null),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 0 });
      mockContext._mockDB.prepare().all.mockResolvedValue({ results: [] });

      await listHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 1, // Math.max(0, 1) = 1
        })
      );
    });

    it('should enforce minimum pageSize value of 1', async () => {
      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/?pageSize=0',
          query: vi.fn(() => null),
        },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue({ total: 0 });
      mockContext._mockDB.prepare().all.mockResolvedValue({ results: [] });

      await listHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          pageSize: 1, // Math.max(0, 1) = 1
        })
      );
    });
  });

  describe('detailHandler', () => {
    it('should return appearance when found', async () => {
      const mockAppearance = {
        id: '1',
        circle_id: 'c1',
        event_id: 'e1',
        booth_id: 'A01a',
      };

      const mockContext = createMockContext({
        req: { param: vi.fn(() => '1') },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue(mockAppearance);

      await detailHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith(mockAppearance);
      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'SELECT * FROM appearances WHERE id = ?'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith('1');
    });

    it('should return 404 when appearance not found', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'nonexistent') },
      });

      mockContext._mockDB.prepare().first.mockResolvedValue(null);

      const result = await detailHandler(mockContext as any);

      expect(result.status).toBe(404);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50001,
        'Not Found',
        404
      );
    });
  });

  describe('createHandler', () => {
    it('should create appearance successfully', async () => {
      const mockBody = {
        id: 'new-id',
        circle_id: 'c1',
        event_id: 'e1',
        artist_id: 'a1',
        booth_id: 'A01a',
        path: '/path/to/image',
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await createHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'INSERT INTO appearances (id, circle_id, event_id, artist_id, booth_id, path) VALUES (?, ?, ?, ?, ?, ?)'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'new-id',
        'c1',
        'e1',
        'a1',
        'A01a',
        '/path/to/image'
      );
    });

    it('should create appearance with minimal required fields', async () => {
      const mockBody = {
        id: 'new-id',
        booth_id: 'A01a',
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await createHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'new-id',
        undefined, // circle_id
        undefined, // event_id
        undefined, // artist_id
        'A01a',
        undefined // path
      );
    });

    it('should return 400 when booth_id is missing', async () => {
      const mockBody = {
        id: 'new-id',
        circle_id: 'c1',
        event_id: 'e1',
        // booth_id is missing
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      (jsonError as any).mockReturnValue({
        status: 400,
        json: async () => ({ code: 50002, message: 'booth_id is required' }),
      });

      const result = await createHandler(mockContext as any);

      expect(result.status).toBe(400);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'booth_id is required',
        400
      );
      expect(mockContext._mockDB.prepare().run).not.toHaveBeenCalled();
    });

    it('should return 400 when booth_id is empty string', async () => {
      const mockBody = {
        id: 'new-id',
        booth_id: '', // empty string
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      (jsonError as any).mockReturnValue({
        status: 400,
        json: async () => ({ code: 50002, message: 'booth_id is required' }),
      });

      const result = await createHandler(mockContext as any);

      expect(result.status).toBe(400);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'booth_id is required',
        400
      );
    });

    it('should return 400 when booth_id is null', async () => {
      const mockBody = {
        id: 'new-id',
        booth_id: null,
      };

      const mockContext = createMockContext({
        req: { json: vi.fn().mockResolvedValue(mockBody) },
      });

      (jsonError as any).mockReturnValue({
        status: 400,
        json: async () => ({ code: 50002, message: 'booth_id is required' }),
      });

      const result = await createHandler(mockContext as any);

      expect(result.status).toBe(400);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        50002,
        'booth_id is required',
        400
      );
    });
  });

  describe('deleteHandler', () => {
    it('should delete appearance successfully', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => '1') },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await deleteHandler(mockContext as any);

      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
      expect(mockContext._mockDB.prepare).toHaveBeenCalledWith(
        'DELETE FROM appearances WHERE id = ?'
      );
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith('1');
    });

    it('should handle deletion of non-existent appearance', async () => {
      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'nonexistent') },
      });

      mockContext._mockDB.prepare().run.mockResolvedValue({ success: true });

      await deleteHandler(mockContext as any);

      // Should still return success even if record doesn't exist
      expect(mockContext.json).toHaveBeenCalledWith({ success: true });
      expect(mockContext._mockDB.prepare().bind).toHaveBeenCalledWith(
        'nonexistent'
      );
    });
  });
});
