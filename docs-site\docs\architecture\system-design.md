# Backend Modular Architecture

## 引言

本文件详细阐述 Ayafeed API 自 v0.4.x 起采用的 Feature-First 模块化架构设计，旨在提高可维护性、可测试性与模块内聚性。该架构遵循领域驱动设计（DDD）原则，每个功能模块都是自包含的。

---

## 目录结构总览

```text
src/
├── app.ts              # 顶层 OpenAPIHono 实例和中间件配置
├── modules/            # 功能模块（Feature-First 架构）
│   ├── admin/          # 后台管理聚合模块
│   │   └── routes.ts   # 聚合所有后台路由
│   ├── auth/           # 认证模块
│   │   ├── controller.ts
│   │   ├── service.ts
│   │   ├── schema.ts
│   │   ├── routes.ts
│   │   └── repository.ts
│   ├── circle/         # 社团模块
│   │   ├── controller.ts
│   │   ├── service.ts
│   │   ├── schema.ts
│   │   ├── routes.ts      # 公开路由
│   │   ├── adminRoutes.ts # 后台路由
│   │   └── repository.ts
│   ├── event/          # 事件模块
│   ├── search/         # 搜索模块
│   ├── feed/           # 内容流模块
│   └── ...             # 其他功能模块
├── middlewares/        # 全局中间件
├── infrastructure/     # 基础设施抽象层
├── schemas/            # 通用 Zod Schema
├── utils/              # 工具函数
├── types.ts            # 全局类型定义
└── index.ts            # Cloudflare Worker 入口
```

> **架构演进**：自 v0.4.2 起，完全采用 Feature-First 模块化架构，每个模块都是自包含的，包含自己的 controller、service、schema、routes 等文件。

---

## 模块内部结构与职责

每个功能模块都遵循统一的内部结构：

| 文件              | 主要职责                                                         | 依赖方向                              | 代码示例                                                     |
| ----------------- | ---------------------------------------------------------------- | ------------------------------------- | ------------------------------------------------------------ |
| **routes.ts**     | • 定义 OpenAPI 路由 <br />• 注册路由处理器 <br />• 公开 API 端点 | Routes → Controller / Schema          | `registerOpenApiRoute(routes, listRoute, controller.list)`   |
| **controller.ts** | • 解析请求参数 <br />• 调用 Service <br />• 返回响应             | Controller → Service / Schema         | `async (c) => jsonSuccess(await service.list(params))`       |
| **service.ts**    | • 业务逻辑处理 <br />• 调用 Repository <br />• 缓存管理          | Service → Repository / Infrastructure | `export const list = async (db) => repo.findAll()`           |
| **repository.ts** | • 数据访问抽象 <br />• SQL 查询封装 <br />• 数据转换             | Repository → DB / Schema              | `export const createRepo = (db) => ({ findAll: () => ... })` |
| **schema.ts**     | • Zod 数据模型 <br />• OpenAPI 元数据 <br />• 类型定义           | 无外部依赖                            | `export const circleSchema = z.object({...})`                |

### 依赖原则

1. **模块内单向依赖**：Routes → Controller → Service → Repository → Schema
2. **模块间隔离**：禁止直接引用其他模块的内部实现
3. **基础设施共享**：通过 `src/infrastructure/` 和 `src/middlewares/` 共享基础功能

---

## 代码示例（模块化架构）

### src/modules/circle/schema.ts

```ts
import { z } from 'zod';

export const circleSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  description: z.string().nullable(),
  created_at: z.string(),
  updated_at: z.string(),
});

export const circleCreateRequest = circleSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export type Circle = z.infer<typeof circleSchema>;
export type CircleCreateRequest = z.infer<typeof circleCreateRequest>;
```

### src/modules/circle/repository.ts

```ts
import type { D1Database } from '@cloudflare/workers-types';
import type { Circle, CircleCreateRequest } from './schema';

export function createCircleRepository(db: D1Database) {
  return {
    async findAll(): Promise<Circle[]> {
      const { results } = await db.prepare('SELECT * FROM circles').all();
      return results as Circle[];
    },

    async findById(id: string): Promise<Circle | null> {
      const result = await db
        .prepare('SELECT * FROM circles WHERE id = ?')
        .bind(id)
        .first();
      return result as Circle | null;
    },

    async create(data: CircleCreateRequest): Promise<Circle> {
      const id = crypto.randomUUID();
      const now = new Date().toISOString();
      const result = await db
        .prepare(
          `
        INSERT INTO circles (id, name, description, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `
        )
        .bind(id, data.name, data.description, now, now)
        .run();

      return { id, ...data, created_at: now, updated_at: now };
    },
  };
}
```

### src/modules/circle/service.ts

```ts
import type { D1Database } from '@cloudflare/workers-types';
import { createCircleRepository } from './repository';
import type { Circle, CircleCreateRequest } from './schema';

export async function listCircles(db: D1Database): Promise<Circle[]> {
  const repo = createCircleRepository(db);
  return repo.findAll();
}

export async function getCircle(
  db: D1Database,
  id: string
): Promise<Circle | null> {
  const repo = createCircleRepository(db);
  return repo.findById(id);
}

export async function createCircle(
  db: D1Database,
  data: CircleCreateRequest
): Promise<Circle> {
  const repo = createCircleRepository(db);
  return repo.create(data);
}
```

### src/modules/circle/controller.ts

```ts
import type { Context } from 'hono';
import { getDB } from '@/utils/db';
import { jsonSuccess, jsonError } from '@/utils/response';
import * as circleService from './service';
import type { HonoApp } from '@/types';

export async function listCircles(c: Context<HonoApp>) {
  const db = getDB(c);
  const circles = await circleService.listCircles(db);
  return jsonSuccess(c, circles);
}

export async function getCircle(c: Context<HonoApp>) {
  const db = getDB(c);
  const id = c.req.param('id');
  const circle = await circleService.getCircle(db, id);

  if (!circle) {
    return jsonError(c, 60002, '社团不存在', 404);
  }

  return jsonSuccess(c, circle);
}
```

### src/modules/circle/routes.ts

```ts
import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import { registerOpenApiRoute } from '@/utils/openapiHelper';
import { successResponse, paginatedResult } from '@/schemas/common';
import { HonoApp } from '@/types';
import * as circleController from './controller';
import { circleSchema } from './schema';

const routes = new OpenAPIHono<HonoApp>();

const listCirclesRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '获取社团列表',
  tags: ['Circles'],
  responses: {
    200: {
      description: '社团列表',
      content: {
        'application/json': { schema: paginatedResult(circleSchema) },
      },
    },
  },
});

const getCircleRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '获取社团详情',
  tags: ['Circles'],
  request: {
    params: z.object({ id: z.string() }),
  },
  responses: {
    200: {
      description: '社团详情',
      content: {
        'application/json': { schema: successResponse(circleSchema) },
      },
    },
  },
});

registerOpenApiRoute(routes, listCirclesRoute, circleController.listCircles);
registerOpenApiRoute(routes, getCircleRoute, circleController.getCircle);

export { routes };
```

---

## 命名与组织规范

1. **文件命名**：采用小驼峰 + Layer 后缀，如 `circleService.ts`、`eventController.ts`。
2. **导入路径**：使用 `@/` alias（见 `tsconfig.json` paths）防止相对路径穿透。
3. **返回格式**：控制器统一使用 `jsonSuccess()` / `jsonError()` 封装，配合 `X-Success-Message` 头。
4. **错误处理**：Service 抛出业务异常（继承 `AppError`），由全局错误处理中间件转换为 HTTP 响应。
5. **测试**：Service 层使用 Vitest 单元测试；Controller 通过 Hono 原生 `inject` 或 Supertest 进行集成测试。

---

## 迁移策略（摘要）

1. **Circles 模块试点** → 通过全部测试。
2. 逐模块迁移（Events → Users → Logs → Stats）。
3. 已删除 `src/routes/` 旧代码，并在 CI 中添加无引用检查。
4. 完成后发布 0.3.x 次版本，并更新 Changelog。

---

> 若对本架构文档有任何疑问或改进建议，请在 PR 中评论。
