import { describe, it, expect, vi } from 'vitest';

import { KVCache } from '@/infrastructure/kvCache';

describe('KVCache', () => {
  it('should return parsed JSON when stored as JSON string', async () => {
    const kv: any = {
      get: vi.fn().mockResolvedValue(JSON.stringify({ foo: 'bar' })),
      put: vi.fn(),
      delete: vi.fn(),
    };

    const cache = new KVCache(kv);
    const result = await cache.get<{ foo: string }>('key');

    expect(kv.get).toHaveBeenCalledWith('key');
    expect(result).toEqual({ foo: 'bar' });
  });

  it('should return raw string when stored string', async () => {
    const kv: any = {
      get: vi.fn().mockResolvedValue('plain-text'),
      put: vi.fn(),
      delete: vi.fn(),
    };

    const cache = new KVCache(kv);
    const result = await cache.get<string>('key');

    expect(result).toBe('plain-text');
  });

  it('should set value without ttl', async () => {
    const kv: any = {
      get: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    };

    const cache = new KVCache(kv);
    await cache.set('key', { foo: 'bar' });

    expect(kv.put).toHaveBeenCalledWith('key', JSON.stringify({ foo: 'bar' }));
  });

  it('should set value with ttl', async () => {
    const kv: any = {
      get: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    };

    const cache = new KVCache(kv);
    await cache.set('key', 'value', 60);

    expect(kv.put).toHaveBeenCalledWith('key', 'value', { expirationTtl: 60 });
  });

  it('should delete value', async () => {
    const kv: any = {
      get: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    };

    const cache = new KVCache(kv);
    await cache.delete('key');

    expect(kv.delete).toHaveBeenCalledWith('key');
  });
});
