import type { SidebarsConfig } from '@docusaurus/plugin-content-docs';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

/**
 * 侧边栏配置 - 按用户角色和使用场景组织文档
 */
const sidebars: SidebarsConfig = {
  // 主文档侧边栏
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: '🚀 快速开始',
      items: [
        'getting-started/overview',
        'getting-started/prerequisites',
        'getting-started/installation',
        'getting-started/faq',
      ],
    },
    {
      type: 'category',
      label: '🔧 API 文档',
      items: [
        'api/README',
        'api/request-spec',
        'api/error-codes',
        'api/openapi',
      ],
    },
    {
      type: 'category',
      label: '💻 前端集成',
      items: [
        'frontend/README',
        'frontend/quick-start',
        'frontend/client-generation',
        'frontend/authentication',
        'frontend/i18n-integration',
        'frontend/error-handling',
        'frontend/business-flows',
        'frontend/common-examples',
      ],
    },
    {
      type: 'category',
      label: '🏗️ 架构设计',
      items: [
        'architecture/system-design',
        'architecture/backend',
        'architecture/frontend',
        'architecture/ddd-practice',
        'architecture/ci-cd',
      ],
    },
    {
      type: 'category',
      label: '📦 功能模块',
      items: [
        'modules/README',
        'modules/users',
        'modules/circles',
        'modules/events',
        'modules/artists',
        'modules/appearances',
        'modules/bookmarks',
        'modules/search',
        'modules/feed',
      ],
    },
    {
      type: 'category',
      label: '🛠️ 开发指南',
      items: [
        'development/contribution',
        'development/code-style',
        'development/git-commit',
        'development/schema-guidelines',
        'development/testing',
        'development/performance',
        'development/i18n',
        'development/frontend-guide',
      ],
    },
    {
      type: 'category',
      label: '📋 运维部署',
      items: ['operations/deployment', 'operations/db-design'],
    },
    {
      type: 'category',
      label: '📚 参考资料',
      items: [
        'reference/roadmap',
        {
          type: 'category',
          label: '架构决策记录',
          items: [
            'reference/adr/init',
            'reference/adr/cookie-based-auth',
            'reference/adr/i18n-events',
            'reference/adr/multilingual-caching',
          ],
        },
        {
          type: 'category',
          label: '变更日志',
          items: ['reference/changelog/CHANGELOG'],
        },
        {
          type: 'category',
          label: '许可证',
          items: ['reference/licenses/third-party'],
        },
      ],
    },
    {
      type: 'category',
      label: '📝 项目管理',
      items: [
        'project/todo-backend',
        'project/todo-frontend',
        {
          type: 'category',
          label: '产品需求',
          items: ['project/prd/admin-api', 'project/prd/role-access'],
        },
      ],
    },
  ],
};

export default sidebars;
