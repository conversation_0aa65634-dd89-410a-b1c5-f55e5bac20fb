﻿# 前端架构（Frontend Architecture）

> 目前前端尚在规划阶段，本节定义技术栈与目录约定，供未来实现参考。

## 技术栈

| 分类 | 选型                  | 备注                          |
| ---- | --------------------- | ----------------------------- |
| 框架 | React 19 (hooks)      | 函数组件                      |
| 路由 | React Router 7        | 嵌套路由                      |
| 状态 | Zustand + React Query | Client cache                  |
| UI   | Ant Design 5          | TailwindCSS on top for layout |
| 构建 | Vite 5                | ESBuild + SWC                 |
| 类型 | TypeScript            | 严格模式                      |

## 目录结构（草案）

```text
web/
├── src/
│   ├── app/           # 根布局与 Providers
│   ├── pages/         # 页面组件（路由 1:1）
│   ├── components/    # 通用 UI 组件
│   ├── features/      # 按领域分隔
│   ├── hooks/         # 复用逻辑
│   ├── libs/          # 第三方包装
│   └── styles/        # Tailwind + SCSS
└── vite.config.ts
```

## API 对接

- 使用 `openapi-typescript-fetch` 自动生成 fetch 客户端，源码位于 `web/src/libs/api.ts`。
- 在 CI 中复用后端生成的 `docs/openapi.json`，保证契约一致。
