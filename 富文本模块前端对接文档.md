# 富文本模块前端对接文档

> 📝 **版本**: v1.0.0  
> 🕒 **更新时间**: 2025-01-02  
> 👥 **目标读者**: 前端开发者

## 📋 快速导航

- [🚀 快速开始](#-快速开始)
- [🔌 API 接口](#-api-接口)
- [💻 前端集成](#-前端集成)
- [🎨 UI 组件示例](#-ui-组件示例)
- [🛡️ 安全注意事项](#️-安全注意事项)
- [❓ 常见问题](#-常见问题)

## 🚀 快速开始

### 基础信息

**模块功能**: 为事件、场馆、社团提供富文本内容管理  
**基础路径**: `/rich-text`  
**支持实体**: `event` | `venue` | `circle`  
**内容类型**: `introduction` | `highlights` | `guide` | `notices`

### 5分钟上手

```typescript
// 1. 获取事件的所有富文本内容
const { data: content } = useEntityContent('event', 'reitaisai-22');

// 2. 更新单个内容
const updateMutation = useUpdateContent();
updateMutation.mutate({
  entity_type: 'event',
  entity_id: 'reitaisai-22',
  content_type: 'introduction',
  content: '<p>新的介绍内容</p>',
});

// 3. 批量更新所有内容
const batchMutation = useBatchUpdateContent();
batchMutation.mutate({
  entityType: 'event',
  entityId: 'reitaisai-22',
  data: {
    introduction: '<p>介绍</p>',
    highlights: '<p>亮点</p>',
  },
});
```

## 🔌 API 接口

### 接口概览

| 方法     | 路径                                             | 功能              | 权限   |
| -------- | ------------------------------------------------ | ----------------- | ------ |
| `GET`    | `/{entityType}/{entityId}/content`               | 获取所有内容      | 公开   |
| `GET`    | `/{entityType}/{entityId}/content/{contentType}` | 获取特定内容      | 公开   |
| `POST`   | `/{entityType}/{entityId}/content`               | 创建/更新单个内容 | 管理员 |
| `PUT`    | `/{entityType}/{entityId}/content`               | 批量更新内容      | 管理员 |
| `DELETE` | `/{entityType}/{entityId}/content`               | 删除所有内容      | 管理员 |
| `POST`   | `/upload/images`                                 | 上传图片          | 管理员 |

### 数据类型定义

```typescript
// 实体类型
type EntityType = 'event' | 'venue' | 'circle';

// 内容类型
type ContentType = 'introduction' | 'highlights' | 'guide' | 'notices';

// 富文本内容
interface RichTextContent {
  id: string;
  entity_type: EntityType;
  entity_id: string;
  content_type: ContentType;
  content: string;
  created_at?: string;
  updated_at?: string;
}

// 内容响应
interface ContentResponse {
  introduction?: string;
  highlights?: string;
  guide?: string;
  notices?: string;
}

// 创建内容请求
interface CreateContentRequest {
  entity_type: EntityType;
  entity_id: string;
  content_type: ContentType;
  content: string; // 最大100000字符
}

// 批量更新请求
interface BatchContentRequest {
  introduction?: string;
  highlights?: string;
  guide?: string;
  notices?: string;
}
```

### 详细接口说明

#### 1. 获取实体所有内容

```typescript
GET /rich-text/{entityType}/{entityId}/content

// 示例
GET /rich-text/event/reitaisai-22/content

// 响应
{
  "code": 0,
  "message": "获取内容成功",
  "data": {
    "introduction": "<p>介绍内容</p>",
    "highlights": "<p>亮点内容</p>",
    "guide": "<p>指南内容</p>",
    "notices": "<p>公告内容</p>"
  }
}
```

#### 2. 获取特定类型内容

```typescript
GET /rich-text/{entityType}/{entityId}/content/{contentType}

// 示例
GET /rich-text/event/reitaisai-22/content/introduction

// 响应
{
  "content": "<p>介绍内容</p>"
}
```

#### 3. 创建/更新单个内容

```typescript
POST /rich-text/{entityType}/{entityId}/content

// 请求体
{
  "entity_type": "event",
  "entity_id": "reitaisai-22",
  "content_type": "introduction",
  "content": "<p>新的介绍内容</p>"
}

// 响应
{
  "code": 0,
  "message": "内容保存成功",
  "data": {
    "id": "uuid-123",
    "entity_type": "event",
    "entity_id": "reitaisai-22",
    "content_type": "introduction",
    "content": "<p>新的介绍内容</p>",
    "created_at": "2025-01-02T10:00:00Z",
    "updated_at": "2025-01-02T10:00:00Z"
  }
}
```

#### 4. 批量更新内容

```typescript
PUT /rich-text/{entityType}/{entityId}/content

// 请求体（所有字段可选）
{
  "introduction": "<p>介绍内容</p>",
  "highlights": "<p>亮点内容</p>",
  "guide": "<p>指南内容</p>",
  "notices": "<p>公告内容</p>"
}

// 响应
{
  "code": 0,
  "message": "批量更新成功",
  "data": {
    "introduction": "<p>介绍内容</p>",
    "highlights": "<p>亮点内容</p>",
    "guide": "<p>指南内容</p>",
    "notices": "<p>公告内容</p>"
  }
}
```

#### 5. 图片上传

```typescript
POST /rich-text/upload/images
Content-Type: multipart/form-data

// FormData
const formData = new FormData();
formData.append('image', file);

// 响应
{
  "code": 0,
  "message": "图片上传成功",
  "data": {
    "url": "/images/content/1640995200000_uuid-123.jpg"
  }
}
```

**图片限制**:

- 支持格式: JPEG, PNG, WebP, GIF
- 最大大小: 10MB
- 最大尺寸: 4096x4096px

## 💻 前端集成

### React Query Hooks

```typescript
// hooks/useRichText.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { request } from '@/lib/http';

// 获取实体内容
export function useEntityContent(entityType: EntityType, entityId: string) {
  return useQuery({
    queryKey: ['rich-text', entityType, entityId],
    queryFn: () =>
      request<ContentResponse>(`/rich-text/${entityType}/${entityId}/content`),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    enabled: !!(entityType && entityId), // 确保参数存在
  });
}

// 获取特定类型内容
export function useEntityContentByType(
  entityType: EntityType,
  entityId: string,
  contentType: ContentType
) {
  return useQuery({
    queryKey: ['rich-text', entityType, entityId, contentType],
    queryFn: async () => {
      const response = await request<{ content: string }>(
        `/rich-text/${entityType}/${entityId}/content/${contentType}`
      );
      return response.content;
    },
    staleTime: 5 * 60 * 1000,
    enabled: !!(entityType && entityId && contentType),
  });
}

// 更新单个内容
export function useUpdateContent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateContentRequest) => {
      return request<RichTextContent>(
        `/rich-text/${data.entity_type}/${data.entity_id}/content`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        }
      );
    },
    onSuccess: (_, variables) => {
      // 更新相关缓存
      queryClient.invalidateQueries({
        queryKey: ['rich-text', variables.entity_type, variables.entity_id],
      });
    },
  });
}

// 批量更新内容
export function useBatchUpdateContent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      entityType,
      entityId,
      data,
    }: {
      entityType: EntityType;
      entityId: string;
      data: BatchContentRequest;
    }) => {
      return request<ContentResponse>(
        `/rich-text/${entityType}/${entityId}/content`,
        {
          method: 'PUT',
          body: JSON.stringify(data),
        }
      );
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['rich-text', variables.entityType, variables.entityId],
      });
    },
  });
}

// 删除实体内容
export function useDeleteEntityContent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      entityType,
      entityId,
    }: {
      entityType: EntityType;
      entityId: string;
    }) => {
      return request(`/rich-text/${entityType}/${entityId}/content`, {
        method: 'DELETE',
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['rich-text', variables.entityType, variables.entityId],
      });
    },
  });
}

// 图片上传
export function useUploadImage() {
  return useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('image', file);

      return request<{ url: string }>('/rich-text/upload/images', {
        method: 'POST',
        body: formData,
        // 注意：不要设置 Content-Type，让浏览器自动设置
      });
    },
  });
}
```

### 错误处理

```typescript
// utils/richTextErrorHandler.ts
export function handleRichTextError(error: any) {
  if (error?.code === 10001) {
    return '请求参数错误，请检查输入内容';
  }
  if (error?.code === 60002) {
    return '内容不存在';
  }
  if (error?.code === 10003) {
    return '服务器错误，请稍后重试';
  }
  return '操作失败，请重试';
}

// 在组件中使用
const updateMutation = useUpdateContent();

const handleSave = async (content: string) => {
  try {
    await updateMutation.mutateAsync({
      entity_type: 'event',
      entity_id: 'reitaisai-22',
      content_type: 'introduction',
      content,
    });
    toast.success('保存成功');
  } catch (error) {
    const message = handleRichTextError(error);
    toast.error(message);
  }
};
```

## 🎨 UI 组件示例

### 富文本编辑器组件

```tsx
// components/RichTextEditor.tsx
import { useState } from 'react';
import { useUpdateContent, useUploadImage } from '@/hooks/useRichText';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface RichTextEditorProps {
  entityType: EntityType;
  entityId: string;
  contentType: ContentType;
  initialContent?: string;
  onSave?: (content: string) => void;
}

export function RichTextEditor({
  entityType,
  entityId,
  contentType,
  initialContent = '',
  onSave,
}: RichTextEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [isEditing, setIsEditing] = useState(false);

  const updateMutation = useUpdateContent();
  const uploadMutation = useUploadImage();

  const handleSave = async () => {
    try {
      await updateMutation.mutateAsync({
        entity_type: entityType,
        entity_id: entityId,
        content_type: contentType,
        content,
      });

      setIsEditing(false);
      onSave?.(content);
      toast.success('保存成功');
    } catch (error) {
      toast.error('保存失败，请重试');
    }
  };

  const handleImageUpload = async (file: File) => {
    try {
      const result = await uploadMutation.mutateAsync(file);
      return result.url;
    } catch (error) {
      toast.error('图片上传失败');
      throw error;
    }
  };

  if (!isEditing) {
    return (
      <div className="space-y-4">
        <div
          className="prose max-w-none"
          dangerouslySetInnerHTML={{ __html: content || '<p>暂无内容</p>' }}
        />
        <Button onClick={() => setIsEditing(true)}>编辑内容</Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 这里集成你选择的富文本编辑器，如 TipTap, Quill 等 */}
      <div className="border rounded-lg p-4 min-h-[200px]">
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          className="w-full h-full resize-none border-none outline-none"
          placeholder="请输入内容..."
        />
      </div>

      <div className="flex gap-2">
        <Button onClick={handleSave} disabled={updateMutation.isPending}>
          {updateMutation.isPending ? '保存中...' : '保存'}
        </Button>
        <Button
          variant="outline"
          onClick={() => {
            setContent(initialContent);
            setIsEditing(false);
          }}
        >
          取消
        </Button>
      </div>
    </div>
  );
}
```

### 内容展示组件

```tsx
// components/RichTextDisplay.tsx
import { useEntityContent } from '@/hooks/useRichText';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface RichTextDisplayProps {
  entityType: EntityType;
  entityId: string;
  contentType?: ContentType;
  className?: string;
}

export function RichTextDisplay({
  entityType,
  entityId,
  contentType,
  className = '',
}: RichTextDisplayProps) {
  const {
    data: content,
    isLoading,
    error,
  } = useEntityContent(entityType, entityId);

  if (isLoading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertDescription>加载内容失败，请刷新页面重试</AlertDescription>
      </Alert>
    );
  }

  // 如果指定了内容类型，只显示该类型的内容
  if (contentType) {
    const specificContent = content?.[contentType];
    if (!specificContent) {
      return <div className={className}>暂无内容</div>;
    }

    return (
      <div
        className={`prose max-w-none ${className}`}
        dangerouslySetInnerHTML={{ __html: specificContent }}
      />
    );
  }

  // 显示所有内容
  const contentTypes: { key: ContentType; label: string }[] = [
    { key: 'introduction', label: '介绍' },
    { key: 'highlights', label: '亮点' },
    { key: 'guide', label: '指南' },
    { key: 'notices', label: '公告' },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {contentTypes.map(({ key, label }) => {
        const sectionContent = content?.[key];
        if (!sectionContent) return null;

        return (
          <section key={key}>
            <h3 className="text-lg font-semibold mb-3">{label}</h3>
            <div
              className="prose max-w-none"
              dangerouslySetInnerHTML={{ __html: sectionContent }}
            />
          </section>
        );
      })}

      {!content ||
        (Object.values(content).every((c) => !c) && (
          <div className="text-center text-gray-500 py-8">暂无内容</div>
        ))}
    </div>
  );
}
```

### 批量编辑组件

```tsx
// components/BatchRichTextEditor.tsx
import { useState, useEffect } from 'react';
import { useBatchUpdateContent, useEntityContent } from '@/hooks/useRichText';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';

interface BatchRichTextEditorProps {
  entityType: EntityType;
  entityId: string;
  onSave?: (content: ContentResponse) => void;
}

export function BatchRichTextEditor({
  entityType,
  entityId,
  onSave,
}: BatchRichTextEditorProps) {
  const { data: initialContent } = useEntityContent(entityType, entityId);
  const batchUpdateMutation = useBatchUpdateContent();

  const [content, setContent] = useState<BatchContentRequest>({
    introduction: '',
    highlights: '',
    guide: '',
    notices: '',
  });

  useEffect(() => {
    if (initialContent) {
      setContent({
        introduction: initialContent.introduction || '',
        highlights: initialContent.highlights || '',
        guide: initialContent.guide || '',
        notices: initialContent.notices || '',
      });
    }
  }, [initialContent]);

  const handleSave = async () => {
    try {
      const result = await batchUpdateMutation.mutateAsync({
        entityType,
        entityId,
        data: content,
      });

      onSave?.(result);
      toast.success('批量保存成功');
    } catch (error) {
      toast.error('保存失败，请重试');
    }
  };

  const contentTypes = [
    {
      key: 'introduction' as const,
      label: '介绍',
      placeholder: '请输入介绍内容...',
    },
    {
      key: 'highlights' as const,
      label: '亮点',
      placeholder: '请输入亮点内容...',
    },
    { key: 'guide' as const, label: '指南', placeholder: '请输入指南内容...' },
    {
      key: 'notices' as const,
      label: '公告',
      placeholder: '请输入公告内容...',
    },
  ];

  return (
    <div className="space-y-6">
      <Tabs defaultValue="introduction" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          {contentTypes.map(({ key, label }) => (
            <TabsTrigger key={key} value={key}>
              {label}
            </TabsTrigger>
          ))}
        </TabsList>

        {contentTypes.map(({ key, label, placeholder }) => (
          <TabsContent key={key} value={key} className="space-y-4">
            <div>
              <label className="text-sm font-medium">{label}</label>
              <textarea
                value={content[key] || ''}
                onChange={(e) =>
                  setContent((prev) => ({ ...prev, [key]: e.target.value }))
                }
                placeholder={placeholder}
                className="w-full mt-2 p-3 border rounded-lg min-h-[300px] resize-y"
              />
              <div className="text-xs text-gray-500 mt-1">
                {(content[key] || '').length} / 100000 字符
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>

      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={batchUpdateMutation.isPending}
          size="lg"
        >
          {batchUpdateMutation.isPending ? '保存中...' : '批量保存'}
        </Button>
      </div>
    </div>
  );
}
```

## 🛡️ 安全注意事项

### HTML 内容安全

```typescript
// utils/htmlSanitizer.ts
import DOMPurify from 'isomorphic-dompurify';

// 客户端HTML清理（双重保护）
export function sanitizeHTML(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      'p',
      'br',
      'strong',
      'em',
      'u',
      's',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'ul',
      'ol',
      'li',
      'blockquote',
      'a',
      'img',
      'table',
      'thead',
      'tbody',
      'tr',
      'th',
      'td',
      'div',
      'span',
    ],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class', 'style', 'target'],
    ALLOW_DATA_ATTR: false,
  });
}

// 在保存前清理内容
const handleSave = (content: string) => {
  const cleanContent = sanitizeHTML(content);
  // 发送到服务器...
};
```

### 图片上传安全

```typescript
// utils/imageValidator.ts
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MAX_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_DIMENSION = 4096;

export function validateImageFile(file: File): {
  valid: boolean;
  error?: string;
} {
  if (!ALLOWED_TYPES.includes(file.type)) {
    return { valid: false, error: '不支持的文件格式' };
  }

  if (file.size > MAX_SIZE) {
    return { valid: false, error: '文件大小超过10MB限制' };
  }

  return { valid: true };
}

export async function validateImageDimensions(
  file: File
): Promise<{ valid: boolean; error?: string }> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      if (img.width > MAX_DIMENSION || img.height > MAX_DIMENSION) {
        resolve({
          valid: false,
          error: `图片尺寸不能超过${MAX_DIMENSION}x${MAX_DIMENSION}`,
        });
      } else {
        resolve({ valid: true });
      }
    };
    img.onerror = () => {
      resolve({ valid: false, error: '无效的图片文件' });
    };
    img.src = URL.createObjectURL(file);
  });
}
```

## ❓ 常见问题

### Q: 如何集成富文本编辑器？

**A**: 推荐使用 TipTap 或 Quill.js：

```typescript
// 使用 TipTap 的示例
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';

const editor = useEditor({
  extensions: [
    StarterKit,
    Image.configure({
      HTMLAttributes: {
        class: 'max-w-full h-auto',
      },
    }),
  ],
  content: initialContent,
  onUpdate: ({ editor }) => {
    setContent(editor.getHTML());
  },
});
```

### Q: 如何处理图片上传？

**A**: 使用提供的上传接口：

```typescript
const handleImageUpload = async (file: File) => {
  // 1. 验证文件
  const validation = validateImageFile(file);
  if (!validation.valid) {
    throw new Error(validation.error);
  }

  // 2. 上传文件
  const result = await uploadMutation.mutateAsync(file);

  // 3. 插入到编辑器
  editor?.chain().focus().setImage({ src: result.url }).run();
};
```

### Q: 如何实现自动保存？

**A**: 使用防抖和本地存储：

```typescript
import { useDebouncedCallback } from 'use-debounce';
import { useLocalStorage } from '@/hooks/useLocalStorage';

const [localContent, setLocalContent] = useLocalStorage(
  `rich-text-${entityType}-${entityId}-${contentType}`,
  ''
);

const debouncedSave = useDebouncedCallback(
  async (content: string) => {
    try {
      await updateMutation.mutateAsync({
        entity_type: entityType,
        entity_id: entityId,
        content_type: contentType,
        content,
      });
      // 清除本地存储
      setLocalContent('');
    } catch (error) {
      // 保存到本地存储
      setLocalContent(content);
    }
  },
  2000 // 2秒防抖
);
```

### Q: 如何处理大量内容的性能问题？

**A**: 使用虚拟化和懒加载：

```typescript
// 1. 内容预览模式
const [isPreview, setIsPreview] = useState(true);
const [showFullContent, setShowFullContent] = useState(false);

// 2. 截断长内容
const truncateContent = (html: string, maxLength: number = 200) => {
  const text = html.replace(/<[^>]*>/g, '');
  if (text.length <= maxLength) return html;

  return text.substring(0, maxLength) + '...';
};

// 3. 懒加载组件
const LazyRichTextEditor = lazy(() => import('./RichTextEditor'));
```

---

## 📚 相关资源

- **API 完整文档**: [OpenAPI 规范](./openapi.json)
- **组件库文档**: [shadcn/ui](https://ui.shadcn.com/)
- **富文本编辑器**: [TipTap](https://tiptap.dev/) | [Quill.js](https://quilljs.com/)
- **图标库**: [Lucide React](https://lucide.dev/)

## 🔄 更新日志

- **v1.0.0** (2025-01-02): 初始版本发布
  - 完整的 CRUD 接口
  - 图片上传功能
  - 批量操作支持
  - 安全内容处理

---

**💡 提示**: 如有问题或建议，请联系后端团队或提交 Issue。
