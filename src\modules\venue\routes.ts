import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import { localizedVenueSchema, venueSearchParams } from './schema';
import * as venueService from './service';
import { getDB } from '@/infrastructure';
import type { Logger } from '@/infrastructure';
import { paginatedResult } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { jsonWithFields } from '@/utils/fieldFilter';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const pubVenues = new OpenAPIHono<HonoApp>();

// OpenAPI route definitions
const listVenuesRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '场馆列表（公开）',
  tags: ['Venues'],
  request: {
    query: venueSearchParams,
  },
  responses: {
    200: {
      description: '场馆列表',
      content: {
        'application/json': { schema: paginatedResult(localizedVenueSchema) },
      },
    },
  },
});

const getVenueRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '场馆详情',
  tags: ['Venues'],
  request: {
    params: z.object({
      id: z.string().openapi({ example: 'tokyo-big-sight' }),
    }),
  },
  responses: {
    200: {
      description: '场馆详情',
      content: { 'application/json': { schema: localizedVenueSchema } },
    },
    404: { description: 'Not Found' },
  },
});

// Route handlers
registerOpenApiRoute(pubVenues, listVenuesRoute, async (c: Context) => {
  const db = getDB(c);
  const logger = c.get('logger') as Logger;

  const query = c.req.valid('query');
  const locale =
    c.req.header('Accept-Language')?.split(',')[0]?.split('-')[0] || 'en';

  try {
    const result = await venueService.listVenues(
      db,
      query,
      locale as 'en' | 'ja' | 'zh'
    );
    return jsonWithFields(c, result);
  } catch (error) {
    logger.error('Failed to list venues', { error, query });
    return jsonError(c, 'Failed to fetch venues', 500);
  }
});

registerOpenApiRoute(pubVenues, getVenueRoute, async (c: Context) => {
  const db = getDB(c);
  const logger = c.get('logger') as Logger;

  const { id } = c.req.valid('param');
  const locale =
    c.req.header('Accept-Language')?.split(',')[0]?.split('-')[0] || 'en';

  try {
    const venue = await venueService.getVenueById(
      db,
      id,
      locale as 'en' | 'ja' | 'zh'
    );
    if (!venue) {
      return jsonError(c, 'Venue not found', 404);
    }
    return jsonWithFields(c, venue);
  } catch (error) {
    logger.error('Failed to get venue', { error, id });
    return jsonError(c, 'Failed to fetch venue', 500);
  }
});

export { pubVenues };
