#!/usr/bin/env tsx

/**
 * 检查预设配置完整性脚本
 * 用于诊断 rich-text-tabs 预设配置是否正确创建
 */

import { getDB } from '@/infrastructure';

interface PresetConfig {
  id: string;
  entity_type: string;
  language_code: string;
  key: string;
  label: string;
  is_preset: boolean;
  is_active: boolean;
}

const EXPECTED_PRESETS = [
  // Event 预设
  { entity_type: 'event', language_code: 'en', key: 'introduction', label: 'Introduction' },
  { entity_type: 'event', language_code: 'zh', key: 'introduction', label: '介绍' },
  { entity_type: 'event', language_code: 'ja', key: 'introduction', label: '紹介' },
  
  // Venue 预设
  { entity_type: 'venue', language_code: 'en', key: 'overview', label: 'Overview' },
  { entity_type: 'venue', language_code: 'zh', key: 'overview', label: '概览' },
  { entity_type: 'venue', language_code: 'ja', key: 'overview', label: '概要' },
];

async function checkPresetConfigs() {
  console.log('🔍 检查预设配置完整性...\n');

  try {
    // 注意：这里需要根据您的环境配置来获取数据库连接
    // 可能需要模拟 Hono Context 或直接使用数据库连接
    const db = getDB(null as any); // 这里需要根据实际情况调整
    
    // 查询所有预设配置
    const stmt = db.prepare(`
      SELECT id, entity_type, language_code, key, label, is_preset, is_active, deleted_at
      FROM content_type_configs 
      WHERE is_preset = 1 AND deleted_at IS NULL
      ORDER BY entity_type, language_code, key
    `);
    
    const result = await stmt.all();
    const existingPresets = result.results as PresetConfig[];
    
    console.log(`📊 数据库中现有预设配置: ${existingPresets.length} 条`);
    console.log(`📋 期望的预设配置: ${EXPECTED_PRESETS.length} 条\n`);
    
    // 检查每个期望的预设配置
    const missingPresets: typeof EXPECTED_PRESETS = [];
    const foundPresets: PresetConfig[] = [];
    
    for (const expected of EXPECTED_PRESETS) {
      const found = existingPresets.find(
        p => p.entity_type === expected.entity_type && 
             p.language_code === expected.language_code && 
             p.key === expected.key
      );
      
      if (found) {
        foundPresets.push(found);
        console.log(`✅ ${expected.entity_type}/${expected.language_code}/${expected.key} - ${found.label}`);
      } else {
        missingPresets.push(expected);
        console.log(`❌ ${expected.entity_type}/${expected.language_code}/${expected.key} - 缺失`);
      }
    }
    
    console.log(`\n📈 统计结果:`);
    console.log(`  ✅ 已存在: ${foundPresets.length} 条`);
    console.log(`  ❌ 缺失: ${missingPresets.length} 条`);
    
    if (missingPresets.length > 0) {
      console.log(`\n🔧 缺失的预设配置:`);
      missingPresets.forEach(preset => {
        console.log(`  - ${preset.entity_type}/${preset.language_code}/${preset.key}: "${preset.label}"`);
      });
      
      console.log(`\n💡 建议解决方案:`);
      console.log(`  1. 运行数据库迁移: pnpm run migrate`);
      console.log(`  2. 或手动执行: tsx scripts/init-preset-configs.ts`);
      console.log(`  3. 或检查迁移文件是否正确执行`);
    } else {
      console.log(`\n🎉 所有预设配置都已正确创建！`);
    }
    
    // 检查是否有额外的预设配置
    const extraPresets = existingPresets.filter(existing => 
      !EXPECTED_PRESETS.some(expected => 
        expected.entity_type === existing.entity_type &&
        expected.language_code === existing.language_code &&
        expected.key === existing.key
      )
    );
    
    if (extraPresets.length > 0) {
      console.log(`\n⚠️  发现额外的预设配置:`);
      extraPresets.forEach(preset => {
        console.log(`  + ${preset.entity_type}/${preset.language_code}/${preset.key}: "${preset.label}"`);
      });
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
    console.log('\n💡 可能的原因:');
    console.log('  1. 数据库连接失败');
    console.log('  2. content_type_configs 表不存在');
    console.log('  3. 迁移脚本未执行');
    process.exit(1);
  }
}

// 执行检查
checkPresetConfigs().catch(console.error);
