# 收藏模块前端对接文档

> 📝 **版本**: v2.0.0  
> 🕒 **更新时间**: 2025-08-02  
> 👥 **目标读者**: 前端开发者

## 📋 快速导航

- [🚀 快速开始](#-快速开始)
- [🔌 API 接口](#-api-接口)
- [🎯 个性化功能](#-个性化功能)
- [💻 前端集成](#-前端集成)
- [🎨 UI 组件示例](#-ui-组件示例)
- [🛡️ 安全注意事项](#️-安全注意事项)
- [❓ 常见问题](#-常见问题)

## 🚀 快速开始

### 基础信息

**模块功能**: 为用户提供完整的社团收藏功能
**支持操作**: 收藏切换、列表查询、状态检查、统计信息、批量操作
**个性化功能**: 社团列表自动优先显示已收藏社团
**认证要求**: 收藏操作需要用户登录，个性化排序支持可选认证
**分页支持**: 传统分页和游标分页双重支持

### 5分钟上手

```typescript
// 1. 切换收藏状态
const toggleMutation = useToggleBookmark();
toggleMutation.mutate('circle-123');

// 2. 获取收藏列表
const { data: bookmarks } = useBookmarks({ page: 1, pageSize: 20 });

// 3. 检查收藏状态
const { data: status } = useBookmarkStatus('circle-123');

// 4. 在组件中使用
function BookmarkButton({ circleId }: { circleId: string }) {
  const { data: status, isLoading } = useBookmarkStatus(circleId);
  const toggleMutation = useToggleBookmark();

  const handleToggle = () => {
    toggleMutation.mutate(circleId);
  };

  return (
    <button
      onClick={handleToggle}
      disabled={isLoading || toggleMutation.isPending}
    >
      {status?.isBookmarked ? '已收藏' : '收藏'}
    </button>
  );
}
```

## 🔌 API 接口

### 接口概览

| 方法   | 路径                                  | 功能         | 权限     |
| ------ | ------------------------------------- | ------------ | -------- |
| `POST` | `/circles/{circleId}/bookmark`        | 切换收藏状态 | 登录用户 |
| `GET`  | `/circles/{circleId}/bookmark/status` | 检查收藏状态 | 登录用户 |
| `GET`  | `/user/bookmarks`                     | 获取收藏列表 | 登录用户 |
| `GET`  | `/user/bookmarks/stats`               | 获取收藏统计 | 登录用户 |
| `POST` | `/user/bookmarks/batch`               | 批量收藏操作 | 登录用户 |

### 数据类型定义

```typescript
// 基础收藏对象
interface Bookmark {
  id: string;
  user_id: string;
  circle_id: string;
  created_at: string;
  updated_at: string;
}

// API 响应格式（项目标准）
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 切换收藏响应
interface ToggleBookmarkResponse {
  isBookmarked: boolean;
}

// 收藏状态响应
interface BookmarkStatusResponse {
  isBookmarked: boolean;
  bookmarkId: string | null;
  createdAt: string | null;
}

// 收藏列表查询参数
interface BookmarkListQuery {
  // 传统分页参数
  page?: number;
  pageSize?: number;

  // 游标分页参数（性能优化）
  cursor?: string;

  // 查询参数
  search?: string;
  sortBy?: 'created_at' | 'circle_name';
  sortOrder?: 'asc' | 'desc';
}

// 收藏列表项
interface BookmarkListItem {
  id: string;
  created_at: string;
  circle: {
    id: string;
    name: string;
    category: string;
    urls: string | null;
    created_at: string;
    updated_at: string;
  };
}

// 收藏列表响应
interface BookmarkListResponse {
  items: BookmarkListItem[];
  // 传统分页信息
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  // 游标分页信息
  nextCursor: string | null;
  hasMore: boolean;
}

// 收藏统计响应
interface BookmarkStatsResponse {
  totalBookmarks: number;
  recentBookmarks: number; // 最近7天
  categoryCounts: Record<string, number>;
}

// 批量操作请求
interface BookmarkBatchRequest {
  action: 'add' | 'remove';
  circleIds: string[]; // 最多50个
}

// 批量操作响应
interface BookmarkBatchResponse {
  success: string[];
  failed: Array<{
    circleId: string;
    reason: string;
  }>;
  total: number;
  successCount: number;
  failedCount: number;
}
```

### 详细接口说明

#### 1. 切换收藏状态

```typescript
POST /circles/{circleId}/bookmark

// 路径参数
{
  circleId: string; // 社团ID，如 'circle-123'
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "已加入收藏", // 或 "已取消收藏"
  "data": {
    "isBookmarked": true // 当前收藏状态
  }
}
```

**功能说明**:

- 如果用户未收藏该社团，则添加收藏，返回 `isBookmarked: true`
- 如果用户已收藏该社团，则取消收藏，返回 `isBookmarked: false`
- 操作是原子性的，不会出现重复收藏的情况

#### 2. 检查收藏状态

```typescript
GET /circles/{circleId}/bookmark/status

// 路径参数
{
  circleId: string; // 社团ID
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "获取收藏状态成功",
  "data": {
    "isBookmarked": true,
    "bookmarkId": "bookmark-uuid",
    "createdAt": "2025-01-01T00:00:00Z"
  }
}
```

#### 3. 获取收藏列表

```typescript
GET /user/bookmarks

// 查询参数
{
  page?: number;        // 页码，默认 1
  pageSize?: number;    // 每页数量，默认 20，最大 100
  cursor?: string;      // 游标分页（推荐）
  search?: string;      // 搜索关键词
  sortBy?: 'created_at' | 'circle_name';  // 排序字段
  sortOrder?: 'asc' | 'desc';             // 排序方向
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "获取收藏列表成功",
  "data": {
    "items": [
      {
        "id": "bookmark-uuid",
        "created_at": "2025-01-01T00:00:00Z",
        "circle": {
          "id": "circle-uuid",
          "name": "某某工作室",
          "category": "original",
          "urls": "{\"twitter\":\"@example\"}",
          "created_at": "2025-01-01T00:00:00Z",
          "updated_at": "2025-01-01T00:00:00Z"
        }
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1,
    "nextCursor": null,
    "hasMore": false
  }
}
```

#### 4. 获取收藏统计

```typescript
GET /user/bookmarks/stats?includeIds=true

// 查询参数
{
  includeIds?: boolean; // 是否包含收藏的社团ID列表，用于前端批量状态检查优化
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 基础响应示例（includeIds=false 或未提供）
{
  "code": 0,
  "message": "获取收藏统计成功",
  "data": {
    "totalBookmarks": 25,
    "recentBookmarks": 3,
    "categoryCounts": {
      "original": 15,
      "derivative": 10
    }
  }
}

// 优化响应示例（includeIds=true）
{
  "code": 0,
  "message": "获取收藏统计成功",
  "data": {
    "totalBookmarks": 25,
    "recentBookmarks": 3,
    "categoryCounts": {
      "original": 15,
      "derivative": 10
    },
    "bookmarkedCircleIds": [
      "circle-1", "circle-2", "circle-3", "circle-7", "circle-12"
      // ... 用户收藏的所有社团ID列表
    ]
  }
}
```

**性能优化说明**：

- `includeIds=true` 时返回收藏的社团ID列表，用于前端批量状态检查
- 可将原本需要50个单独请求的场景优化为1个请求
- 数据量相比完整收藏列表大幅减少（仅ID vs 完整社团信息）

## 🎯 个性化功能

### 社团列表个性化排序

**功能说明**：已登录用户在浏览社团列表时，已收藏的社团会自动排在前面，提升浏览体验。

#### API 端点

```typescript
GET / circles;
```

#### 鉴权方式

- **可选认证**：无需强制登录，支持渐进式增强
- **Session Cookie**：通过 `auth_session` cookie 自动识别用户身份
- **向后兼容**：未登录用户正常访问，已登录用户获得个性化体验

#### 排序规则

| 用户状态   | 排序方式            | 说明                                         |
| ---------- | ------------------- | -------------------------------------------- |
| **未登录** | 按社团名称排序      | 标准的字母顺序排列                           |
| **已登录** | 收藏优先 + 名称排序 | 已收藏社团排在前面，同等收藏状态下按名称排序 |

#### 使用示例

```typescript
// 1. 未登录用户 - 标准排序
const response = await fetch('/circles?page=1&pageSize=20');

// 2. 已登录用户 - 自动个性化排序
const response = await fetch('/circles?page=1&pageSize=20', {
  credentials: 'include', // 自动携带 session cookie
});

// 3. 使用 React Query
const { data: circles } = useQuery({
  queryKey: ['circles', page, pageSize, search],
  queryFn: () =>
    request<CircleListResponse>(
      `/circles?page=${page}&pageSize=${pageSize}&search=${search}`
    ),
  staleTime: 5 * 60 * 1000, // 5分钟缓存
});
```

#### 响应格式

```json
{
  "items": [
    // 已收藏的社团排在前面
    {
      "id": "circle-1",
      "name": "已收藏社团A",
      "urls": "{\"twitter\":\"@example\"}",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    },
    {
      "id": "circle-2",
      "name": "已收藏社团B",
      "urls": "{\"website\":\"https://example.com\"}",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    },
    // 然后是未收藏的社团
    {
      "id": "circle-3",
      "name": "未收藏社团C",
      "urls": null,
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 20
}
```

#### 性能特点

- **缓存策略**：未登录用户结果使用全局缓存，已登录用户实时查询
- **数据库优化**：单次SQL查询完成排序，利用现有索引
- **网络开销**：无额外请求，响应格式保持一致

#### 前端集成建议

```typescript
// 推荐：结合收藏状态管理的完整方案
function CircleListPage() {
  const [page, setPage] = useState(1);
  const { isBookmarked, toggleBookmark } = useBookmarkManager();

  // 获取社团列表（自动个性化排序）
  const { data: circles, isLoading } = useQuery({
    queryKey: ['circles', page],
    queryFn: () => request<CircleListResponse>(`/circles?page=${page}`),
  });

  return (
    <div className="circle-list">
      {circles?.items.map((circle) => (
        <CircleCard
          key={circle.id}
          circle={circle}
          isBookmarked={isBookmarked(circle.id)}
          onToggleBookmark={() => toggleBookmark(circle.id)}
        />
      ))}
    </div>
  );
}
```

## 💻 前端集成

### React Query Hooks

```typescript
// hooks/useBookmark.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { request } from '@/lib/http';

// 切换收藏状态
export function useToggleBookmark() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (circleId: string) => {
      return request<ToggleBookmarkResponse>(`/circles/${circleId}/bookmark`, {
        method: 'POST',
      });
    },
    onSuccess: (data, circleId) => {
      // 更新收藏状态缓存
      queryClient.setQueryData(['bookmark-status', circleId], {
        isBookmarked: data.isBookmarked,
        bookmarkId: data.isBookmarked ? 'temp-id' : null,
        createdAt: data.isBookmarked ? new Date().toISOString() : null,
      });

      // 刷新收藏列表和统计
      queryClient.invalidateQueries({ queryKey: ['bookmarks'] });
      queryClient.invalidateQueries({ queryKey: ['bookmark-stats'] });
    },
  });
}

// 检查收藏状态
export function useBookmarkStatus(circleId: string) {
  return useQuery({
    queryKey: ['bookmark-status', circleId],
    queryFn: () =>
      request<BookmarkStatusResponse>(`/circles/${circleId}/bookmark/status`),
    staleTime: 10 * 60 * 1000, // 10分钟缓存
    enabled: !!circleId,
  });
}

// 获取收藏列表
export function useBookmarks(query: BookmarkListQuery) {
  return useQuery({
    queryKey: ['bookmarks', query],
    queryFn: () => {
      const params = new URLSearchParams();
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
      return request<BookmarkListResponse>(`/user/bookmarks?${params}`);
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 获取收藏统计
export function useBookmarkStats() {
  return useQuery({
    queryKey: ['bookmark-stats'],
    queryFn: () => request<BookmarkStatsResponse>('/user/bookmarks/stats'),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 批量操作收藏
export function useBatchBookmarks() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: BookmarkBatchRequest) =>
      request<BookmarkBatchResponse>('/user/bookmarks/batch', {
        method: 'POST',
        body: JSON.stringify(request),
      }),
    onSuccess: () => {
      // 刷新所有相关缓存
      queryClient.invalidateQueries({ queryKey: ['bookmarks'] });
      queryClient.invalidateQueries({ queryKey: ['bookmark-status'] });
      queryClient.invalidateQueries({ queryKey: ['bookmark-stats'] });
    },
  });
}

// 🚀 性能优化：批量收藏状态管理器
export function useBookmarkManager() {
  const queryClient = useQueryClient();

  // 获取收藏统计（包含ID列表）
  const { data: stats, isLoading } = useQuery({
    queryKey: ['bookmark-stats-with-ids'],
    queryFn: () =>
      request<BookmarkStatsResponse>('/user/bookmarks/stats?includeIds=true'),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  // 创建收藏状态映射
  const bookmarkedIds = useMemo(() => {
    return new Set(stats?.bookmarkedCircleIds || []);
  }, [stats?.bookmarkedCircleIds]);

  // 快速查询收藏状态
  const isBookmarked = useCallback(
    (circleId: string) => {
      return bookmarkedIds.has(circleId);
    },
    [bookmarkedIds]
  );

  // 切换收藏状态（带乐观更新）
  const toggleBookmark = useMutation({
    mutationFn: (circleId: string) =>
      request(`/circles/${circleId}/bookmark`, { method: 'POST' }),

    onMutate: async (circleId) => {
      // 乐观更新：立即更新UI
      await queryClient.cancelQueries({
        queryKey: ['bookmark-stats-with-ids'],
      });

      const previousStats = queryClient.getQueryData([
        'bookmark-stats-with-ids',
      ]);

      queryClient.setQueryData(['bookmark-stats-with-ids'], (old: any) => {
        if (!old) return old;

        const currentIds = new Set(old.bookmarkedCircleIds || []);
        const wasBookmarked = currentIds.has(circleId);

        if (wasBookmarked) {
          currentIds.delete(circleId);
        } else {
          currentIds.add(circleId);
        }

        return {
          ...old,
          totalBookmarks: old.totalBookmarks + (wasBookmarked ? -1 : 1),
          bookmarkedCircleIds: Array.from(currentIds),
        };
      });

      return { previousStats };
    },

    onError: (err, circleId, context) => {
      // 回滚乐观更新
      if (context?.previousStats) {
        queryClient.setQueryData(
          ['bookmark-stats-with-ids'],
          context.previousStats
        );
      }
    },

    onSettled: () => {
      // 确保数据同步
      queryClient.invalidateQueries({ queryKey: ['bookmark-stats-with-ids'] });
    },
  });

  return {
    isBookmarked,
    toggleBookmark: toggleBookmark.mutate,
    isLoading,
    isToggling: toggleBookmark.isPending,
    stats,
  };
}
```

### 错误处理

```typescript
// utils/bookmarkErrorHandler.ts
export function handleBookmarkError(error: any) {
  if (error?.code === 20001) {
    return '请先登录后再进行收藏操作';
  }
  if (error?.code === 40001) {
    return '社团ID无效';
  }
  if (error?.code === 10002) {
    return '社团不存在';
  }
  if (error?.code === 50001) {
    return '服务器错误，请重试';
  }
  return '操作失败，请重试';
}

// 在组件中使用
const toggleMutation = useToggleBookmark();

const handleToggle = async (circleId: string) => {
  try {
    await toggleMutation.mutateAsync(circleId);
    toast.success('操作成功');
  } catch (error) {
    const message = handleBookmarkError(error);
    toast.error(message);
  }
};
```

### 游标分页支持

```typescript
// 无限滚动收藏列表
export function useInfiniteBookmarks(
  baseQuery: Omit<BookmarkListQuery, 'cursor'>
) {
  return useInfiniteQuery({
    queryKey: ['bookmarks-infinite', baseQuery],
    queryFn: ({ pageParam }) =>
      request<BookmarkListResponse>('/user/bookmarks', {
        params: {
          ...baseQuery,
          cursor: pageParam,
          pageSize: 20,
        },
      }),
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    initialPageParam: undefined as string | undefined,
  });
}
```

## 🎨 前端集成要点

### 个性化社团列表集成

**核心API调用**：

```typescript
// 获取个性化社团列表
const response = await fetch('/circles?page=1&pageSize=20', {
  credentials: 'include', // 携带认证信息
});
```

**集成要点**：

- 使用 `credentials: 'include'` 自动携带session cookie
- 已登录用户自动获得个性化排序
- 未登录用户正常访问，无需特殊处理
- 结合收藏状态管理实现完整体验

### 基础收藏按钮

```tsx
// 简单的收藏按钮示例
function BookmarkButton({ circleId }: { circleId: string }) {
  const { data: isBookmarked } = useBookmarkStatus(circleId);
  const toggleMutation = useToggleBookmark();

  return (
    <button onClick={() => toggleMutation.mutate(circleId)}>
      {isBookmarked ? '已收藏' : '收藏'}
    </button>
  );
```

## 🛡️ 安全注意事项

### 认证要求

```typescript
// 确保用户已登录
import { useAuth } from '@/hooks/useAuth';

function BookmarkFeature({ circleId }: { circleId: string }) {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return (
      <Button variant="outline" onClick={() => router.push('/login')}>
        登录后收藏
      </Button>
    );
  }

  // 已登录用户显示收藏功能
  return <BookmarkButton circleId={circleId} />;
}
```

### 数据验证

```typescript
// 验证收藏操作的数据完整性
function validateBookmarkData(circleId: string) {
  if (!circleId || typeof circleId !== 'string') {
    throw new Error('Invalid circle ID');
  }

  if (circleId.length < 1 || circleId.length > 50) {
    throw new Error('Circle ID length invalid');
  }
}
```

## ❓ 常见问题

### Q: 个性化排序是否会影响性能？

**A**: 性能影响很小，已做优化：

- **未登录用户**：使用全局缓存，性能无影响
- **已登录用户**：数据库层面排序，单次SQL查询完成
- **缓存策略**：个性化结果不缓存，避免缓存污染
- **索引优化**：利用现有的bookmarks表索引，查询效率高

### Q: 个性化排序的逻辑是什么？

**A**: 排序规则如下：

1. **已收藏社团**：排在列表前面
2. **未收藏社团**：排在已收藏社团后面
3. **同等收藏状态**：按社团名称字母顺序排列
4. **搜索结果**：保持相同的个性化排序逻辑

### Q: 如何在前端判断是否启用了个性化排序？

**A**: 通过用户登录状态判断：

```typescript
// 检查用户是否登录
const { user } = useAuth();
const isPersonalized = !!user;

// 在UI中显示提示
{isPersonalized && (
  <Badge variant="secondary">
    个性化排序已启用
  </Badge>
)}
```

### Q: 个性化排序是否支持搜索？

**A**: 完全支持，搜索结果同样会应用个性化排序：

```typescript
// 搜索时保持个性化排序
const { data } = useQuery({
  queryKey: ['circles', search],
  queryFn: () => request(`/circles?search=${search}`),
  // 已收藏的匹配结果会排在前面
});
```

### Q: 如何处理用户登录状态变化？

**A**: 监听认证状态变化，清理或同步收藏数据：

```typescript
// 监听登录状态变化
useEffect(() => {
  if (isAuthenticated) {
    // 用户登录后，重新获取个性化数据
    queryClient.invalidateQueries({ queryKey: ['circles'] });
    queryClient.invalidateQueries({ queryKey: ['bookmarks'] });
  } else {
    // 用户登出后，清理个人数据缓存
    queryClient.removeQueries({ queryKey: ['bookmarks'] });
    queryClient.removeQueries({ queryKey: ['bookmark-stats'] });
  }
}, [isAuthenticated]);
```

---

## 📚 相关文档

- [API 规范文档](./api-specification.md)
- [社团模块文档](./circle-module.md)
- [用户认证文档](./auth-module.md)
- [错误处理指南](./error-handling.md)

---

**📞 技术支持**: 如有问题请联系后端团队或查看项目 Issues
