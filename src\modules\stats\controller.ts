import { Context } from 'hono';

import { getStats } from './service';
import { getDB } from '@/infrastructure';

/**
 * 后台统计数据 GET /admin/stats
 */
export async function listStats(c: Context) {
  const search = new URL(c.req.url).searchParams;
  const yearParam = search.get('year');
  const year = yearParam ? Number(yearParam) : undefined;

  const db = getDB(c);
  const result = await getStats(db, year);
  return c.json(result);
}
