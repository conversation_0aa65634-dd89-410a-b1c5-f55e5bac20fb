---
sidebar_position: 4
title: 故障排查
description: Ayafeed API 常见问题和故障排查指南
---

# 故障排查

本文档提供 Ayafeed API 常见问题的诊断和解决方案。

## 🚨 常见问题

### 1. 部署相关问题

#### 问题：部署失败

**症状**：

```bash
Error: Failed to publish your Function. Got error: Script startup exceeded CPU time limit.
```

**原因**：

- 依赖包过大
- 初始化代码执行时间过长
- 内存使用超限

**解决方案**：

```bash
# 1. 检查依赖包大小
pnpm bundle-analyzer

# 2. 优化依赖
pnpm install --production

# 3. 检查 wrangler 配置
wrangler whoami
wrangler kv:namespace list
```

#### 问题：环境变量未生效

**症状**：

```
ReferenceError: JWT_SECRET is not defined
```

**解决方案**：

```bash
# 1. 检查环境变量
wrangler secret list

# 2. 设置缺失的环境变量
wrangler secret put JWT_SECRET

# 3. 验证配置
wrangler dev --local
```

### 2. 数据库相关问题

#### 问题：数据库连接失败

**症状**：

```
Error: D1_ERROR: Database not found
```

**解决方案**：

```bash
# 1. 检查数据库列表
wrangler d1 list

# 2. 检查 wrangler.jsonc 配置
cat wrangler.jsonc | grep -A 5 "d1_databases"

# 3. 重新创建数据库绑定
wrangler d1 create ayafeed-production
```

#### 问题：数据库查询超时

**症状**：

```
Error: Query timeout after 30000ms
```

**解决方案**：

```sql
-- 1. 检查查询性能
EXPLAIN QUERY PLAN SELECT * FROM circles WHERE name LIKE '%test%';

-- 2. 添加索引
CREATE INDEX idx_circles_name_search ON circles(name);

-- 3. 优化查询
SELECT * FROM circles WHERE name LIKE 'test%' LIMIT 50;
```

### 3. 认证相关问题

#### 问题：JWT Token 验证失败

**症状**：

```json
{
  "error": "Invalid token",
  "code": "AUTH_INVALID_TOKEN"
}
```

**解决方案**：

```typescript
// 1. 检查 JWT 密钥
console.log('JWT_SECRET exists:', !!env.JWT_SECRET);

// 2. 验证 Token 格式
const token = request.headers.get('Authorization')?.replace('Bearer ', '');
console.log('Token format:', token?.split('.').length === 3);

// 3. 检查 Token 过期时间
const payload = jwt.decode(token);
console.log('Token expires:', new Date(payload.exp * 1000));
```

#### 问题：CORS 错误

**症状**：

```
Access to fetch at 'https://api.ayafeed.com' from origin 'https://app.ayafeed.com' has been blocked by CORS policy
```

**解决方案**：

```typescript
// 更新 CORS 配置
const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://app.ayafeed.com',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Locale',
  'Access-Control-Max-Age': '86400',
};

// 处理预检请求
if (request.method === 'OPTIONS') {
  return new Response(null, { headers: corsHeaders });
}
```

### 4. 性能相关问题

#### 问题：响应时间过长

**症状**：

- API 响应时间 > 1000ms
- 用户体验差

**诊断步骤**：

```typescript
// 1. 添加性能监控
const start = performance.now();
const result = await someOperation();
const duration = performance.now() - start;
console.log(`Operation took ${duration}ms`);

// 2. 检查数据库查询
const dbStart = performance.now();
const data = await env.DB.prepare(query).all();
const dbDuration = performance.now() - dbStart;
console.log(`DB query took ${dbDuration}ms`);

// 3. 检查缓存命中率
const cacheKey = `circles:${locale}:${page}`;
const cached = await env.KV.get(cacheKey);
console.log(`Cache ${cached ? 'HIT' : 'MISS'} for ${cacheKey}`);
```

**解决方案**：

```typescript
// 1. 添加缓存
const cacheKey = `api:${endpoint}:${JSON.stringify(params)}`;
let result = await env.KV.get(cacheKey, 'json');

if (!result) {
  result = await fetchData(params);
  await env.KV.put(cacheKey, JSON.stringify(result), {
    expirationTtl: 300 // 5分钟
  });
}

// 2. 优化数据库查询
// 使用索引
CREATE INDEX idx_events_date_range ON events(start_date, end_date);

// 限制结果集
SELECT * FROM events ORDER BY start_date DESC LIMIT 20;

// 3. 并行处理
const [circles, events] = await Promise.all([
  getCircles(params),
  getEvents(params)
]);
```

### 5. 缓存相关问题

#### 问题：缓存数据不一致

**症状**：

- 数据更新后前端显示旧数据
- 不同语言版本数据不同步

**解决方案**：

```typescript
// 1. 实现缓存失效策略
async function updateCircle(id: string, data: any) {
  // 更新数据库
  await env.DB.prepare(updateQuery).bind(data).run();

  // 清除相关缓存
  const cacheKeys = [`circle:${id}`, `circles:list:*`, `search:circles:*`];

  await Promise.all(cacheKeys.map((key) => env.KV.delete(key)));
}

// 2. 使用版本化缓存
const cacheKey = `circles:v2:${locale}:${page}`;
const version = (await env.KV.get('cache:version:circles')) || '1';
const versionedKey = `${cacheKey}:${version}`;
```

## 🔍 诊断工具

### 1. 健康检查

```bash
# 检查 API 健康状态
curl https://api.ayafeed.com/health

# 检查特定端点
curl -I https://api.ayafeed.com/circles

# 检查认证端点
curl -H "Authorization: Bearer $TOKEN" https://api.ayafeed.com/auth/me
```

### 2. 日志分析

```bash
# 查看实时日志
wrangler tail

# 过滤错误日志
wrangler tail --format pretty | grep ERROR

# 查看特定时间段日志
wrangler tail --since 1h
```

### 3. 性能分析

```typescript
// 性能分析中间件
async function performanceMiddleware(
  request: Request,
  env: Env,
  ctx: ExecutionContext
) {
  const start = Date.now();
  const url = new URL(request.url);

  try {
    const response = await next(request, env, ctx);
    const duration = Date.now() - start;

    // 记录性能指标
    ctx.waitUntil(
      env.ANALYTICS.writeDataPoint({
        blobs: [url.pathname, request.method],
        doubles: [duration, response.status],
        indexes: ['performance'],
      })
    );

    // 慢请求告警
    if (duration > 1000) {
      console.warn(
        `Slow request: ${request.method} ${url.pathname} took ${duration}ms`
      );
    }

    return response;
  } catch (error) {
    const duration = Date.now() - start;

    // 记录错误
    ctx.waitUntil(
      env.ANALYTICS.writeDataPoint({
        blobs: [url.pathname, request.method, error.message],
        doubles: [duration, 500],
        indexes: ['error'],
      })
    );

    throw error;
  }
}
```

## 📊 监控指标

### 关键指标阈值

| 指标         | 正常       | 警告      | 严重    |
| ------------ | ---------- | --------- | ------- |
| 响应时间 P95 | &lt; 200ms | 200-500ms | > 500ms |
| 错误率       | &lt; 0.1%  | 0.1-1%    | > 1%    |
| CPU 使用率   | &lt; 50%   | 50-80%    | > 80%   |
| 内存使用率   | &lt; 70%   | 70-90%    | > 90%   |
| 数据库连接   | &lt; 50    | 50-80     | > 80    |

### 告警规则

```yaml
# Cloudflare Workers 告警配置
alerts:
  - name: 'High Error Rate'
    condition: 'error_rate > 1%'
    duration: '5m'

  - name: 'Slow Response Time'
    condition: 'p95_response_time > 500ms'
    duration: '10m'

  - name: 'High CPU Usage'
    condition: 'cpu_usage > 80%'
    duration: '5m'
```

## 🛠️ 故障恢复

### 1. 快速恢复步骤

```bash
# 1. 回滚到上一个版本
wrangler rollback

# 2. 检查服务状态
curl -f https://api.ayafeed.com/health || echo "Service down"

# 3. 重新部署
git checkout main
pnpm install
pnpm build
pnpm deploy

# 4. 验证恢复
curl https://api.ayafeed.com/circles | jq '.data | length'
```

### 2. 数据恢复

```bash
# 1. 从备份恢复数据库
wrangler d1 execute ayafeed-production --file=backup/schema.sql

# 2. 验证数据完整性
wrangler d1 execute ayafeed-production --command="SELECT COUNT(*) FROM circles"

# 3. 清除缓存
wrangler kv:key delete --namespace-id=$KV_ID "circles:*"
```

## 📞 联系支持

### 紧急联系方式

- **技术支持**: <EMAIL>
- **运维团队**: <EMAIL>
- **GitHub Issues**: [提交问题](https://github.com/ayafeed/ayafeed-api/issues)

### 问题报告模板

```markdown
## 问题描述

[简要描述问题]

## 复现步骤

1. [步骤1]
2. [步骤2]
3. [步骤3]

## 预期行为

[描述预期的正确行为]

## 实际行为

[描述实际发生的行为]

## 环境信息

- 环境: [production/staging/development]
- 时间: [问题发生时间]
- 用户ID: [如果相关]
- 请求ID: [如果有]

## 错误日志
```

[粘贴相关错误日志]

```

## 其他信息
[任何其他相关信息]
```

---

**相关文档**: [部署指南](./deployment.md) | [监控告警](./monitoring.md) | [数据库设计](./database.md)
