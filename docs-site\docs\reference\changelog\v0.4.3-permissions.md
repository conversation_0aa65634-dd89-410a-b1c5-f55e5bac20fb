# v0.4.3 权限系统更新

> 更新时间：2025-07-28
> 影响范围：后端权限控制、前端权限判断

## 🔄 权限变更概述

本次更新调整了管理后台的权限控制策略，允许 `editor` 角色访问更多管理功能，提升内容管理效率。

## 📋 具体变更

### 权限调整

| 资源类型           | 变更前   | 变更后         | 说明               |
| ------------------ | -------- | -------------- | ------------------ |
| `/admin/events/*`  | 仅 admin | admin + editor | editor 可管理展会  |
| `/admin/circles/*` | 仅 admin | admin + editor | editor 可管理社团  |
| `/admin/users/*`   | 仅 admin | 仅 admin       | 用户管理仍限 admin |
| `/admin/logs/*`    | 仅 admin | 仅 admin       | 日志查看仍限 admin |
| `/admin/stats/*`   | 仅 admin | 仅 admin       | 统计数据仍限 admin |

### 角色权限矩阵

| 功能         | admin | editor | viewer |
| ------------ | ----- | ------ | ------ |
| 管理用户     | ✅    | ❌     | ❌     |
| 管理展会     | ✅    | ✅     | ❌     |
| 管理社团     | ✅    | ✅     | ❌     |
| 查看统计     | ✅    | ❌     | ❌     |
| 查看日志     | ✅    | ❌     | ❌     |
| 查看公开内容 | ✅    | ✅     | ✅     |

## 🔧 技术实现

### 后端变更

```typescript
// src/modules/admin/routes.ts
routes.use('*', authMiddleware());

// 细粒度权限控制
routes.use('/events/*', roleGuard(['admin', 'editor']));
routes.use('/circles/*', roleGuard(['admin', 'editor']));
routes.use('/users/*', roleGuard('admin'));
routes.use('/logs/*', roleGuard('admin'));
routes.use('/stats/*', roleGuard('admin'));
```

### 前端适配

```typescript
// 权限判断示例
const permissions = {
  canManageUsers: user?.role === 'admin',
  canManageEvents: ['admin', 'editor'].includes(user?.role || ''),
  canManageCircles: ['admin', 'editor'].includes(user?.role || ''),
  canViewStats: user?.role === 'admin',
  canViewLogs: user?.role === 'admin',
};
```

## 🎯 业务价值

1. **提升效率**：editor 用户可直接管理展会和社团，无需 admin 介入
2. **权责分离**：保持用户管理、系统监控等敏感功能的 admin 独占
3. **扩展性**：为未来更细粒度的权限控制奠定基础

## 🚀 升级指南

### 对现有用户的影响

- **admin 用户**：无影响，保持所有权限
- **editor 用户**：新增展会和社团管理权限
- **viewer 用户**：无影响，保持只读权限

### 前端代码更新

如果你的前端代码中有硬编码的权限判断，请更新为：

```typescript
// ❌ 旧的判断方式
if (user.role === 'admin') {
  // 显示管理按钮
}

// ✅ 新的判断方式
if (['admin', 'editor'].includes(user.role)) {
  // 显示展会/社团管理按钮
}
if (user.role === 'admin') {
  // 显示用户/统计/日志管理按钮
}
```

## 🔍 测试验证

### 测试用例

1. **editor 用户访问 `/admin/events`** → 应返回 200
2. **editor 用户访问 `/admin/circles`** → 应返回 200
3. **editor 用户访问 `/admin/users`** → 应返回 403
4. **editor 用户访问 `/admin/stats`** → 应返回 403
5. **admin 用户访问所有路径** → 应返回 200

### 验证命令

```bash
# 使用 editor 账户测试
curl -X POST http://localhost:8145/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"editor","password":"password"}' \
  -c cookies.txt

# 测试权限
curl -b cookies.txt http://localhost:8145/admin/events    # 应成功
curl -b cookies.txt http://localhost:8145/admin/circles   # 应成功
curl -b cookies.txt http://localhost:8145/admin/users     # 应返回 403
```

## 📚 相关文档

- [用户模块文档](../modules/users.md)
- [角色与权限接入说明](../project/prd/role-access.md)
- [前端认证指南](../frontend/authentication.md)

## 🐛 已知问题

无

## 🔮 后续规划

- 考虑引入更细粒度的权限控制（如资源级权限）
- 支持自定义角色和权限组合
- 添加权限变更的审计日志
