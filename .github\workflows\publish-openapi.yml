name: Publish OpenAPI JSON to gh-pages

on:
  push:
    branches: [main]
    paths:
      - 'openapi.json'

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Prepare publish directory
        run: |
          mkdir -p ./gh-pages
          cp openapi.json ./gh-pages/openapi.json

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_branch: gh-pages
          publish_dir: ./gh-pages
          keep_files: true
          commit_message: 'chore(docs): update openapi.json'
