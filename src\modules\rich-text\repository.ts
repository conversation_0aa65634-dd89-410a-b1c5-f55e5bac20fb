// D1Database 类型现在在全局作用域中可用
import { v4 as uuidv4 } from 'uuid';
import type {
  RichTextContent,
  CreateContentRequest,
  UpdateContentRequest,
  EntityType,
  // ContentType,
} from './schema';

export interface ContentRepository {
  findByEntity(
    entityType: EntityType,
    entityId: string
  ): Promise<RichTextContent[]>;
  findByEntityAndType(
    entityType: EntityType,
    entityId: string,
    contentType: string
  ): Promise<RichTextContent | null>;
  create(data: CreateContentRequest): Promise<RichTextContent>;
  update(id: string, data: UpdateContentRequest): Promise<void>;
  upsert(
    entityType: EntityType,
    entityId: string,
    languageCode: string,
    contentType: string,
    content: string
  ): Promise<RichTextContent>;
  delete(id: string): Promise<void>;
  deleteByEntity(entityType: EntityType, entityId: string): Promise<void>;
}

export function createContentRepository(db: D1Database): ContentRepository {
  return {
    async findByEntity(
      entityType: EntityType,
      entityId: string
    ): Promise<RichTextContent[]> {
      const stmt = db.prepare(`
        SELECT * FROM rich_text_contents 
        WHERE entity_type = ? AND entity_id = ?
        ORDER BY content_type
      `);

      const result = await stmt.bind(entityType, entityId).all();
      return result.results as RichTextContent[];
    },

    async findByEntityAndType(
      entityType: EntityType,
      entityId: string,
      contentType: string
    ): Promise<RichTextContent | null> {
      const stmt = db.prepare(`
        SELECT * FROM rich_text_contents 
        WHERE entity_type = ? AND entity_id = ? AND content_type = ?
      `);

      const result = await stmt.bind(entityType, entityId, contentType).first();
      return result as RichTextContent | null;
    },

    async create(data: CreateContentRequest): Promise<RichTextContent> {
      const id = uuidv4();
      const now = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO rich_text_contents (
          id, entity_type, entity_id, content_type, content, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      await stmt
        .bind(
          id,
          data.entity_type,
          data.entity_id,
          data.content_type,
          data.content,
          now,
          now
        )
        .run();

      return {
        id,
        ...data,
        created_at: now,
        updated_at: now,
      };
    },

    async update(id: string, data: UpdateContentRequest): Promise<void> {
      const now = new Date().toISOString();

      const stmt = db.prepare(`
        UPDATE rich_text_contents 
        SET content = ?, updated_at = ?
        WHERE id = ?
      `);

      await stmt.bind(data.content, now, id).run();
    },

    async upsert(
      entityType: EntityType,
      entityId: string,
      languageCode: string,
      contentType: string,
      content: string
    ): Promise<RichTextContent> {
      const existing = await this.findByEntityAndType(
        entityType,
        entityId,
        contentType
      );

      if (existing) {
        await this.update(existing.id, { content });
        return {
          ...existing,
          content,
          updated_at: new Date().toISOString(),
        };
      } else {
        return await this.create({
          entity_type: entityType,
          entity_id: entityId,
          language_code: languageCode as any,
          content_type: contentType,
          content,
        });
      }
    },

    async delete(id: string): Promise<void> {
      const stmt = db.prepare('DELETE FROM rich_text_contents WHERE id = ?');
      await stmt.bind(id).run();
    },

    async deleteByEntity(
      entityType: EntityType,
      entityId: string
    ): Promise<void> {
      const stmt = db.prepare(`
        DELETE FROM rich_text_contents 
        WHERE entity_type = ? AND entity_id = ?
      `);
      await stmt.bind(entityType, entityId).run();
    },
  };
}
