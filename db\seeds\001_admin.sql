-- 更新管理员用户创建脚本
-- 删除旧的 auth_user 相关代码，改为插入 Better Auth 的 user 表

INSERT OR IGNORE INTO user (
  id, 
  name, 
  email, 
  role, 
  locale,
  email_verified, 
  created_at, 
  updated_at
) VALUES (
  '26edc487-fc17-46aa-9833-10be85fef91d', 
  'admin', 
  '<EMAIL>',
  'admin', 
  'zh',
  1,
  strftime('%s', 'now') * 1000,
  strftime('%s', 'now') * 1000
);

-- 为管理员创建密码凭据
INSERT OR IGNORE INTO account (
  id,
  account_id,
  provider_id,
  user_id,
  password,
  created_at,
  updated_at
) VALUES (
  'admin_credential',
  'admin',
  'credential',
  '26edc487-fc17-46aa-9833-10be85fef91d',
  '$2a$12$QdAJZT8j46n2y0uZeLl/suoVl/CdWCKssiR4eHAE01JLjHRNg4O8S',  -- admin123
  strftime('%s', 'now') * 1000,
  strftime('%s', 'now') * 1000
);

