# 出展模块 (Appearances Module)

## 概述

出展模块管理作者在各种展会活动中的出展记录，建立作者、社团和展会之间的关联关系。

## 功能特性

- ✅ 出展记录管理
- ✅ 作者-社团-展会关联
- ✅ 展位信息记录
- ✅ 出展历史查询
- ✅ 多维度查询支持

## API端点

### 公开接口

#### GET /appearances

**功能**: 获取出展记录列表

**查询参数**:

- `event_id` (可选) - 按展会ID过滤
- `circle_id` (可选) - 按社团ID过滤
- `artist_id` (可选) - 按作者ID过滤
- `page` (可选) - 页码，默认 `1`
- `pageSize` (可选) - 每页数量，默认 `50`

**响应格式**:

```json
{
  "items": [
    {
      "id": "appearance-123",
      "event_id": "event-456",
      "circle_id": "circle-789",
      "artist_id": "artist-101",
      "booth_id": "A-01",
      "event_name": "Comiket 103",
      "circle_name": "某某工作室",
      "artist_name": "作者名称",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 200,
  "page": 1,
  "pageSize": 50
}
```

#### GET /appearances/{id}

**功能**: 获取出展记录详情

**路径参数**:

- `id` (必需) - 出展记录ID

**响应格式**:

```json
{
  "success": true,
  "data": {
    "id": "appearance-123",
    "event_id": "event-456",
    "circle_id": "circle-789",
    "artist_id": "artist-101",
    "booth_id": "A-01",
    "event": {
      "id": "event-456",
      "name": "Comiket 103",
      "date": "2024-12-30T10:00:00Z",
      "venue_name": "东京国际展示场"
    },
    "circle": {
      "id": "circle-789",
      "name": "某某工作室",
      "description": "专注原创同人作品"
    },
    "artist": {
      "id": "artist-101",
      "name": "作者名称",
      "description": "知名同人作者"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 后台管理接口

#### GET /admin/appearances

**功能**: 后台获取出展记录列表

**权限要求**: `admin` 或 `editor` 角色

#### POST /admin/appearances

**功能**: 创建出展记录

**权限要求**: `admin` 或 `editor` 角色

**请求体**:

```json
{
  "event_id": "event-456",
  "circle_id": "circle-789",
  "artist_id": "artist-101",
  "booth_id": "A-01"
}
```

#### PUT /admin/appearances/{id}

**功能**: 更新出展记录

**权限要求**: `admin` 或 `editor` 角色

#### DELETE /admin/appearances/{id}

**功能**: 删除出展记录

**权限要求**: `admin` 角色

## 数据结构

### Appearance Schema

```typescript
interface Appearance {
  id: string;
  event_id: string;
  circle_id: string;
  artist_id: string;
  booth_id: string | null;
  created_at: string;
  updated_at: string;
}
```

### 数据库表结构

```sql
CREATE TABLE appearances (
  id TEXT PRIMARY KEY,
  event_id TEXT NOT NULL,
  circle_id TEXT NOT NULL,
  artist_id TEXT NOT NULL,
  booth_id TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (event_id) REFERENCES events(id),
  FOREIGN KEY (circle_id) REFERENCES circles(id),
  FOREIGN KEY (artist_id) REFERENCES artists(id),
  UNIQUE(event_id, circle_id, artist_id)
);
```

## 关联查询

### 按展会查询出展社团

```bash
curl -X GET "https://api.example.com/appearances?event_id=event-456"
```

### 按社团查询出展历史

```bash
curl -X GET "https://api.example.com/appearances?circle_id=circle-789"
```

### 按作者查询出展记录

```bash
curl -X GET "https://api.example.com/appearances?artist_id=artist-101"
```

## 业务逻辑

### 出展记录创建规则

1. **唯一性约束**: 同一展会中，同一社团的同一作者只能有一条出展记录
2. **关联验证**: 创建前需验证 event_id、circle_id、artist_id 的有效性
3. **展位信息**: booth_id 可选，用于记录具体展位号

### 数据一致性

1. **级联查询**: 获取出展记录时自动关联展会、社团、作者信息
2. **软删除**: 支持软删除机制，保留历史数据
3. **审计日志**: 记录所有创建、更新、删除操作

## 缓存策略

- **缓存键格式**:
  - `appearances:event:{event_id}:{page}:{pageSize}`
  - `appearances:circle:{circle_id}:{page}:{pageSize}`
  - `appearances:artist:{artist_id}:{page}:{pageSize}`
- **TTL**: 1小时
- **缓存更新**: 出展记录变更时清除相关缓存

## 性能优化

1. **复合索引**: 在 (event_id, circle_id, artist_id) 上建立复合索引
2. **分页查询**: 支持分页减少数据传输量
3. **关联查询优化**: 使用 JOIN 减少数据库查询次数

## 限制和注意事项

1. **数据完整性**: 必须确保关联的展会、社团、作者存在
2. **唯一性**: 同一展会中同一社团的同一作者不能重复出展
3. **展位格式**: booth_id 建议使用标准格式（如 A-01, B-23）
4. **删除限制**: 删除展会、社团或作者时需要处理关联的出展记录

## 未来规划

- [ ] 出展状态管理（确认、取消、待定）
- [ ] 出展费用记录
- [ ] 出展作品信息
- [ ] 出展评价系统
- [ ] 出展统计分析

---

_最后更新: 2025-07-28_
