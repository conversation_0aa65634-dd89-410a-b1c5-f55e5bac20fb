import { Context } from 'hono';

import { jsonError } from './errorResponse';

/**
 * Check if value is a plain object (not null and typeof object but not array)
 */
const isPlainObject = (val: unknown): val is Record<string, unknown> => {
  return val !== null && typeof val === 'object' && !Array.isArray(val);
};

/**
 * Pick specified keys from an object
 */
const pick = <T extends Record<string, unknown>>(
  obj: T,
  keys: (keyof T)[]
): Partial<T> => {
  const res: Partial<T> = {};
  for (const k of keys) {
    if (k in obj) {
      // 属性写入保持 any 但局部
      (res as Record<string, unknown>)[k as string] = obj[k];
    }
  }
  return res;
};

/**
 * Apply `fields` query parameter from the request.
 *
 * If the parameter exists, it should be a comma-separated list of keys.
 * – Validate all requested fields exist on the sample object (first element for arrays, object itself for single objects).
 * – If unknown fields are found, return a 400 error response.
 * – Otherwise, filter the response data so that only requested keys are included.
 */
export const applyFieldFilter = (
  c: Context,
  data: unknown
): Response | unknown => {
  const fieldsParam = c.req.query('fields');
  // 未传入字段参数时直接返回原数据；若传入但为空字符串，则返回错误
  if (fieldsParam === undefined || fieldsParam === null) {
    return data;
  }

  if (fieldsParam.trim() === '') {
    return jsonError(c, 30001, 'fields parameter cannot be empty', 400);
  }

  // Split and sanitise – ignore empty strings caused by `,,`
  const fields = fieldsParam
    .split(',')
    .map((f) => f.trim())
    .filter(Boolean);

  if (!fields.length) {
    // 如果过滤后依旧为空，同样视为无效
    return jsonError(c, 30001, 'fields parameter cannot be empty', 400);
  }

  // -------- 新增：检测并处理分页结构 ----------------------------------
  const hasItemsArray = (val: unknown): val is { items: unknown[] } =>
    isPlainObject(val) &&
    'items' in val &&
    Array.isArray((val as { items: unknown }).items);

  const isPaginated = hasItemsArray(data);

  if (isPaginated) {
    const paginated = data;

    const sample = paginated.items.length > 0 ? paginated.items[0] : null;
    if (!sample || !isPlainObject(sample)) return data; // 无数据或非对象，跳过过滤

    // Validate fields
    const unknown = fields.filter((f) => !(f in sample));
    if (unknown.length) {
      return jsonError(
        c,
        30002,
        `Unknown field(s): ${unknown.join(', ')}`,
        400
      );
    }

    // Perform filtering on items only
    const filteredItems = paginated.items.map((item) =>
      isPlainObject(item) ? pick(item, fields) : item
    );

    return { ...paginated, items: filteredItems };
  }
  // -------- 原逻辑保持不变 ---------------------------------------------

  // Determine sample object to validate keys
  const sample = Array.isArray(data)
    ? data.length > 0
      ? data[0]
      : null
    : isPlainObject(data)
      ? (data as Record<string, unknown>)
      : null;

  if (!sample) {
    // Data not an object/array of objects – return as is
    return data;
  }

  // Validate fields
  const unknown = fields.filter((f) => !(f in sample));
  if (unknown.length) {
    return jsonError(c, 30002, `Unknown field(s): ${unknown.join(', ')}`, 400);
  }

  // Perform filtering
  if (Array.isArray(data)) {
    return data.map((item) =>
      isPlainObject(item) ? pick(item, fields) : item
    );
  }
  return pick(sample, fields);
};

export const jsonWithFields = (c: Context, data: unknown) => {
  const filtered = applyFieldFilter(c, data);
  if (filtered instanceof Response) return filtered;
  // Cast to object to satisfy type expectations without triggering no-empty-object-type rule
  return c.json(filtered as object);
};
