import { describe, it, expect, vi } from 'vitest';

import {
  listAppearances,
  createAppearance,
} from '@/modules/appearance/controller';

/**
 * 构造可最小化满足 controller 依赖的 Hono Context stub。
 */
function createMockContext({
  url = 'http://localhost/appearances',
  query = {} as Record<string, string>,
  body = undefined,
  db = createMockDB(),
}: {
  url?: string;
  query?: Record<string, string>;
  body?: any;
  db?: any;
}) {
  // 提前解析 URLSearchParams，供 query() 方法与 URL 同步使用
  const urlObj = new URL(url);
  Object.entries(query).forEach(([k, v]) => urlObj.searchParams.set(k, v));

  // 本地存储用于模拟 Context.set/get
  const store: Record<string, any> = {};

  return {
    req: {
      url: urlObj.toString(),
      query: (key: string) => urlObj.searchParams.get(key) ?? undefined,
      json: async () => body,
    },
    env: { DB: db },
    // header() 在 listAppearances 内部用于写 Deprecation, 需可 spy
    header: vi.fn(),
    // json() 直接返回对象，便于断言
    json: (data: any, _status?: number) => data,
    // 提供 get/set 存取接口，供 errorResponse 使用
    get: (key: string) => store[key],
    set: (key: string, value: any) => {
      store[key] = value;
    },
  } as any;
}

/**
 * 复用 integration 测试中使用的 D1 mock。
 */
function createMockDB() {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();
      const buildResp = () => ({
        all: async () => ({
          results: [
            { id: '1', circle_id: '1', event_id: 'e1', booth_id: 'A01a' },
          ],
        }),
        first: async () => {
          if (upper.includes('COUNT(*)')) return { total: 1 };
          return { id: '1', circle_id: '1', event_id: 'e1' };
        },
        run: async () => ({ success: true }),
        bind: (..._args: any[]) => buildResp(),
      });
      return buildResp();
    },
  };
}

describe('Appearance Controller', () => {
  it('listAppearances: should set Deprecation header when filter by event_id', async () => {
    const ctx = createMockContext({ query: { event_id: 'e1' } });
    const result: any = await listAppearances(ctx);

    expect(result.items.length).toBe(1);
    // header 方法应被调用一次并包含 Deprecation
    expect(ctx.header).toHaveBeenCalledWith('Deprecation', 'true');
  });

  it('createAppearance: should return error when booth_id missing', async () => {
    // stub uuid 生成固定值，避免快照差异
    vi.mock('uuid', () => ({ v4: () => 'req-id' }));

    const ctx = createMockContext({
      body: {
        id: 'a1',
        circle_id: 'c1',
        event_id: 'e1',
        artist_id: 'u1',
        // booth_id 缺失
        path: '/path',
      },
    });
    const data: any = await createAppearance(ctx);

    expect(data.code).toBe(50002);
    expect(data.message).toBe('booth_id is required');
  });

  it('createAppearance: should insert when booth_id provided', async () => {
    const db = createMockDB();
    // spy prepare 以确认执行了插入语句
    const prepareSpy = vi.spyOn(db, 'prepare');
    const ctx = createMockContext({
      body: {
        id: 'a2',
        circle_id: 'c1',
        event_id: 'e1',
        artist_id: 'u1',
        booth_id: 'A01a',
        path: '/path',
      },
      db,
    });
    const resp: any = await createAppearance(ctx);

    expect(resp.success).toBe(true);
    expect(prepareSpy).toHaveBeenCalled();
  });
});
