import type { EntityType, LanguageCode, ContentTypeConfig } from './schema';
import { RichTextI18nManager } from './i18n';

/**
 * 预设配置保护管理器
 * 处理预设配置的保护逻辑和验证
 */
export class PresetProtectionManager {
  // ========================================
  // 预设配置验证
  // ========================================

  /**
   * 检查配置是否为预设配置
   */
  static isPresetConfig(config: ContentTypeConfig): boolean {
    return config.is_preset === true;
  }

  /**
   * 检查配置是否可以删除
   */
  static canDelete(config: ContentTypeConfig): ValidationResult {
    if (this.isPresetConfig(config)) {
      return {
        isValid: false,
        errors: ['预设配置不能删除'],
        code: 'PRESET_CANNOT_DELETE',
      };
    }

    return {
      isValid: true,
      errors: [],
    };
  }

  /**
   * 检查配置是否可以禁用
   */
  static canDisable(config: ContentTypeConfig): ValidationResult {
    if (this.isPresetConfig(config)) {
      return {
        isValid: false,
        errors: ['预设配置不能禁用'],
        code: 'PRESET_CANNOT_DISABLE',
      };
    }

    return {
      isValid: true,
      errors: [],
    };
  }

  /**
   * 检查配置键值是否可以修改
   */
  static canModifyKey(config: ContentTypeConfig): ValidationResult {
    if (this.isPresetConfig(config)) {
      return {
        isValid: false,
        errors: ['预设配置的键值不能修改'],
        code: 'PRESET_KEY_IMMUTABLE',
      };
    }

    return {
      isValid: true,
      errors: [],
    };
  }

  /**
   * 检查配置的预设标记是否可以修改
   */
  static canModifyPresetFlag(_config: ContentTypeConfig): ValidationResult {
    // 预设标记一旦设置就不能修改
    return {
      isValid: false,
      errors: ['预设标记不能修改'],
      code: 'PRESET_FLAG_IMMUTABLE',
    };
  }

  // ========================================
  // 批量操作验证
  // ========================================

  /**
   * 验证批量删除操作
   */
  static validateBatchDelete(
    configs: ContentTypeConfig[]
  ): BatchValidationResult {
    const results: ValidationResult[] = [];
    const validConfigs: ContentTypeConfig[] = [];
    const invalidConfigs: ContentTypeConfig[] = [];

    for (const config of configs) {
      const validation = this.canDelete(config);
      results.push(validation);

      if (validation.isValid) {
        validConfigs.push(config);
      } else {
        invalidConfigs.push(config);
      }
    }

    return {
      isValid: invalidConfigs.length === 0,
      validConfigs,
      invalidConfigs,
      results,
      summary: {
        total: configs.length,
        valid: validConfigs.length,
        invalid: invalidConfigs.length,
      },
    };
  }

  /**
   * 验证批量状态更新操作
   */
  static validateBatchStatusUpdate(
    configs: ContentTypeConfig[],
    newStatus: boolean
  ): BatchValidationResult {
    const results: ValidationResult[] = [];
    const validConfigs: ContentTypeConfig[] = [];
    const invalidConfigs: ContentTypeConfig[] = [];

    for (const config of configs) {
      let validation: ValidationResult;

      if (!newStatus) {
        // 禁用操作
        validation = this.canDisable(config);
      } else {
        // 启用操作总是允许的
        validation = { isValid: true, errors: [] };
      }

      results.push(validation);

      if (validation.isValid) {
        validConfigs.push(config);
      } else {
        invalidConfigs.push(config);
      }
    }

    return {
      isValid: invalidConfigs.length === 0,
      validConfigs,
      invalidConfigs,
      results,
      summary: {
        total: configs.length,
        valid: validConfigs.length,
        invalid: invalidConfigs.length,
      },
    };
  }

  // ========================================
  // 预设配置初始化
  // ========================================

  /**
   * 生成预设配置数据
   */
  static generatePresetConfigs(
    entityType: EntityType,
    languageCode: LanguageCode
  ): PresetConfigData[] {
    const presetConfigs = RichTextI18nManager.getPresetConfigsForLanguage(
      entityType,
      languageCode
    );

    return presetConfigs.map((config) => ({
      id: this.generatePresetId(entityType, languageCode, config.key),
      entity_type: entityType,
      language_code: languageCode,
      key: config.key,
      label: config.label,
      placeholder: config.placeholder,
      icon: config.icon,
      sort_order: config.sort_order,
      is_active: true,
      is_preset: true,
      deleted_at: null,
      deleted_by: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }));
  }

  /**
   * 生成预设配置的固定ID
   */
  private static generatePresetId(
    entityType: EntityType,
    languageCode: LanguageCode,
    key: string
  ): string {
    return `preset-${entityType}-${languageCode}-${key}`;
  }

  /**
   * 检查预设配置是否已存在
   */
  static isPresetConfigExists(
    existingConfigs: ContentTypeConfig[],
    entityType: EntityType,
    languageCode: LanguageCode,
    key: string
  ): boolean {
    const presetId = this.generatePresetId(entityType, languageCode, key);
    return existingConfigs.some((config) => config.id === presetId);
  }

  /**
   * 获取缺失的预设配置
   */
  static getMissingPresetConfigs(
    existingConfigs: ContentTypeConfig[],
    entityType: EntityType,
    languageCode: LanguageCode
  ): PresetConfigData[] {
    const allPresets = this.generatePresetConfigs(entityType, languageCode);

    return allPresets.filter(
      (preset) =>
        !this.isPresetConfigExists(
          existingConfigs,
          entityType,
          languageCode,
          preset.key
        )
    );
  }

  // ========================================
  // 预设配置修复
  // ========================================

  /**
   * 检查并修复预设配置
   */
  static checkAndRepairPresetConfigs(
    existingConfigs: ContentTypeConfig[],
    entityType: EntityType,
    languageCode: LanguageCode
  ): RepairResult {
    const missingConfigs = this.getMissingPresetConfigs(
      existingConfigs,
      entityType,
      languageCode
    );

    const corruptedConfigs = this.findCorruptedPresetConfigs(
      existingConfigs,
      entityType,
      languageCode
    );

    return {
      needsRepair: missingConfigs.length > 0 || corruptedConfigs.length > 0,
      missingConfigs,
      corruptedConfigs,
      repairActions: this.generateRepairActions(
        missingConfigs,
        corruptedConfigs
      ),
    };
  }

  /**
   * 查找损坏的预设配置
   */
  private static findCorruptedPresetConfigs(
    existingConfigs: ContentTypeConfig[],
    entityType: EntityType,
    languageCode: LanguageCode
  ): CorruptedConfig[] {
    const expectedPresets = this.generatePresetConfigs(
      entityType,
      languageCode
    );
    const corruptedConfigs: CorruptedConfig[] = [];

    for (const expected of expectedPresets) {
      const existing = existingConfigs.find(
        (config) => config.id === expected.id
      );

      if (existing && existing.is_preset) {
        const issues: string[] = [];

        if (existing.key !== expected.key) {
          issues.push('键值不匹配');
        }

        if (existing.entity_type !== expected.entity_type) {
          issues.push('实体类型不匹配');
        }

        if (existing.language_code !== expected.language_code) {
          issues.push('语言代码不匹配');
        }

        if (existing.deleted_at !== null) {
          issues.push('预设配置被标记为删除');
        }

        if (issues.length > 0) {
          corruptedConfigs.push({
            config: existing,
            expected: expected,
            issues,
          });
        }
      }
    }

    return corruptedConfigs;
  }

  /**
   * 生成修复操作
   */
  private static generateRepairActions(
    missingConfigs: PresetConfigData[],
    corruptedConfigs: CorruptedConfig[]
  ): RepairAction[] {
    const actions: RepairAction[] = [];

    // 添加缺失的配置
    for (const missing of missingConfigs) {
      actions.push({
        type: 'CREATE',
        config: missing,
        reason: '缺失预设配置',
      });
    }

    // 修复损坏的配置
    for (const corrupted of corruptedConfigs) {
      actions.push({
        type: 'UPDATE',
        config: corrupted.expected,
        reason: `修复损坏的预设配置: ${corrupted.issues.join(', ')}`,
      });
    }

    return actions;
  }
}

// ========================================
// 类型定义
// ========================================

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  code?: string;
}

interface BatchValidationResult {
  isValid: boolean;
  validConfigs: ContentTypeConfig[];
  invalidConfigs: ContentTypeConfig[];
  results: ValidationResult[];
  summary: {
    total: number;
    valid: number;
    invalid: number;
  };
}

interface PresetConfigData {
  id: string;
  entity_type: EntityType;
  language_code: LanguageCode;
  key: string;
  label: string;
  placeholder: string;
  icon: string;
  sort_order: number;
  is_active: boolean;
  is_preset: boolean;
  deleted_at: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
}

interface CorruptedConfig {
  config: ContentTypeConfig;
  expected: PresetConfigData;
  issues: string[];
}

interface RepairAction {
  type: 'CREATE' | 'UPDATE';
  config: PresetConfigData;
  reason: string;
}

interface RepairResult {
  needsRepair: boolean;
  missingConfigs: PresetConfigData[];
  corruptedConfigs: CorruptedConfig[];
  repairActions: RepairAction[];
}
