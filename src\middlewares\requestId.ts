import type { MiddlewareHandler } from 'hono';
import { v4 as uuidv4 } from 'uuid';

/**
 * 为每个请求注入 requestId，便于前端和后端追踪
 */
export const requestIdMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const incoming = c.req.header('x-request-id');
    const id = incoming || uuidv4();
    c.set('requestId', id);
    // 立即写入响应头，确保即便抛错依旧带回
    c.header('X-Request-Id', id);
    await next();
  };
};
