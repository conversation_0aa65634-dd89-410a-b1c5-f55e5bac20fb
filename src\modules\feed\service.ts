import type { D1Database } from '@cloudflare/workers-types';

import type { FeedItem } from './schema';
import type { C<PERSON>, Logger } from '@/infrastructure';
import type { Locale } from '@/middlewares/locale';

/**
 * 获取 Feed 流数据
 */
export async function getFeedData(
  db: D1Database,
  page: number,
  limit: number,
  type: 'all' | 'events' | 'circles',
  locale: Locale,
  cache?: Cache,
  logger?: Logger
): Promise<{ items: FeedItem[]; total: number }> {
  const offset = (page - 1) * limit;

  // 构造缓存键
  const cacheKey = `feed:${locale}:${type}:page:${page}:limit:${limit}`;

  if (cache) {
    const cached = await cache.get<{ items: FeedItem[]; total: number }>(
      cacheKey
    );
    if (cached) {
      logger?.debug?.('getFeedData: hit cache', { key: cacheKey });
      return cached;
    }
  }

  const items: FeedItem[] = [];
  let total = 0;

  // 获取事件数据
  if (type === 'all' || type === 'events') {
    const eventQuery = `
      SELECT 
        'event' as type,
        id,
        name_${locale} as name,
        description_${locale} as description,
        date as start_date,
        image_url,
        created_at
      FROM events 
      WHERE name_${locale} IS NOT NULL
      ORDER BY created_at DESC, date DESC
      LIMIT ? OFFSET ?
    `;

    const { results: eventResults } = await db
      .prepare(eventQuery)
      .bind(limit, offset)
      .all();

    // 转换为 Feed 格式
    const eventFeedItems: FeedItem[] = (eventResults as any[]).map(
      (event, _index) => ({
        id: `feed-event-${event.id}`,
        type: 'event' as const,
        content: {
          id: event.id,
          name: event.name,
          description: event.description,
          start_date: event.start_date,
          image_url: event.image_url,
        },
        created_at: event.created_at || new Date().toISOString(),
      })
    );

    items.push(...eventFeedItems);

    // 获取事件总数
    if (type === 'events') {
      const countResult = await db
        .prepare(
          `SELECT COUNT(*) as count FROM events WHERE name_${locale} IS NOT NULL`
        )
        .first<{ count: number }>();
      total = countResult?.count || 0;
    }
  }

  // 获取社团数据
  if (type === 'all' || type === 'circles') {
    const circleQuery = `
      SELECT 
        'circle' as type,
        id,
        name_${locale} as name,
        description_${locale} as description,
        created_at
      FROM circles 
      WHERE name_${locale} IS NOT NULL
      ORDER BY created_at DESC, name_${locale} ASC
      LIMIT ? OFFSET ?
    `;

    const { results: circleResults } = await db
      .prepare(circleQuery)
      .bind(limit, offset)
      .all();

    // 转换为 Feed 格式
    const circleFeedItems: FeedItem[] = (circleResults as any[]).map(
      (circle, _index) => ({
        id: `feed-circle-${circle.id}`,
        type: 'circle' as const,
        content: {
          id: circle.id,
          name: circle.name,
          description: circle.description,
          start_date: null,
          image_url: null,
        },
        created_at: circle.created_at || new Date().toISOString(),
      })
    );

    items.push(...circleFeedItems);

    // 获取社团总数
    if (type === 'circles') {
      const countResult = await db
        .prepare(
          `SELECT COUNT(*) as count FROM circles WHERE name_${locale} IS NOT NULL`
        )
        .first<{ count: number }>();
      total = countResult?.count || 0;
    }
  }

  // 如果是 'all' 类型，需要混合排序并获取总数
  if (type === 'all') {
    items.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    // 获取总数
    const [eventCountResult, circleCountResult] = await Promise.all([
      db
        .prepare(
          `SELECT COUNT(*) as count FROM events WHERE name_${locale} IS NOT NULL`
        )
        .first<{ count: number }>(),
      db
        .prepare(
          `SELECT COUNT(*) as count FROM circles WHERE name_${locale} IS NOT NULL`
        )
        .first<{ count: number }>(),
    ]);

    total = (eventCountResult?.count || 0) + (circleCountResult?.count || 0);
  }

  // 限制结果数量
  const finalItems = items.slice(0, limit);
  const result = { items: finalItems, total };

  if (cache) {
    // 缓存 5 分钟
    await cache.set(cacheKey, result, 300);
    logger?.debug?.('getFeedData: cached results', {
      key: cacheKey,
      count: finalItems.length,
      total,
    });
  }

  return result;
}
