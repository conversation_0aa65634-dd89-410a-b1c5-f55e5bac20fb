// The shape of the environment variables for the Hono app
export type Env = {
  Bindings: CloudflareBindings;
};

// Better Auth 认证上下文类型 - 与 betterAuthMiddleware 保持一致
export type BetterAuthContext = {
  user: {
    id: string;
    email: string;
    name?: string;
    role: string;
  } | null;
  session: {
    id: string;
    userId: string;
    expiresAt: Date;
  } | null;
};

// 兼容性：保留旧的 AuthContext 类型，但标记为已弃用
/** @deprecated 使用 BetterAuthContext 替代 */
export type AuthContext = {
  user: {
    id: string;
    username: string;
    role?: string;
    email?: string;
  } | null;
  session: {
    id: string;
  } | null;
};

// Define the full Hono app type by extending the base env
export type HonoApp = {
  Bindings: CloudflareBindings;
  Variables: {
    auth: BetterAuthContext;
    // 兼容性：roleGuard 中间件使用的用户信息
    user?: {
      id: string;
      role: string;
      username: string;
      email: string;
    };
  };
};

// Global type declarations for Cloudflare Workers environment
declare global {
  // Base64 encoding/decoding functions available in Cloudflare Workers
  function btoa(data: string): string;
  function atob(data: string): string;

  // 启动检查标记
  var __richTextTabsChecked: boolean | undefined;
}

export {};
