import js from '@eslint/js';
import tsParser from '@typescript-eslint/parser';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import prettierPlugin from 'eslint-plugin-prettier';
import importPlugin from 'eslint-plugin-import';
import boundariesPlugin from 'eslint-plugin-boundaries';

/** @type {import('eslint').Linter.FlatConfig[]} */
export default [
  // 忽略
  {
    ignores: [
      '**/node_modules/**',
      '**/dist/**',
      '**/coverage/**',
      '**/.wrangler/**',
      '**/docs-site/build/**',
      '**/docs-site/.docusaurus/**',
      '**/*.config.js',
      '**/*.config.ts',
      '**/.dependency-cruiser.js',
      '**/*.mdx',
      // 生成文件
      'src/api-types.d.ts',
      'openapi.json',
      'scripts/@/api-types.d.ts',
      'worker-configuration.d.ts',
      // 构建产物
      '*.tsbuildinfo',
      // 锁文件
      'pnpm-lock.yaml',
      'package-lock.json',
      'yarn.lock',
    ],
  },
  // JavaScript 基础规则
  js.configs.recommended,
  // TypeScript + Prettier 规则
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        // 支持根源代码与文档站点（docs-site）共同的 TypeScript 配置，避免"TSConfig 未包含文件"错误
        project: [
          './tsconfig.json',
          './tsconfig.src.json',
          './scripts/tsconfig.scripts.json',
          './tsconfig.tests.json',
        ],
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
      // 允许在 TS 文件中使用 Node、浏览器与 ES2021 全局
      globals: {
        console: 'readonly',
        process: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        fetch: 'readonly',
        crypto: 'readonly',
        // Node.js globals
        __dirname: 'readonly',
        require: 'readonly',
        // React global for new JSX runtime (used in docs-site)
        React: 'readonly',
        // Cloudflare Workers globals
        CloudflareBindings: 'readonly',
        Cloudflare: 'readonly',
        KVNamespace: 'readonly',
        R2Bucket: 'readonly',
        D1Database: 'readonly',
        URLSearchParams: 'readonly',
        URL: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': tsPlugin,
      prettier: prettierPlugin,
      import: importPlugin,
      boundaries: boundariesPlugin,
    },
    settings: {
      // eslint-plugin-boundaries 需要声明 element 类型与路径模式
      'boundaries/elements': [
        { type: 'modules', pattern: 'src/modules/**' },
        { type: 'controllers', pattern: 'src/controllers/**' },
        { type: 'services', pattern: 'src/services/**' },
        { type: 'infrastructure', pattern: 'src/infrastructure/**' },
        { type: 'schemas', pattern: 'src/schemas/**' },
      ],
      'import/resolver': {
        node: {
          extensions: ['.js', '.jsx', '.ts', '.tsx'],
        },
        typescript: {
          project: ['./tsconfig.json'],
          alwaysTryTypes: true,
        },
      },
    },
    rules: {
      ...tsPlugin.configs.recommended.rules,
      // 临时关闭 any 类型检查，便于快速开发
      '@typescript-eslint/no-explicit-any': 'off',
      // 忽略以 _ 开头的未使用变量（常用于测试 stub）
      '@typescript-eslint/no-unused-vars': [
        'error',
        { argsIgnorePattern: '^_', varsIgnorePattern: '^_' },
      ],
      // 将 import 顺序检查降级为警告
      'import/no-unresolved': 'warn',
      'import/order': 'warn',
      'prettier/prettier': 'error',
      // 限制跨模块直接引用 schema。允许：公共 schema 与模块内部相对路径；禁止：其他模块 schema。
      'no-restricted-imports': [
        'error',
        {
          patterns: [
            {
              group: ['@/schemas/*Schema'],
              message: '不应直接引用各模块独立导出的 *Schema 文件名',
            },
            {
              group: ['../schemas/*Schema'],
              message: '不应直接引用各模块独立导出的 *Schema 文件名',
            },
            {
              group: ['@/modules/*/schema'],
              message:
                '不允许跨模块直接引用其他模块的 schema。如需共享类型，请在模块中显式导出。',
            },
            {
              group: ['../modules/*/schema'],
              message:
                '不允许跨模块直接引用其他模块的 schema。如需共享类型，请在模块中显式导出。',
            },
          ],
        },
      ],
      'boundaries/element-types': [
        'error',
        {
          default: 'disallow',
          rules: [
            // ① modules 层
            {
              from: ['modules/**'],
              allow: ['modules', 'schemas', 'infrastructure'],
            },

            // ② controllers 层
            {
              from: ['controllers/**'],
              allow: [
                'controllers',
                'modules',
                'services',
                'schemas',
                'infrastructure',
              ],
            },

            // ③ services 层
            {
              from: ['services/**'],
              allow: ['services', 'infrastructure', 'schemas'],
            },

            // ④ infrastructure 层  ← 新增
            {
              from: ['infrastructure/**'],
              allow: ['modules', 'schemas', 'infrastructure'],
            },
          ],
        },
      ],
    },
  },
  // Node.js 环境下的纯 JavaScript 文件
  {
    files: ['**/*.js', '**/*.cjs', '**/*.mjs'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        console: 'readonly',
        process: 'readonly',
        __dirname: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',
      },
    },
  },
  // docs-site 目录中特定规则调整
  {
    files: ['docs-site/**/*.{ts,tsx,mdx}'],
    languageOptions: {
      // 单独解析 docs-site TypeScript，避免 "parserOptions.project" 未包含文件报错
      parser: tsParser,
      parserOptions: {
        project: ['./docs-site/tsconfig.json'],
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
    },
    rules: {
      // 允许在 Docusaurus 示例中使用 require 导入 SVG
      '@typescript-eslint/no-require-imports': 'off',
      // React 依赖由 Docusaurus 注入，无需显式导入
      'no-undef': 'off',
      'import/no-unresolved': 'off',
    },
  },
  // 测试文件中放宽规则，专注于业务逻辑而非代码规范
  {
    files: [
      'tests/**/*.ts',
      'src/**/*.test.ts',
      'src/**/*.integration.test.ts',
      'src/**/*.spec.ts',
      '**/*.test.ts',
    ],
    rules: {
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'import/order': 'off',
      'no-restricted-imports': 'off',
      'no-undef': 'off',
      'no-control-regex': 'off',
      'no-prototype-builtins': 'off',
    },
  },
  // 脚本文件中允许使用 any
  {
    files: ['scripts/**/*.ts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },
];
