{"extends": "./tsconfig.json", "compilerOptions": {"strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "useUnknownInCatchVariables": true, "alwaysStrict": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noEmit": true, "outDir": "./dist/src", "tsBuildInfoFile": "./dist/src/tsconfig.src.tsbuildinfo", "types": ["./worker-configuration.d.ts", "@types/node"]}, "include": ["src/**/*", "./worker-configuration.d.ts"], "exclude": ["src/**/*.test.ts", "src/**/*.spec.ts"]}