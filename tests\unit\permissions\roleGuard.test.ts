import { describe, it, expect, vi, beforeEach } from 'vitest';
import { roleGuard, type UserRole } from '@/middlewares/roleGuard';
import { jsonError } from '@/utils/errorResponse';

// Mock jsonError
vi.mock('@/utils/errorResponse', () => ({
  jsonError: vi.fn().mockReturnValue({
    status: 401,
    json: async () => ({ code: 20001, message: '未登录' }),
  }),
}));

// Mock context
const createMockContext = (user: { role?: UserRole } | null = null) => ({
  get: vi.fn((key: string) => {
    if (key === 'user') {
      return user;
    }
    return null;
  }),
  set: vi.fn(),
});

// Mock next function
const createMockNext = () => vi.fn().mockResolvedValue(undefined);

describe('roleGuard middleware', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('basic functionality', () => {
    it('should allow access when no role requirement is specified', async () => {
      const guard = roleGuard();
      const mockContext = createMockContext({ role: 'viewer' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(jsonError).not.toHaveBeenCalled();
    });

    it('should reject unauthenticated users', async () => {
      const guard = roleGuard('admin');
      const mockContext = createMockContext(null);
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(mockContext, 20001, '未登录', 401);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject users without role', async () => {
      const guard = roleGuard('admin');
      const mockContext = createMockContext({});
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(mockContext, 20001, '未登录', 401);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('single role requirements', () => {
    it('should allow admin access to admin-only routes', async () => {
      const guard = roleGuard('admin');
      const mockContext = createMockContext({ role: 'admin' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(jsonError).not.toHaveBeenCalled();
    });

    it('should reject editor access to admin-only routes', async () => {
      const guard = roleGuard('admin');
      const mockContext = createMockContext({ role: 'editor' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject viewer access to admin-only routes', async () => {
      const guard = roleGuard('admin');
      const mockContext = createMockContext({ role: 'viewer' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('multiple role requirements', () => {
    it('should allow admin access to admin+editor routes', async () => {
      const guard = roleGuard(['admin', 'editor']);
      const mockContext = createMockContext({ role: 'admin' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(jsonError).not.toHaveBeenCalled();
    });

    it('should allow editor access to admin+editor routes', async () => {
      const guard = roleGuard(['admin', 'editor']);
      const mockContext = createMockContext({ role: 'editor' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(jsonError).not.toHaveBeenCalled();
    });

    it('should reject viewer access to admin+editor routes', async () => {
      const guard = roleGuard(['admin', 'editor']);
      const mockContext = createMockContext({ role: 'viewer' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('admin routes permission matrix', () => {
    const testCases = [
      // Events and Circles: admin + editor can access
      {
        route: 'events',
        allowedRoles: ['admin', 'editor'],
        deniedRoles: ['viewer'],
      },
      {
        route: 'circles',
        allowedRoles: ['admin', 'editor'],
        deniedRoles: ['viewer'],
      },

      // Users, Logs, Stats: only admin can access
      {
        route: 'users',
        allowedRoles: ['admin'],
        deniedRoles: ['editor', 'viewer'],
      },
      {
        route: 'logs',
        allowedRoles: ['admin'],
        deniedRoles: ['editor', 'viewer'],
      },
      {
        route: 'stats',
        allowedRoles: ['admin'],
        deniedRoles: ['editor', 'viewer'],
      },
    ];

    testCases.forEach(({ route, allowedRoles, deniedRoles }) => {
      describe(`/admin/${route} permissions`, () => {
        allowedRoles.forEach((role) => {
          it(`should allow ${role} access to /admin/${route}`, async () => {
            const guard =
              route === 'events' || route === 'circles'
                ? roleGuard(['admin', 'editor'])
                : roleGuard('admin');

            const mockContext = createMockContext({ role: role as UserRole });
            const mockNext = createMockNext();

            await guard(mockContext as any, mockNext);

            expect(mockNext).toHaveBeenCalled();
            expect(jsonError).not.toHaveBeenCalled();
          });
        });

        deniedRoles.forEach((role) => {
          it(`should deny ${role} access to /admin/${route}`, async () => {
            const guard =
              route === 'events' || route === 'circles'
                ? roleGuard(['admin', 'editor'])
                : roleGuard('admin');

            const mockContext = createMockContext({ role: role as UserRole });
            const mockNext = createMockNext();

            await guard(mockContext as any, mockNext);

            expect(jsonError).toHaveBeenCalledWith(
              mockContext,
              20002,
              '权限不足',
              403
            );
            expect(mockNext).not.toHaveBeenCalled();
          });
        });
      });
    });
  });

  describe('edge cases', () => {
    it('should handle invalid role gracefully', async () => {
      const guard = roleGuard('admin');
      const mockContext = createMockContext({ role: 'invalid' as UserRole });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle empty role array', async () => {
      const guard = roleGuard([]);
      const mockContext = createMockContext({ role: 'admin' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle case-sensitive role matching', async () => {
      const guard = roleGuard('admin');
      const mockContext = createMockContext({ role: 'ADMIN' as UserRole });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('real-world scenarios', () => {
    it('should simulate editor trying to access user management', async () => {
      // This simulates the real permission setup for /admin/users/*
      const guard = roleGuard('admin');
      const mockContext = createMockContext({ role: 'editor' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should simulate editor accessing event management', async () => {
      // This simulates the real permission setup for /admin/events/*
      const guard = roleGuard(['admin', 'editor']);
      const mockContext = createMockContext({ role: 'editor' });
      const mockNext = createMockNext();

      await guard(mockContext as any, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(jsonError).not.toHaveBeenCalled();
    });

    it('should simulate viewer trying to access any admin route', async () => {
      const adminOnlyGuard = roleGuard('admin');
      const adminEditorGuard = roleGuard(['admin', 'editor']);
      const mockContext = createMockContext({ role: 'viewer' });
      const mockNext = createMockNext();

      // Test admin-only routes
      await adminOnlyGuard(mockContext as any, mockNext);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );

      vi.clearAllMocks();

      // Test admin+editor routes
      await adminEditorGuard(mockContext as any, mockNext);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        20002,
        '权限不足',
        403
      );

      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
