import { describe, it, expect, vi, beforeEach } from 'vitest';

import {
  toggleBookmark,
  getUserBookmarks,
  getBookmarkStatus,
  getBookmarkStats,
} from '../service';
import { createBookmarkRepository } from '../repository';

// Mock repository
vi.mock('../repository');

// Mock cache
vi.mock('../cache', () => ({
  createBookmarkCache: vi.fn(),
}));

import { createBookmarkCache } from '../cache';

const mockRepository = {
  toggle: vi.fn(),
  getUserBookmarks: vi.fn(),
  getBookmarkStatus: vi.fn(),
  getBookmarkStats: vi.fn(),
};

const mockCache = {
  getBookmarkStats: vi.fn(),
  setBookmarkStats: vi.fn(),
};

const mockDB = {} as any;
const mockKV = {} as any;

describe('Bookmark Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (createBookmarkRepository as any).mockReturnValue(mockRepository);

    // Setup cache mock
    (createBookmarkCache as any).mockReturnValue(mockCache);
  });

  describe('toggleBookmark', () => {
    it('should return isBookmarked true when bookmark is added', async () => {
      mockRepository.toggle.mockResolvedValue(true);

      const result = await toggleBookmark(mockDB, 'user-1', 'circle-1');

      expect(result).toEqual({ isBookmarked: true });
      expect(mockRepository.toggle).toHaveBeenCalledWith('user-1', 'circle-1');
    });

    it('should return isBookmarked false when bookmark is removed', async () => {
      mockRepository.toggle.mockResolvedValue(false);

      const result = await toggleBookmark(mockDB, 'user-1', 'circle-1');

      expect(result).toEqual({ isBookmarked: false });
      expect(mockRepository.toggle).toHaveBeenCalledWith('user-1', 'circle-1');
    });
  });

  describe('getUserBookmarks', () => {
    it('should return paginated bookmark list', async () => {
      const mockResponse = {
        items: [
          {
            id: 'bookmark-1',
            created_at: '2025-01-01T00:00:00Z',
            circle: {
              id: 'circle-1',
              name: '测试工作室',
              category: 'original',
              urls: null,
              created_at: '2025-01-01T00:00:00Z',
              updated_at: '2025-01-01T00:00:00Z',
            },
          },
        ],
        total: 1,
        page: 1,
        pageSize: 20,
        totalPages: 1,
      };

      mockRepository.getUserBookmarks.mockResolvedValue(mockResponse);

      const query = {
        page: 1,
        pageSize: 20,
        search: undefined,
        sortBy: 'created_at' as const,
        sortOrder: 'desc' as const,
      };

      const result = await getUserBookmarks(mockDB, 'user-1', query);

      expect(result).toEqual(mockResponse);
      expect(mockRepository.getUserBookmarks).toHaveBeenCalledWith(
        'user-1',
        query
      );
    });
  });

  describe('getBookmarkStatus', () => {
    it('should return bookmark status when bookmarked', async () => {
      const mockResponse = {
        isBookmarked: true,
        bookmarkId: 'bookmark-1',
        createdAt: '2025-01-01T00:00:00Z',
      };

      mockRepository.getBookmarkStatus.mockResolvedValue(mockResponse);

      const result = await getBookmarkStatus(mockDB, 'user-1', 'circle-1');

      expect(result).toEqual(mockResponse);
      expect(mockRepository.getBookmarkStatus).toHaveBeenCalledWith(
        'user-1',
        'circle-1'
      );
    });

    it('should return not bookmarked status', async () => {
      const mockResponse = {
        isBookmarked: false,
        bookmarkId: null,
        createdAt: null,
      };

      mockRepository.getBookmarkStatus.mockResolvedValue(mockResponse);

      const result = await getBookmarkStatus(mockDB, 'user-1', 'circle-1');

      expect(result).toEqual(mockResponse);
    });
  });

  describe('getBookmarkStats', () => {
    it('should return bookmark statistics', async () => {
      const mockResponse = {
        totalBookmarks: 15,
        recentBookmarks: 3,
        categoryCounts: {
          original: 8,
          derivative: 7,
        },
      };

      mockRepository.getBookmarkStats.mockResolvedValue(mockResponse);

      const result = await getBookmarkStats(mockDB, 'user-1');

      expect(result).toEqual(mockResponse);
      expect(mockRepository.getBookmarkStats).toHaveBeenCalledWith(
        'user-1',
        false
      );
    });

    it('should return empty stats for user with no bookmarks', async () => {
      const mockResponse = {
        totalBookmarks: 0,
        recentBookmarks: 0,
        categoryCounts: {},
      };

      mockRepository.getBookmarkStats.mockResolvedValue(mockResponse);

      const result = await getBookmarkStats(mockDB, 'user-1');

      expect(result).toEqual(mockResponse);
    });

    it('should return stats with bookmarked circle IDs when includeIds is true', async () => {
      const mockResponse = {
        totalBookmarks: 3,
        recentBookmarks: 1,
        categoryCounts: { original: 2, derivative: 1 },
        bookmarkedCircleIds: ['circle-1', 'circle-2', 'circle-3'],
      };

      mockRepository.getBookmarkStats.mockResolvedValue(mockResponse);

      const result = await getBookmarkStats(mockDB, 'user-1', undefined, true);

      expect(result).toEqual(mockResponse);
      expect(mockRepository.getBookmarkStats).toHaveBeenCalledWith(
        'user-1',
        true
      );
      expect(result.bookmarkedCircleIds).toEqual([
        'circle-1',
        'circle-2',
        'circle-3',
      ]);
    });

    it('should not use cache when includeIds is true', async () => {
      const mockResponse = {
        totalBookmarks: 2,
        recentBookmarks: 0,
        categoryCounts: { original: 2 },
        bookmarkedCircleIds: ['circle-1', 'circle-2'],
      };

      mockRepository.getBookmarkStats.mockResolvedValue(mockResponse);

      // 调用两次，第二次应该仍然调用repository（不使用缓存）
      await getBookmarkStats(mockDB, 'user-1', mockKV, true);
      await getBookmarkStats(mockDB, 'user-1', mockKV, true);

      expect(mockRepository.getBookmarkStats).toHaveBeenCalledTimes(2);
      expect(mockRepository.getBookmarkStats).toHaveBeenCalledWith(
        'user-1',
        true
      );
    });

    it('should use cache when includeIds is false', async () => {
      const mockResponse = {
        totalBookmarks: 2,
        recentBookmarks: 0,
        categoryCounts: { original: 2 },
      };

      mockRepository.getBookmarkStats.mockResolvedValue(mockResponse);
      mockCache.getBookmarkStats
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockResponse);

      // 第一次调用
      await getBookmarkStats(mockDB, 'user-1', mockKV, false);
      // 第二次调用应该使用缓存
      const result = await getBookmarkStats(mockDB, 'user-1', mockKV, false);

      expect(mockRepository.getBookmarkStats).toHaveBeenCalledTimes(1);
      expect(mockCache.getBookmarkStats).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockResponse);
    });
  });
});

describe('Bookmark Repository', () => {
  // 这里可以添加更多的 repository 层单元测试
  // 由于 repository 涉及数据库操作，通常会使用 mock DB 或内存数据库进行测试

  it('should be tested with integration tests', () => {
    // Repository 层的详细测试在集成测试中进行
    expect(true).toBe(true);
  });
});
