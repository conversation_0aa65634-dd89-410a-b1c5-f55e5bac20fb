﻿# 测试指南（Testing Guide）

Ayafeed API 使用 **Vitest** 进行单元与集成测试。

## 测试类型

| 目录                  | 目的                            |
| --------------------- | ------------------------------- |
| `tests/unit`          | 纯函数、Service、Util           |
| `tests/integration`   | 路由 + DB（使用 D1 本地数据库） |
| `src/modules/*/tests` | 模块特定测试                    |

## 运行

```bash
pnpm test                # 全量
pnpm test src/modules    # 指定路径
pnpm test --watch        # watch mode
```

## Mock 策略

- **Logger / Cache**：注入内存实现。
- **第三方请求**：使用 `vi.stubGlobal(fetch)`。

## 数据隔离

- 每个测试文件自动迁移并回滚事务，确保数据独立。

## Coverage

```bash
vitest run --coverage
```

阈值配置见 `vitest.config.ts`。

## 常见测试问题和解决方案

### 1. Schema 验证错误

**问题**: ZodError - schema 字段不匹配

**常见原因**:

- 模块间 schema 定义不一致
- 数据库字段与 schema 定义不匹配
- 本地化字段访问错误

**解决方案**:

```typescript
// 确保 schema 定义与数据库字段一致
const EventSchema = z.object({
  id: z.string(),
  name_en: z.string(),
  name_zh: z.string(),
  name_ja: z.string(),
  // 避免内嵌复杂对象，使用关联查询
});

// 正确的本地化字段访问
const localizedName = row[`name_${locale}`];
```

### 2. 模块架构分离问题

**问题**: 模块间职责不清，导致循环依赖

**解决方案**:

- 遵循 Feature-First 架构原则
- 每个模块只负责自己的数据和业务逻辑
- 通过 API 调用而非直接导入进行模块间通信

```typescript
// ❌ 错误：直接在 event 模块中构建 venue 对象
const event = {
  ...eventData,
  venue: await venueRepository.getById(eventData.venue_id),
};

// ✅ 正确：通过 venue 模块的 API 获取
const venue = await venueService.getById(eventData.venue_id);
```

### 3. 国际化测试问题

**问题**: 多语言字段处理错误

**解决方案**:

```typescript
// 测试多语言字段
describe('Event i18n', () => {
  it('should return localized event name', async () => {
    const event = await eventService.getById('event1', 'zh');
    expect(event.name).toBe('中文名称');

    const eventEn = await eventService.getById('event1', 'en');
    expect(eventEn.name).toBe('English Name');
  });
});
```

### 4. 数据库事务问题

**问题**: 测试间数据污染

**解决方案**:

```typescript
// 使用事务确保测试隔离
describe('Event CRUD', () => {
  beforeEach(async () => {
    await db.exec('BEGIN TRANSACTION');
  });

  afterEach(async () => {
    await db.exec('ROLLBACK');
  });
});
```

## 测试最佳实践

### 1. 测试结构

```typescript
describe('Module: Event', () => {
  describe('Service: EventService', () => {
    describe('Method: getById', () => {
      it('should return event when id exists', async () => {
        // Arrange
        const eventId = 'test-event-1';

        // Act
        const result = await eventService.getById(eventId);

        // Assert
        expect(result).toBeDefined();
        expect(result.id).toBe(eventId);
      });
    });
  });
});
```

### 2. Mock 数据管理

```typescript
// 使用工厂函数创建测试数据
const createTestEvent = (overrides = {}) => ({
  id: 'test-event-1',
  name_en: 'Test Event',
  name_zh: '测试活动',
  name_ja: 'テストイベント',
  date_start: '2024-06-01',
  date_end: '2024-06-02',
  ...overrides,
});
```

### 3. 异步测试

```typescript
// 正确处理异步操作
it('should handle async operations', async () => {
  const promise = eventService.create(testData);
  await expect(promise).resolves.toBeDefined();
});
```

## 性能测试

### 1. 数据库查询性能

```typescript
describe('Performance: Event queries', () => {
  it('should complete list query within 100ms', async () => {
    const start = Date.now();
    await eventService.list({ page: 1, pageSize: 50 });
    const duration = Date.now() - start;

    expect(duration).toBeLessThan(100);
  });
});
```

### 2. 内存使用监控

```typescript
// 监控内存泄漏
afterEach(() => {
  if (global.gc) {
    global.gc();
  }
});
```

## 调试技巧

### 1. 测试调试

```bash
# 运行单个测试文件
pnpm test tests/unit/event.test.ts

# 调试模式
pnpm test --inspect-brk

# 详细输出
pnpm test --reporter=verbose
```

### 2. 数据库状态检查

```typescript
// 在测试中检查数据库状态
it('should create event in database', async () => {
  await eventService.create(testData);

  // 直接查询数据库验证
  const dbResult = await db
    .prepare('SELECT * FROM events WHERE id = ?')
    .bind(testData.id)
    .first();

  expect(dbResult).toBeDefined();
});
```

## 持续集成

### 1. CI 配置

```yaml
# .github/workflows/test.yml
- name: Run tests
  run: |
    pnpm test --coverage
    pnpm test:integration
```

### 2. 测试报告

- 覆盖率报告自动生成到 `coverage/` 目录
- 集成测试结果输出到 CI 日志
- 失败测试自动重试机制
