import { Context } from 'hono';

import {
  toggleBookmark,
  getUserBookmarks,
  getBookmarkStatus,
  getBookmarkStats,
  batchBookmarks,
} from './service';
import {
  bookmarkListQuery,
  bookmarkBatchRequest,
  bookmarkStatsQuery,
} from './schema';
import { getDB, getKV } from '@/infrastructure';
import { jsonError, jsonSuccess } from '@/utils/errorResponse';

// 切换收藏状态：POST /circles/:circleId/bookmark
export async function toggleCircleBookmark(c: Context) {
  const user = c.get('user') as { id?: string };
  if (!user?.id) {
    return jsonError(c, 20001, '未登录', 401);
  }

  const circleId = c.req.param('circleId');
  if (!circleId) {
    return jsonError(c, 40001, '缺少 circleId', 400);
  }

  try {
    const db = getDB(c);
    const kv = getKV(c);
    const { isBookmarked } = await toggleBookmark(db, user.id, circleId, kv);

    const message = isBookmarked ? '已加入收藏' : '已取消收藏';
    console.log(`用户 ${user.id} ${message} 社团 ${circleId}`);
    return jsonSuccess(c, message, { isBookmarked });
  } catch (error) {
    console.error('切换收藏状态失败:', {
      userId: user.id,
      circleId,
      error: error instanceof Error ? error.message : error,
    });
    return jsonError(c, 50001, '切换收藏状态失败', 500);
  }
}

// 获取用户收藏列表：GET /user/bookmarks
export async function listUserBookmarks(c: Context) {
  const user = c.get('user') as { id?: string };
  if (!user?.id) {
    return jsonError(c, 20001, '未登录', 401);
  }

  let query;
  try {
    // 解析查询参数
    query = bookmarkListQuery.parse({
      page: c.req.query('page'),
      pageSize: c.req.query('pageSize'),
      search: c.req.query('search'),
      sortBy: c.req.query('sortBy'),
      sortOrder: c.req.query('sortOrder'),
    });

    const db = getDB(c);
    const kv = getKV(c);
    const result = await getUserBookmarks(db, user.id, query, kv);

    console.log(`用户 ${user.id} 获取收藏列表成功，共 ${result.total} 条记录`);
    return jsonSuccess(c, '获取收藏列表成功', result);
  } catch (error) {
    console.error('获取收藏列表失败:', {
      userId: user.id,
      query,
      error: error instanceof Error ? error.message : error,
    });
    return jsonError(c, 50001, '获取收藏列表失败', 500);
  }
}

// 检查收藏状态：GET /circles/:circleId/bookmark/status
export async function checkBookmarkStatus(c: Context) {
  const user = c.get('user') as { id?: string };
  if (!user?.id) {
    return jsonError(c, 20001, '未登录', 401);
  }

  const circleId = c.req.param('circleId');
  if (!circleId) {
    return jsonError(c, 40001, '缺少 circleId', 400);
  }

  try {
    const db = getDB(c);
    const kv = getKV(c);
    const result = await getBookmarkStatus(db, user.id, circleId, kv);

    console.log(
      `用户 ${user.id} 检查社团 ${circleId} 收藏状态: ${result.isBookmarked}`
    );
    return jsonSuccess(c, '获取收藏状态成功', result);
  } catch (error) {
    console.error('获取收藏状态失败:', {
      userId: user.id,
      circleId,
      error: error instanceof Error ? error.message : error,
    });
    return jsonError(c, 50001, '获取收藏状态失败', 500);
  }
}

// 获取收藏统计：GET /user/bookmarks/stats
export async function getUserBookmarkStats(c: Context) {
  const user = c.get('user') as { id?: string };
  if (!user?.id) {
    return jsonError(c, 20001, '未登录', 401);
  }

  try {
    // 解析查询参数
    const query = bookmarkStatsQuery.parse({
      includeIds: c.req.query('includeIds'),
    });

    const db = getDB(c);
    const kv = getKV(c);
    const result = await getBookmarkStats(db, user.id, kv, query.includeIds);

    const message = query.includeIds
      ? `获取收藏统计成功，总计 ${result.totalBookmarks} 个收藏，包含ID列表`
      : `获取收藏统计成功，总计 ${result.totalBookmarks} 个收藏`;

    console.log(`用户 ${user.id} ${message}`);
    return jsonSuccess(c, '获取收藏统计成功', result);
  } catch (error) {
    console.error('获取收藏统计失败:', {
      userId: user.id,
      error: error instanceof Error ? error.message : error,
    });
    return jsonError(c, 50001, '获取收藏统计失败', 500);
  }
}

// 批量操作收藏：POST /user/bookmarks/batch
export async function batchUserBookmarks(c: Context) {
  const user = c.get('user') as { id?: string };
  if (!user?.id) {
    return jsonError(c, 20001, '未登录', 401);
  }

  try {
    // 解析请求体
    const body = await c.req.json();
    const request = bookmarkBatchRequest.parse(body);

    const db = getDB(c);
    const kv = getKV(c);
    const result = await batchBookmarks(db, user.id, request, kv);

    const message =
      request.action === 'add'
        ? `批量收藏操作完成，成功 ${result.successCount} 个，失败 ${result.failedCount} 个`
        : `批量取消收藏操作完成，成功 ${result.successCount} 个，失败 ${result.failedCount} 个`;

    console.log(`用户 ${user.id} ${message}`);
    return jsonSuccess(c, message, result);
  } catch (error) {
    console.error('批量操作收藏失败:', {
      userId: user.id,
      error: error instanceof Error ? error.message : error,
    });
    return jsonError(c, 50001, '批量操作收藏失败', 500);
  }
}
