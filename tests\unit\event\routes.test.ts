import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { Context } from 'hono';
import * as eventService from '@/modules/event/service';
import { getDB } from '@/infrastructure';
import { jsonError, jsonWithFields } from '@/utils/errorResponse';

// Copy the handler functions for unit testing
async function getEventHandler(c: Context) {
  const db = getDB(c);
  const event = await eventService.getEvent(db, c.req.param('id'));
  if (!event) return jsonError(c, 10002, '资源不存在', 404);
  return jsonWithFields(c, event);
}

async function getEventCirclesHandler(c: Context) {
  const db = getDB(c);
  const items = await eventService.listCirclesByEvent(db, c.req.param('id'));
  return c.json(items);
}

async function getEventAppearancesHandler(c: Context) {
  const db = getDB(c);
  const res = await eventService.listAppearances(
    db,
    c.req.param('id'),
    new URL(c.req.url).searchParams
  );
  return c.json(res);
}

// Mock dependencies
vi.mock('@/modules/event/service');
vi.mock('@/infrastructure');
vi.mock('@/utils/errorResponse', () => ({
  jsonError: vi.fn(),
  jsonWithFields: vi.fn(),
}));

// Helper function to create mock context
const createMockContext = (overrides: any = {}) => {
  const mockDB = { prepare: vi.fn() };

  return {
    req: {
      url: 'http://localhost/',
      param: vi.fn(),
      ...overrides.req,
    },
    json: vi.fn((data) => ({
      status: 200,
      json: async () => data,
    })),
    env: { DB: mockDB },
    _mockDB: mockDB,
    ...overrides,
  };
};

describe('event/routes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (getDB as any).mockImplementation((c) => c.env.DB);
    (jsonError as any).mockReturnValue({
      status: 404,
      json: async () => ({ code: 10002, message: '资源不存在' }),
    });
    (jsonWithFields as any).mockReturnValue({
      status: 200,
      json: async () => ({ id: '1', name: 'Test Event' }),
    });
  });

  describe('getEventHandler', () => {
    it('should return event when found', async () => {
      const mockEvent = {
        id: '1',
        name: 'Test Event',
        description: 'Test Description',
        start_date: '2024-01-01T00:00:00Z',
        end_date: '2024-01-02T00:00:00Z',
      };

      (eventService.getEvent as any).mockResolvedValue(mockEvent);

      const mockContext = createMockContext({
        req: { param: vi.fn(() => '1') },
      });

      await getEventHandler(mockContext as any);

      expect(eventService.getEvent).toHaveBeenCalledWith(
        mockContext._mockDB,
        '1'
      );
      expect(jsonWithFields).toHaveBeenCalledWith(mockContext, mockEvent);
    });

    it('should return 404 when event not found', async () => {
      (eventService.getEvent as any).mockResolvedValue(null);

      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'nonexistent') },
      });

      const result = await getEventHandler(mockContext as any);

      expect(result.status).toBe(404);
      expect(jsonError).toHaveBeenCalledWith(
        mockContext,
        10002,
        '资源不存在',
        404
      );
    });
  });

  describe('getEventCirclesHandler', () => {
    it('should return circles for event', async () => {
      const mockCircles = [
        {
          id: 'circle-1',
          name: 'Circle A',
          category: 'original',
          booth_id: 'A01a',
        },
        {
          id: 'circle-2',
          name: 'Circle B',
          category: 'touhou',
          booth_id: 'A01b',
        },
      ];

      (eventService.listCirclesByEvent as any).mockResolvedValue(mockCircles);

      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'event-1') },
      });

      await getEventCirclesHandler(mockContext as any);

      expect(eventService.listCirclesByEvent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'event-1'
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockCircles);
    });

    it('should return empty array when no circles found', async () => {
      (eventService.listCirclesByEvent as any).mockResolvedValue([]);

      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'event-without-circles') },
      });

      await getEventCirclesHandler(mockContext as any);

      expect(eventService.listCirclesByEvent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'event-without-circles'
      );
      expect(mockContext.json).toHaveBeenCalledWith([]);
    });

    it('should handle different event IDs', async () => {
      const mockCircles = [
        {
          id: 'circle-3',
          name: 'Circle C',
          category: 'vocaloid',
          booth_id: 'B02a',
        },
      ];

      (eventService.listCirclesByEvent as any).mockResolvedValue(mockCircles);

      const mockContext = createMockContext({
        req: { param: vi.fn(() => 'special-event-123') },
      });

      await getEventCirclesHandler(mockContext as any);

      expect(eventService.listCirclesByEvent).toHaveBeenCalledWith(
        mockContext._mockDB,
        'special-event-123'
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockCircles);
    });
  });

  describe('getEventAppearancesHandler', () => {
    it('should return appearances for event with default parameters', async () => {
      const mockAppearances = {
        items: [
          {
            id: 'appearance-1',
            circle_id: 'circle-1',
            event_id: 'event-1',
            booth_id: 'A01a',
          },
          {
            id: 'appearance-2',
            circle_id: 'circle-2',
            event_id: 'event-1',
            booth_id: 'A01b',
          },
        ],
        total: 2,
        page: 1,
        pageSize: 50,
      };

      (eventService.listAppearances as any).mockResolvedValue(mockAppearances);

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/events/event-1/appearances',
          param: vi.fn(() => 'event-1'),
        },
      });

      await getEventAppearancesHandler(mockContext as any);

      expect(eventService.listAppearances).toHaveBeenCalledWith(
        mockContext._mockDB,
        'event-1',
        expect.any(URLSearchParams)
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockAppearances);
    });

    it('should return appearances with custom query parameters', async () => {
      const mockAppearances = {
        items: [
          {
            id: 'appearance-3',
            circle_id: 'circle-3',
            event_id: 'event-2',
            booth_id: 'B01a',
          },
        ],
        total: 1,
        page: 2,
        pageSize: 10,
      };

      (eventService.listAppearances as any).mockResolvedValue(mockAppearances);

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/events/event-2/appearances?page=2&pageSize=10&category=original',
          param: vi.fn(() => 'event-2'),
        },
      });

      await getEventAppearancesHandler(mockContext as any);

      expect(eventService.listAppearances).toHaveBeenCalledWith(
        mockContext._mockDB,
        'event-2',
        expect.any(URLSearchParams)
      );

      // Verify that the URLSearchParams contains the expected parameters
      const callArgs = (eventService.listAppearances as any).mock.calls[0];
      const searchParams = callArgs[2];
      expect(searchParams.get('page')).toBe('2');
      expect(searchParams.get('pageSize')).toBe('10');
      expect(searchParams.get('category')).toBe('original');

      expect(mockContext.json).toHaveBeenCalledWith(mockAppearances);
    });

    it('should return empty results when no appearances found', async () => {
      const mockAppearances = {
        items: [],
        total: 0,
        page: 1,
        pageSize: 50,
      };

      (eventService.listAppearances as any).mockResolvedValue(mockAppearances);

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/events/empty-event/appearances',
          param: vi.fn(() => 'empty-event'),
        },
      });

      await getEventAppearancesHandler(mockContext as any);

      expect(eventService.listAppearances).toHaveBeenCalledWith(
        mockContext._mockDB,
        'empty-event',
        expect.any(URLSearchParams)
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockAppearances);
    });

    it('should handle complex query parameters', async () => {
      const mockAppearances = {
        items: [],
        total: 0,
        page: 1,
        pageSize: 20,
      };

      (eventService.listAppearances as any).mockResolvedValue(mockAppearances);

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/events/event-3/appearances?search=test&category=touhou,original&sort=name&order=desc',
          param: vi.fn(() => 'event-3'),
        },
      });

      await getEventAppearancesHandler(mockContext as any);

      expect(eventService.listAppearances).toHaveBeenCalledWith(
        mockContext._mockDB,
        'event-3',
        expect.any(URLSearchParams)
      );

      // Verify that the URLSearchParams contains the expected parameters
      const callArgs = (eventService.listAppearances as any).mock.calls[0];
      const searchParams = callArgs[2];
      expect(searchParams.get('search')).toBe('test');
      expect(searchParams.get('category')).toBe('touhou,original');
      expect(searchParams.get('sort')).toBe('name');
      expect(searchParams.get('order')).toBe('desc');

      expect(mockContext.json).toHaveBeenCalledWith(mockAppearances);
    });

    it('should handle URL without query parameters', async () => {
      const mockAppearances = {
        items: [],
        total: 0,
        page: 1,
        pageSize: 50,
      };

      (eventService.listAppearances as any).mockResolvedValue(mockAppearances);

      const mockContext = createMockContext({
        req: {
          url: 'http://localhost/events/simple-event/appearances',
          param: vi.fn(() => 'simple-event'),
        },
      });

      await getEventAppearancesHandler(mockContext as any);

      expect(eventService.listAppearances).toHaveBeenCalledWith(
        mockContext._mockDB,
        'simple-event',
        expect.any(URLSearchParams)
      );

      // Verify that the URLSearchParams is empty
      const callArgs = (eventService.listAppearances as any).mock.calls[0];
      const searchParams = callArgs[2];
      expect([...searchParams.keys()]).toHaveLength(0);

      expect(mockContext.json).toHaveBeenCalledWith(mockAppearances);
    });
  });
});
