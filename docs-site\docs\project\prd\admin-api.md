# 后台管理系统后端接口需求说明（Admin API PRD）

> 更新时间：2025-07-11

## 1. 项目背景与目标

现有前端 Admin 模块已完成基础 CRUD 功能，临时采用本地 JSON 文件模拟持久化。为满足正式上线需求，需要提供真正的后端接口服务，实现数据持久化、权限校验与统一的错误处理。

## 2. 认证与鉴权

采用 **短效 Access Token + 长效 Refresh Token** 双令牌方案。

| 项目          | 说明                                                                   |
| ------------- | ---------------------------------------------------------------------- |
| Access Token  | JWT（RS256）15 min，放在 `Authorization: Bearer <token>` 请求头        |
| Refresh Token | `refresh_token`，HttpOnly Cookie，14 d，`Path=/; Secure; SameSite=Lax` |

### 2.1 登录流程

1. `POST /auth/login` 提交 `username`、`password`。
2. 成功后：
   - Body 返回三段式结构：
     ```jsonc
     {
       "code": 0,
       "data": {
         "accessToken": "<jwt>",
         "user": { "id": "u1", "username": "alice", "role": "viewer" },
       },
       "message": "登录成功",
     }
     ```
   - `Set-Cookie: refresh_token=...; Max-Age=1209600; Path=/; HttpOnly; Secure; SameSite=Lax`。

### 2.2 令牌刷新

- `POST /auth/refresh`：前端在 `401` 或 Access Token 将到期时调用。成功后返回新的 `accessToken` 并重置 Refresh Token 过期（滑动窗口）。

### 2.3 Token 失效机制

- JWT `payload` 含 `ver` 字段，与数据库 `users.token_ver` 对应。后端在**修改密码 / 封禁账号**等场景递增该字段，使旧 Token 自动失效。

### 2.4 权限校验

- 未携带或 Token 过期 ⇒ **401 Unauthorized**。
- 已登录但 `role` 不符合要求 ⇒ **403 Forbidden**。

## 3. 通用约定

| 项目         | 说明                                                                                    |
| ------------ | --------------------------------------------------------------------------------------- |
| Base URL     | `https://api.example.com`（通过环境变量配置）                                           |
| Content-Type | `application/json; charset=utf-8`                                                       |
| 时间         | 统一使用 ISO-8601 字符串，如 `2025-03-28T12:00:00Z`                                     |
| 分页         | 暂未实现，保留 `page` `pageSize` 参数以备扩展                                           |
| 返回格式     | 成功：`200 / 201` + `{ code: 0, data, message }`<br/>失败：非 2xx + `{ code, message }` |

## 4. 接口清单

### 4.1 Auth（认证）

| Method | Path           | 描述                                             |
| ------ | -------------- | ------------------------------------------------ |
| POST   | /auth/login    | 用户名密码登录，成功后设置 `auth_session` Cookie |
| POST   | /auth/register | 注册新用户（根据需要决定是否对公开放）           |
| POST   | /auth/logout   | 登出，清除 `auth_session` Cookie                 |
| GET    | /auth/me       | 获取当前登录用户信息                             |

### 4.2 Event（展会） - 管理接口

| Method | Path                 | 描述             |
| ------ | -------------------- | ---------------- |
| GET    | /admin/events        | 获取全部展会列表 |
| POST   | /admin/events        | 新增展会         |
| GET    | `/admin/events/{id}` | 获取展会详情     |
| PUT    | `/admin/events/{id}` | 更新展会         |
| DELETE | `/admin/events/{id}` | 删除展会         |

字段定义（与前端保持一致）：

```ts
interface Event {
  id: string; // 主键，由后端生成
  name: string; // 展会名称（必填）
  date: string; // 举办日期（必填，可为自由文本）
  date_sort: string; // 排序用日期 (YYYYMMDD)
  image_url?: string; // 海报图片 URL
  venue_name?: string; // 场馆名称
  venue_address?: string; // 场馆地址
  venue_lat?: number; // 场馆纬度
  venue_lng?: number; // 场馆经度
  url?: string; // 官网地址
  description?: string; // 展会介绍
}
```

#### 请求-响应示例

_GET /admin/events_

```http
GET /admin/events HTTP/1.1
Host: api.example.com
Cookie: auth_session=...
```

```json
200 OK
[
  {
    "id": "reitaisai-16",
    "name": "第十六回博麗神社例大祭",
    "date": "2019年5月5日",
    "date_sort": "20190505",
    "venue_name": "東京ビッグサイト",
    "venue_address": "東京都江東区有明3-11-1",
    "venue_lat": 35.6298,
    "venue_lng": 139.793
  }
]
```

_POST /admin/events_

```jsonc
// Request Body
{
  "name": "第十七回博麗神社例大祭",
  "date": "2020年3月22日",
  "date_sort": "20200322",
  "venue_name": "東京ビッグサイト",
  "venue_address": "東京都江東区有明3-11-1",
  "venue_lat": 35.6298,
  "venue_lng": 139.793,
}
```

```jsonc
// Response 201
{
  "id": "generated-uuid-reitaisai-17",
  "name": "第十七回博麗神社例大祭",
  "date": "2020年3月22日",
  "date_sort": "20200322",
  "venue_name": "東京ビッグサイト",
  "venue_address": "東京都江東区有明3-11-1",
  "venue_lat": 35.6298,
  "venue_lng": 139.793,
}
```

### 4.3 Circle（社团） - 管理接口

| Method | Path                  | 描述             |
| ------ | --------------------- | ---------------- |
| GET    | /admin/circles        | 获取全部社团列表 |
| POST   | /admin/circles        | 新增社团         |
| GET    | `/admin/circles/{id}` | 获取社团详情     |
| PUT    | `/admin/circles/{id}` | 更新社团         |
| DELETE | `/admin/circles/{id}` | 删除社团         |

字段定义：

```ts
interface Circle {
  id: string; // 主键
  name: string; // 社团名称（必填）
  author?: string; // 负责人 / 作者
  twitter_url?: string;
  pixiv_url?: string;
  web_url?: string;
}
```

### 4.4 错误响应示例

| HTTP Status | 示例 Payload                                     |
| ----------- | ------------------------------------------------ |
| 400         | `{ "code": 40001, "message": "参数校验失败" }`   |
| 401         | `{ "code": 10002, "message": "未认证" }`         |
| 403         | `{ "code": 20001, "message": "权限不足" }`       |
| 404         | `{ "code": 30001, "message": "资源不存在" }`     |
| 409         | `{ "code": 30004, "message": "冲突：名称重复" }` |
| 500         | `{ "code": 50000, "message": "服务器内部错误" }` |

## 5. 兼容性与版本控制

- 首版 API 未使用版本号前缀。如需升级可采用 `/v2/events` 路由或增加 `Accept-Version` 头。
- 前端通过 `.env` 切换 Base URL，无需变更代码。

## 6. 后续扩展建议

1. 分页、筛选、排序参数标准化。
2. Upload Service：支持上传展会海报、社团 logo。
3. 操作日志：记录增删改操作及管理员信息。
4. 更细粒度的权限（多角色）。

---

如有疑问或新增字段，请及时沟通确认。
