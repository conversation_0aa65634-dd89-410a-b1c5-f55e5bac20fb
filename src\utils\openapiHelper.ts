import type { OpenAPIHono } from '@hono/zod-openapi';

/**
 * registerOpenApiRoute 是对 `OpenAPIHono#openapi` 的轻量包装，使调用者无需显式指定复杂的泛型参数。
 * 通过利用 `Parameters` 提取 `openapi` 的参数类型，我们能在保持类型安全的同时让 IDE 正确联想，
 * 从而移除原来的 `@ts-expect-error`。
 */
export function registerOpenApiRoute<
  App extends Record<string, unknown>,
  // 利用条件类型提取 `openapi` 方法的参数元组
  T extends Parameters<OpenAPIHono<App>['openapi']> = Parameters<
    OpenAPIHono<App>['openapi']
  >,
>(router: OpenAPIHono<App>, ...args: T) {
  // 通过 `.openapi.bind(router)` 保证 this 上下文正确，同时保持类型签名一致
  return (
    router.openapi.bind(router) as (
      ...a: T
    ) => ReturnType<typeof router.openapi>
  )(...args);
}
