#!/usr/bin/env tsx

/**
 * 生成 Cloudflare D1 迁移文件
 * 将富文本标签页管理系统的迁移脚本转换为 D1 格式
 */

import { readFileSync, writeFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';

const MIGRATION_DIR = 'migrations';
const MIGRATION_FILE = '001_rich_text_tabs_migration.sql';

function generateD1Migration() {
  try {
    console.log('🚀 生成 Cloudflare D1 迁移文件...');

    // 确保迁移目录存在
    mkdirSync(MIGRATION_DIR, { recursive: true });

    // 读取原始迁移脚本
    const migrationPath = join(__dirname, '../db/migrations', MIGRATION_FILE);
    const migrationSQL = readFileSync(migrationPath, 'utf-8');

    // 清理和优化 SQL 用于 D1
    const d1SQL = cleanSQLForD1(migrationSQL);

    // 生成 D1 迁移文件
    const d1MigrationPath = join(MIGRATION_DIR, MIGRATION_FILE);
    writeFileSync(d1MigrationPath, d1SQL, 'utf-8');

    console.log(`✅ D1 迁移文件已生成: ${d1MigrationPath}`);
    console.log('\n📋 下一步操作:');
    console.log('1. 运行: npx wrangler d1 migrations apply ayafeed-db --local');
    console.log(
      '2. 运行: npx wrangler d1 migrations apply ayafeed-db --remote'
    );
    console.log('\n💡 提示: 请确保在 wrangler.toml 中正确配置了数据库绑定');
  } catch (error) {
    console.error('❌ 生成迁移文件失败:', error);
    process.exit(1);
  }
}

function cleanSQLForD1(sql: string): string {
  // 移除不兼容的 PRAGMA 语句
  let cleaned = sql.replace(/PRAGMA foreign_keys=OFF;/g, '');
  cleaned = cleaned.replace(/PRAGMA foreign_keys=ON;/g, '');

  // 移除备份相关的语句（D1 不支持）
  cleaned = cleaned.replace(
    /CREATE TABLE IF NOT EXISTS rich_text_contents_backup[\s\S]*?;/g,
    ''
  );
  cleaned = cleaned.replace(
    /INSERT INTO rich_text_contents_backup[\s\S]*?;/g,
    ''
  );

  // 移除迁移历史表相关语句（使用 D1 内置迁移管理）
  cleaned = cleaned.replace(
    /CREATE TABLE IF NOT EXISTS migration_history[\s\S]*?;/g,
    ''
  );
  cleaned = cleaned.replace(
    /INSERT OR REPLACE INTO migration_history[\s\S]*?;/g,
    ''
  );

  // 移除最后的提示语句
  cleaned = cleaned.replace(/SELECT 'Rich Text Tabs Migration[\s\S]*?;/g, '');

  // 清理多余的空行和注释
  cleaned = cleaned
    .split('\n')
    .filter((line) => {
      const trimmed = line.trim();
      return trimmed.length > 0 && !trimmed.startsWith('-- 迁移完成提示');
    })
    .join('\n');

  // 添加 D1 特定的头部注释
  const header = `-- ========================================
-- 富文本标签页管理系统 - Cloudflare D1 迁移
-- 版本: v3.0.0
-- 生成时间: ${new Date().toISOString()}
-- ========================================

`;

  return header + cleaned;
}

// 生成示例 wrangler.toml 配置
function generateWranglerConfig() {
  const config = `# 在 wrangler.toml 中添加以下配置

[[d1_databases]]
binding = "DB"
database_name = "ayafeed-db"
database_id = "your-database-id"
migrations_dir = "migrations"

# 开发环境配置
[env.development]
[[env.development.d1_databases]]
binding = "DB"
database_name = "ayafeed-db-dev"
database_id = "your-dev-database-id"
migrations_dir = "migrations"
`;

  writeFileSync('wrangler.toml.example', config, 'utf-8');
  console.log('📝 已生成 wrangler.toml.example 配置示例');
}

// 生成 D1 操作脚本
function generateD1Scripts() {
  const scripts = {
    'd1:migrate:local': 'wrangler d1 migrations apply ayafeed-db --local',
    'd1:migrate:remote': 'wrangler d1 migrations apply ayafeed-db --remote',
    'd1:list:local': 'wrangler d1 migrations list ayafeed-db --local',
    'd1:list:remote': 'wrangler d1 migrations list ayafeed-db --remote',
    'd1:execute:local': 'wrangler d1 execute ayafeed-db --local --command',
    'd1:execute:remote': 'wrangler d1 execute ayafeed-db --remote --command',
  };

  console.log('\n📦 建议在 package.json 中添加以下脚本:');
  console.log(JSON.stringify(scripts, null, 2));
}

// 主函数
function main() {
  generateD1Migration();
  generateWranglerConfig();
  generateD1Scripts();
}

if (require.main === module) {
  main();
}
