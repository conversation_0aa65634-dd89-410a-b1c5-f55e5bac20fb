import { describe, it, expect } from 'vitest';

import app from '@/app';

const mockDB = {
  prepare: (query: string) => {
    const upper = query.toUpperCase();
    const buildResp = () => ({
      all: async () => ({
        results: [
          {
            id: '1',
            name_en: 'Event1 EN',
            name_ja: 'イベント1 JA',
            name_zh: '事件1 ZH',
            date_en: 'May 1, 2025',
            date_ja: '2025年5月1日',
            date_zh: '2025年5月1日',
            date_sort: 20250101,
            image_url: null,
            venue_name_en: 'Tokyo',
            venue_name_ja: '東京',
            venue_name_zh: '北京',
            venue_address_en: null,
            venue_address_ja: null,
            venue_address_zh: null,
            venue_lat: 35.0,
            venue_lng: 139.0,
            url: null,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
        ],
      }),
      first: async () => {
        if (upper.includes('COUNT(*)')) return { total: 1 };
        return {
          id: '1',
          name_en: 'Event1 EN',
          name_ja: 'イベント1 JA',
          name_zh: '事件1 ZH',
          date_en: 'May 1, 2025',
          date_ja: '2025年5月1日',
          date_zh: '2025年5月1日',
          date_sort: 20250101,
          image_url: null,
          venue_name_en: 'Tokyo',
          venue_name_ja: '東京',
          venue_name_zh: '北京',
          venue_address_en: null,
          venue_address_ja: null,
          venue_address_zh: null,
          venue_lat: 35.0,
          venue_lng: 139.0,
          url: null,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        };
      },
      run: async () => ({ success: true }),
      bind: () => buildResp(),
    });
    return buildResp();
  },
};

const Request = globalThis.Request;

function withEnv(url: string, env: any) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base), env);
}

describe('Events API', () => {
  it('should return all events', async () => {
    const res = await withEnv('/events', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(data.page).toBe(1);
    expect(data.pageSize).toBe(50);
    expect(data.total).toBe(1);
    expect(Array.isArray(data.items)).toBe(true);
  });

  it('should return event detail', async () => {
    const res = await withEnv('/events/1', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(data.id).toBe('1');
  });

  it('should return 404 for not found event', async () => {
    const res = await withEnv('http://localhost/api/events/not-exist-id', {
      DB: mockDB,
    });
    expect(res.status).toBe(404);
  });

  it('should filter fields when using ?fields=id,name', async () => {
    const res = await withEnv('/events?fields=id,name', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(Array.isArray(data.items)).toBe(true);
    expect(data.page).toBe(1);
    expect(data.total).toBe(1);
    expect(data.items.length).toBeGreaterThan(0);
    expect(Object.keys(data.items[0]).sort()).toEqual(['id', 'name']);
  });

  it('should return 400 when requesting unknown field', async () => {
    const res = await withEnv('/events?fields=unknown', { DB: mockDB });
    expect(res.status).toBe(400);
  });
});
