import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import {
  venueSchema,
  venueCreateRequest,
  venueUpdateRequest,
  venueSearchParams,
} from './schema';
import * as venueService from './service';
import { getDB } from '@/infrastructure';
import type { Logger } from '@/infrastructure';
import {
  paginatedResult,
  successResponse,
  errorResponse,
  ErrorCode,
} from '@/utils/schemas';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { jsonWithFields } from '@/utils/fieldFilter';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const adminVenues = new OpenAPIHono<HonoApp>();

// 管理员API路由定义
const listVenuesRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '场馆列表（管理员）',
  tags: ['Admin.Venues'],
  request: {
    query: venueSearchParams,
  },
  responses: {
    200: {
      description: '场馆列表',
      content: { 'application/json': { schema: paginatedResult(venueSchema) } },
    },
  },
});

const getVenueRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '场馆详情（管理员）',
  tags: ['Admin.Venues'],
  request: {
    params: z.object({ id: z.string() }),
  },
  responses: {
    200: {
      description: '场馆详情',
      content: { 'application/json': { schema: venueSchema } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

const createVenueRoute = createRoute({
  method: 'post',
  path: '/',
  summary: '创建场馆',
  tags: ['Admin.Venues'],
  request: {
    body: {
      content: { 'application/json': { schema: venueCreateRequest } },
    },
  },
  responses: {
    201: {
      description: '创建成功',
      content: { 'application/json': { schema: venueSchema } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

const updateVenueRoute = createRoute({
  method: 'put',
  path: '/{id}',
  summary: '更新场馆',
  tags: ['Admin.Venues'],
  request: {
    params: z.object({ id: z.string() }),
    body: {
      content: { 'application/json': { schema: venueUpdateRequest } },
    },
  },
  responses: {
    200: {
      description: '更新成功',
      content: { 'application/json': { schema: successResponse } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

const deleteVenueRoute = createRoute({
  method: 'delete',
  path: '/{id}',
  summary: '删除场馆',
  tags: ['Admin.Venues'],
  request: {
    params: z.object({ id: z.string() }),
  },
  responses: {
    200: {
      description: '删除成功',
      content: { 'application/json': { schema: successResponse } },
    },
    400: {
      description: '场馆正在使用中，无法删除',
      content: { 'application/json': { schema: errorResponse } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// 路由处理器
registerOpenApiRoute(adminVenues, listVenuesRoute, async (c: Context) => {
  const db = getDB(c);
  const logger = c.get('logger') as Logger;

  // 从URL搜索参数中获取查询参数
  const searchParams = new URL(c.req.url).searchParams;
  const query = {
    page: searchParams.get('page') || undefined,
    pageSize: searchParams.get('pageSize') || undefined,
    keyword: searchParams.get('keyword') || undefined,
    city: searchParams.get('city') || undefined,
    capacity_min: searchParams.get('capacity_min') || undefined,
    capacity_max: searchParams.get('capacity_max') || undefined,
    has_parking: searchParams.get('has_parking') || undefined,
    has_wifi: searchParams.get('has_wifi') || undefined,
  };

  try {
    // 管理员API返回完整的多语言数据
    const result = await venueService.listVenuesForAdmin(db, query);
    return jsonWithFields(c, result);
  } catch (error) {
    logger.error('Failed to list venues (admin)', { error, query });
    return jsonError(
      c,
      ErrorCode.RESOURCE_NOT_FOUND,
      'Failed to fetch venues',
      500
    );
  }
});

registerOpenApiRoute(adminVenues, getVenueRoute, async (c: Context) => {
  const db = getDB(c);
  const logger = c.get('logger') as Logger;

  const id = c.req.param('id');

  try {
    const venue = await venueService.getVenueByIdForAdmin(db, id);
    if (!venue) {
      return jsonError(c, ErrorCode.RESOURCE_NOT_FOUND, 'Venue not found', 404);
    }
    return jsonWithFields(c, venue);
  } catch (error) {
    logger.error('Failed to get venue (admin)', { error, id });
    return jsonError(
      c,
      ErrorCode.RESOURCE_NOT_FOUND,
      'Failed to fetch venue',
      500
    );
  }
});

registerOpenApiRoute(adminVenues, createVenueRoute, async (c: Context) => {
  const db = getDB(c);
  const logger = c.get('logger') as Logger;

  let rawData: any;

  try {
    // 获取并验证请求数据
    rawData = await c.req.json();
    const data = venueCreateRequest.parse(rawData);

    // 生成venue ID
    const id = await venueService.generateVenueId(db, data.name_en);

    const venue = await venueService.createVenue(db, { ...data, id });
    return c.json(venue, 201);
  } catch (error) {
    logger.error('Failed to create venue', {
      error,
      requestBody: rawData || 'Failed to parse JSON',
    });

    // 区分验证错误和其他错误
    if (error instanceof Error && error.name === 'ZodError') {
      return jsonError(
        c,
        ErrorCode.PARAM_MISSING_OR_INVALID,
        `Validation failed: ${error.message}`,
        400
      );
    }

    return jsonError(
      c,
      ErrorCode.PARAM_MISSING_OR_INVALID,
      'Failed to create venue',
      500
    );
  }
});

registerOpenApiRoute(adminVenues, updateVenueRoute, async (c: Context) => {
  const db = getDB(c);
  const logger = c.get('logger') as Logger;

  const id = c.req.param('id');
  let rawData: any;

  try {
    // 获取并验证请求数据
    rawData = await c.req.json();
    const data = venueUpdateRequest.parse(rawData);

    // 检查venue是否存在
    const existingVenue = await venueService.getVenueByIdForAdmin(db, id);
    if (!existingVenue) {
      return jsonError(c, ErrorCode.RESOURCE_NOT_FOUND, 'Venue not found', 404);
    }

    await venueService.updateVenue(db, id, data);
    return c.json({ success: true, message: 'Venue updated successfully' });
  } catch (error) {
    logger.error('Failed to update venue', {
      error,
      id,
      requestBody: rawData || 'Failed to parse JSON',
    });

    // 区分验证错误和其他错误
    if (error instanceof Error && error.name === 'ZodError') {
      return jsonError(
        c,
        ErrorCode.PARAM_MISSING_OR_INVALID,
        `Validation failed: ${error.message}`,
        400
      );
    }

    return jsonError(
      c,
      ErrorCode.PARAM_MISSING_OR_INVALID,
      'Failed to update venue',
      500
    );
  }
});

registerOpenApiRoute(adminVenues, deleteVenueRoute, async (c: Context) => {
  const db = getDB(c);
  const logger = c.get('logger') as Logger;

  const id = c.req.param('id');

  try {
    // 检查venue是否存在
    const existingVenue = await venueService.getVenueByIdForAdmin(db, id);
    if (!existingVenue) {
      return jsonError(c, ErrorCode.RESOURCE_NOT_FOUND, 'Venue not found', 404);
    }

    // 检查是否被events使用
    const inUse = await venueService.isVenueInUse(db, id);
    if (inUse) {
      return jsonError(
        c,
        ErrorCode.PARAM_MISSING_OR_INVALID,
        'Cannot delete venue: it is being used by events',
        400
      );
    }

    await venueService.deleteVenue(db, id);
    return c.json({ success: true, message: 'Venue deleted successfully' });
  } catch (error) {
    logger.error('Failed to delete venue', { error, id });
    return jsonError(
      c,
      ErrorCode.RESOURCE_NOT_FOUND,
      'Failed to delete venue',
      500
    );
  }
});

export { adminVenues };
