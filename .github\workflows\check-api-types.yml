name: Check API Types

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]

jobs:
  check-api-types:
    name: Check API Types
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate API types
        run: pnpm run gen:api

      - name: Check for changes
        id: git-check
        run: |
          if [[ -n "$(git status --porcelain)" ]]; then
            echo "检测到以下文件有变更："
            git status --porcelain
            echo "请运行 'pnpm run gen:api' 并提交更新后的文件"
            exit 1
          fi
