import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import * as imageController from './controller';
import { imageDeleteRequest } from './schema';
import { successResponse, errorResponse } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const routes = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI 定义 ----------

// 图片上传接口
const uploadImageRoute = createRoute({
  method: 'post',
  path: '/upload',
  summary: '上传图片',
  tags: ['Admin.Images'],
  request: {
    body: {
      content: {
        'multipart/form-data': {
          schema: z.object({
            file: z.any().openapi({
              type: 'string',
              format: 'binary',
              description: '图片文件',
            }),
            category: z.enum(['event', 'circle', 'venue']).openapi({
              example: 'event',
              description: '图片分类',
            }),
            resourceId: z.string().openapi({
              example: 'resource-uuid-789',
              description: '关联的资源ID',
            }),
            imageType: z.enum(['poster', 'logo', 'banner', 'gallery']).openapi({
              example: 'poster',
              description: '图片类型',
            }),
            variant: z.enum(['original', 'large', 'medium', 'thumb']).openapi({
              example: 'thumb',
              description: '图片变体',
            }),
            groupId: z.string().optional().openapi({
              example: 'group-uuid-456',
              description: '关联同一组图片的标识，可选',
            }),
          }),
        },
      },
    },
  },
  responses: {
    201: {
      description: '图片上传成功',
      content: {
        'application/json': { schema: successResponse },
      },
    },
    400: {
      description: '请求参数错误',
      content: {
        'application/json': { schema: errorResponse },
      },
    },
  },
});

// 图片删除接口
const deleteImagesRoute = createRoute({
  method: 'delete',
  path: '/',
  summary: '删除图片',
  tags: ['Admin.Images'],
  request: {
    body: {
      content: {
        'application/json': { schema: imageDeleteRequest },
      },
    },
  },
  responses: {
    200: {
      description: '图片删除完成',
      content: {
        'application/json': { schema: successResponse },
      },
    },
    400: {
      description: '请求参数错误',
      content: {
        'application/json': { schema: errorResponse },
      },
    },
  },
});

// 获取图片信息（管理员版本，包含更多详细信息）
const getImageRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '获取图片详细信息',
  tags: ['Admin.Images'],
  request: {
    params: z.object({
      id: z.string().openapi({
        example: 'uuid-123',
        description: '图片ID',
      }),
    }),
  },
  responses: {
    200: {
      description: '图片信息',
      content: {
        'application/json': { schema: successResponse },
      },
    },
    404: {
      description: '图片不存在',
      content: {
        'application/json': { schema: errorResponse },
      },
    },
  },
});

// 注册路由
registerOpenApiRoute(routes, uploadImageRoute, imageController.uploadImage);
registerOpenApiRoute(routes, deleteImagesRoute, imageController.deleteImages);
registerOpenApiRoute(routes, getImageRoute, imageController.getImageById);

export { routes };
