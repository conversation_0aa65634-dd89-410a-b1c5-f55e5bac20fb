import { describe, it, expect } from 'vitest';

import app from '@/app';

// 构造 mock D1 数据库
const mockDB = {
  prepare: (query: string) => {
    const upper = query.toUpperCase();
    const buildResp = () => ({
      all: async () => ({
        results: [
          {
            event_id: 'e1',
            event_name: 'Event1',
            event_date: '2025-05-01',
            booth_id: 'A01a',
          },
        ],
      }),
      first: async () => {
        if (upper.includes('COUNT(*)')) return { total: 1 };
        return { id: 'e1', name: 'Event1' };
      },
      run: async () => ({ success: true }),
      bind: () => buildResp(),
    });
    return buildResp();
  },
};

// @ts-ignore
const Request = globalThis.Request;

function withEnv(url: string, env: any) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base), env);
}

describe('Circle Appearances API', () => {
  it('should return appearances for given circle', async () => {
    const res = await withEnv('/circles/1/appearances', { DB: mockDB });
    expect(res.status).toBe(200);
    const data = (await res.json()) as any;
    expect(Array.isArray(data.items)).toBe(true);
    expect(data.total).toBe(1);
    expect(data.page).toBe(1);
    expect(data.items[0].event_id).toBe('e1');
    expect(data.items[0].booth_id).toBe('A01a');
  });
});
