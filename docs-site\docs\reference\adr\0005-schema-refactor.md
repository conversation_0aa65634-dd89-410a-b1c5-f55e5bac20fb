# ADR-0005: Schema 文件重构和模块化组织

## 状态

已接受 (Accepted) - 2024-01-30

## 背景

在项目发展过程中，发现 schema 文件组织存在以下问题：

1. **重复定义** - `src/schemas/` 目录下存在独立的 schema 文件，与模块内的 schema 重复
2. **未注册 OpenAPI** - 独立 schema 没有正确注册到 OpenAPI 系统
3. **不符合最佳实践** - 违反了模块化架构原则
4. **维护困难** - 多处定义相同的 schema，容易产生不一致

### 具体重复文件

- `src/schemas/common.ts` - 通用 schema（需要保留但移动位置）
- `src/schemas/event.ts` - 与 `src/modules/event/schema.ts` 重复
- `src/schemas/venue.ts` - 与 `src/modules/venue/schema.ts` 重复

## 决策

### 1. 重新组织通用 Schema

**移动路径：** `src/schemas/common.ts` → `src/utils/schemas.ts`

**理由：**

- 通用 schema 属于工具类，放在 utils 目录更合适
- 与其他工具函数保持一致的组织结构
- 便于统一管理和维护

### 2. 删除重复的模块 Schema

删除以下重复文件：

- `src/schemas/event.ts`
- `src/schemas/venue.ts`

**理由：**

- 遵循 Feature-First 架构原则
- 每个模块的 schema 应该在模块内部定义
- 避免重复定义和维护成本

### 3. 更新导入路径

将所有对 `src/schemas/common.ts` 的引用更新为 `src/utils/schemas.ts`

**影响的文件：**

- `src/modules/*/schema.ts`
- `src/modules/*/controller.ts`
- 其他相关文件

### 4. 确保 OpenAPI 注册

确保所有 schema 都通过模块的路由正确注册到 OpenAPI 系统。

## 实施步骤

### 第一步：移动通用 Schema

```bash
mv src/schemas/common.ts src/utils/schemas.ts
```

### 第二步：更新导入路径

```typescript
// 从
import { PaginationSchema } from '../schemas/common';

// 改为
import { PaginationSchema } from '../utils/schemas';
```

### 第三步：删除重复文件

```bash
rm src/schemas/event.ts
rm src/schemas/venue.ts
rmdir src/schemas  # 如果目录为空
```

### 第四步：验证 OpenAPI 生成

```bash
pnpm gen:api
```

## 后果

### 正面影响

1. **架构一致性** - 符合 Feature-First 架构原则
2. **维护简化** - 消除重复定义，单一数据源
3. **OpenAPI 完整性** - 所有 schema 正确注册
4. **代码清晰度** - 明确的模块边界和职责

### 负面影响

1. **短期工作量** - 需要更新多个文件的导入路径
2. **潜在风险** - 移动文件可能导致临时的构建错误

### 风险缓解

1. **分步实施** - 逐步移动和更新，确保每步都能正常构建
2. **测试验证** - 每步完成后运行测试确保功能正常
3. **文档更新** - 及时更新相关文档和注释

## 验证标准

- [ ] 所有测试通过
- [ ] OpenAPI 规范正确生成
- [ ] 没有重复的 schema 定义
- [ ] 导入路径全部更新
- [ ] 构建无错误和警告

## 相关决策

- [ADR-0001: 项目初始化](./0001-init.md) - 确立了 Feature-First 架构原则
- [ADR-0003: 国际化事件](./0003-i18n-events.md) - 涉及事件 schema 的设计

## 参考资料

- [Feature-First 架构指南](../development/architecture-guidelines.md)
- [Schema 设计最佳实践](../development/schema-guidelines.md)
- [OpenAPI 集成文档](../api/openapi.md)
