﻿# 安装与启动（Installation）

## 1. 克隆仓库

```bash
git clone https://github.com/ayafeed/ayafeed-api.git
cd ayafeed-api
```

## 2. 安装依赖

```bash
pnpm install
```

## 3. 初始化配置

```bash
cp wrangler.example.jsonc wrangler.jsonc
# 编辑 account_id、数据库绑定等字段
```

## 4. 创建数据库（首次）

```bash
# 创建 DEV 数据库
wrangler d1 create ayafeed-dev
# 推送 schema 与 seed
wrangler d1 execute ayafeed-dev --file=db/schema.sql
wrangler d1 execute ayafeed-dev --file=db/seeds/000_base.sql
wrangler d1 execute ayafeed-dev --file=db/seeds/001_admin.sql
```

## 5. 本地开发

```bash
pnpm dev    # http://localhost:8787
```

## 6. 生成 OpenAPI 与类型

```bash
pnpm gen:api  # 输出 docs-site/static/openapi.json & src/api-types.d.ts
```

## 7. 运行测试

```bash
# Vitest + Hono
pnpm test
```

## 8. 构建 & 部署

```bash
pnpm deploy        # Wrangler 打包并推送
```

---

> 如遇问题请先查阅 [FAQ](./faq.md)。
