import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import { searchResponseSchema } from './schema';
import * as searchService from './service';
import { getDB } from '@/infrastructure';
import type { <PERSON><PERSON>, Logger } from '@/infrastructure';
import type { Locale } from '@/middlewares/locale';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const pubSearch = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI ----------
const searchRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '搜索内容',
  tags: ['Search'],
  request: {
    query: z.object({
      q: z.string().openapi({
        description: '搜索关键词',
        example: 'Comiket',
      }),
      type: z.enum(['all', 'events', 'circles']).optional().openapi({
        description: '搜索类型',
        example: 'events',
        default: 'all',
      }),
      page: z.string().optional().openapi({
        description: '页码（暂未实现分页）',
        example: '1',
      }),
      limit: z.string().optional().openapi({
        description: '每页数量（暂未实现分页）',
        example: '20',
      }),
    }),
  },
  responses: {
    200: {
      description: '搜索结果',
      content: {
        'application/json': { schema: searchResponseSchema },
      },
    },
    400: {
      description: '请求参数错误',
    },
  },
});

// ---------- Handlers ----------
async function searchHandler(c: Context<HonoApp>) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');
  const locale = (c.get('locale') as Locale) || 'en';

  const searchParams = new URL(c.req.url).searchParams;
  const query = searchParams.get('q');
  const type =
    (searchParams.get('type') as 'all' | 'events' | 'circles') || 'all';

  if (!query || query.trim().length === 0) {
    return c.json({
      success: true,
      data: [],
      locale,
      timestamp: new Date().toISOString(),
      meta: {
        total: 0,
        query: '',
        type,
      },
    });
  }

  try {
    const results = await searchService.searchContent(
      db,
      query,
      type,
      locale,
      cache,
      logger
    );

    return c.json({
      success: true,
      data: results,
      locale,
      timestamp: new Date().toISOString(),
      meta: {
        total: results.length,
        query: query.trim(),
        type,
      },
    });
  } catch (error) {
    logger?.error?.('searchHandler error', { error, query, type, locale });
    return jsonError(c, 50001, 'Search failed', 500);
  }
}

// ---------- Register ----------
registerOpenApiRoute(pubSearch, searchRoute, searchHandler);

export { pubSearch, pubSearch as routes };
