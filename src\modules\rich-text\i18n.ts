import type { LanguageCode, EntityType } from './schema';

/**
 * 多语言支持工具类
 * 提供标签页配置的多语言管理功能
 */
export class RichTextI18nManager {
  // ========================================
  // 预设标签页多语言配置
  // ========================================

  /**
   * 获取预设标签页的多语言配置
   */
  static getPresetConfigs(entityType: EntityType): PresetConfigSet {
    return PRESET_CONFIGS[entityType];
  }

  /**
   * 获取特定语言的预设配置
   */
  static getPresetConfigsForLanguage(
    entityType: EntityType,
    languageCode: LanguageCode
  ): PresetConfig[] {
    const presetSet = this.getPresetConfigs(entityType);
    return presetSet[languageCode] || [];
  }

  /**
   * 验证预设配置的完整性
   */
  static validatePresetConfigs(entityType: EntityType): ValidationResult {
    const errors: string[] = [];
    const presetSet = this.getPresetConfigs(entityType);
    const languages: LanguageCode[] = ['en', 'zh', 'ja'];

    // 检查每种语言是否都有配置
    for (const lang of languages) {
      const configs = presetSet[lang];
      if (!configs || configs.length === 0) {
        errors.push(`${entityType} 实体缺少 ${lang} 语言的预设配置`);
        continue;
      }

      // 检查配置的键值是否一致
      const keys = configs.map((c) => c.key).sort();
      const baseKeys = presetSet.en?.map((c) => c.key).sort() || [];

      if (JSON.stringify(keys) !== JSON.stringify(baseKeys)) {
        errors.push(`${entityType} 实体的 ${lang} 语言配置键值与英语不一致`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // ========================================
  // 语言同步功能
  // ========================================

  /**
   * 同步配置到其他语言
   * 当创建新的配置时，自动为其他语言创建对应配置
   */
  static generateCrossLanguageConfigs(baseConfig: {
    entity_type: EntityType;
    language_code: LanguageCode;
    key: string;
    icon?: string;
    sort_order: number;
    is_active: boolean;
  }): CrossLanguageConfig[] {
    const results: CrossLanguageConfig[] = [];
    const languages: LanguageCode[] = ['en', 'zh', 'ja'];

    for (const lang of languages) {
      if (lang === baseConfig.language_code) {
        continue; // 跳过原始语言
      }

      const translatedLabel = this.translateLabel(
        baseConfig.key,
        baseConfig.entity_type,
        lang
      );

      const translatedPlaceholder = this.translatePlaceholder(
        baseConfig.key,
        baseConfig.entity_type,
        lang
      );

      results.push({
        entity_type: baseConfig.entity_type,
        language_code: lang,
        key: baseConfig.key,
        label: translatedLabel,
        placeholder: translatedPlaceholder,
        icon: baseConfig.icon,
        sort_order: baseConfig.sort_order,
        is_active: baseConfig.is_active,
        is_preset: false, // 用户创建的配置不是预设
      });
    }

    return results;
  }

  /**
   * 翻译标签名称
   */
  private static translateLabel(
    key: string,
    entityType: EntityType,
    targetLanguage: LanguageCode
  ): string {
    const translationKey = `${entityType}.${key}`;
    const translations = LABEL_TRANSLATIONS[translationKey];

    if (translations && translations[targetLanguage]) {
      return translations[targetLanguage];
    }

    // 回退到通用翻译
    const commonTranslations = COMMON_LABEL_TRANSLATIONS[key];
    if (commonTranslations && commonTranslations[targetLanguage]) {
      return commonTranslations[targetLanguage];
    }

    // 最后回退：首字母大写的 key
    return key.charAt(0).toUpperCase() + key.slice(1);
  }

  /**
   * 翻译占位符文本
   */
  private static translatePlaceholder(
    key: string,
    entityType: EntityType,
    targetLanguage: LanguageCode
  ): string {
    const translationKey = `${entityType}.${key}`;
    const translations = PLACEHOLDER_TRANSLATIONS[translationKey];

    if (translations && translations[targetLanguage]) {
      return translations[targetLanguage];
    }

    // 回退到通用翻译
    const commonTranslations = COMMON_PLACEHOLDER_TRANSLATIONS[key];
    if (commonTranslations && commonTranslations[targetLanguage]) {
      return commonTranslations[targetLanguage];
    }

    // 最后回退：根据语言生成默认占位符
    const entityName = ENTITY_NAMES[entityType][targetLanguage];
    const actionText = ACTION_TEXTS.enter[targetLanguage];

    return `${actionText}${entityName}${key}...`;
  }

  // ========================================
  // 语言检测和验证
  // ========================================

  /**
   * 检测文本的主要语言
   */
  static detectLanguage(text: string): LanguageCode {
    // 简单的语言检测逻辑
    const chineseRegex = /[\u4e00-\u9fff]/;
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;

    if (chineseRegex.test(text)) {
      return 'zh';
    }

    if (japaneseRegex.test(text)) {
      return 'ja';
    }

    return 'en'; // 默认英语
  }

  /**
   * 验证文本是否适合指定语言
   */
  static validateTextForLanguage(
    text: string,
    languageCode: LanguageCode
  ): boolean {
    const detectedLanguage = this.detectLanguage(text);

    // 英语可以包含其他语言字符（国际化内容）
    if (languageCode === 'en') {
      return true;
    }

    // 其他语言应该匹配检测结果
    return detectedLanguage === languageCode;
  }
}

// ========================================
// 类型定义
// ========================================

interface PresetConfig {
  key: string;
  label: string;
  placeholder: string;
  icon: string;
  sort_order: number;
}

interface PresetConfigSet {
  en: PresetConfig[];
  zh: PresetConfig[];
  ja: PresetConfig[];
}

interface CrossLanguageConfig {
  entity_type: EntityType;
  language_code: LanguageCode;
  key: string;
  label: string;
  placeholder: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  is_preset: boolean;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// ========================================
// 翻译数据
// ========================================

const PRESET_CONFIGS: Record<EntityType, PresetConfigSet> = {
  event: {
    en: [
      {
        key: 'introduction',
        label: 'Introduction',
        placeholder: 'Enter event introduction...',
        icon: 'info',
        sort_order: 0,
      },
    ],
    zh: [
      {
        key: 'introduction',
        label: '介绍',
        placeholder: '输入活动介绍...',
        icon: 'info',
        sort_order: 0,
      },
    ],
    ja: [
      {
        key: 'introduction',
        label: '紹介',
        placeholder: 'イベント紹介を入力...',
        icon: 'info',
        sort_order: 0,
      },
    ],
  },
  venue: {
    en: [
      {
        key: 'overview',
        label: 'Overview',
        placeholder: 'Enter venue overview...',
        icon: 'map-pin',
        sort_order: 0,
      },
    ],
    zh: [
      {
        key: 'overview',
        label: '概览',
        placeholder: '输入场馆概览...',
        icon: 'map-pin',
        sort_order: 0,
      },
    ],
    ja: [
      {
        key: 'overview',
        label: '概要',
        placeholder: '会場概要を入力...',
        icon: 'map-pin',
        sort_order: 0,
      },
    ],
  },
};

const LABEL_TRANSLATIONS: Record<string, Record<LanguageCode, string>> = {
  'event.details': {
    en: 'Details',
    zh: '详情',
    ja: '詳細',
  },
  'event.schedule': {
    en: 'Schedule',
    zh: '日程',
    ja: 'スケジュール',
  },
  'venue.facilities': {
    en: 'Facilities',
    zh: '设施',
    ja: '施設',
  },
  'venue.access': {
    en: 'Access',
    zh: '交通',
    ja: 'アクセス',
  },
};

const COMMON_LABEL_TRANSLATIONS: Record<
  string,
  Record<LanguageCode, string>
> = {
  description: {
    en: 'Description',
    zh: '描述',
    ja: '説明',
  },
  notes: {
    en: 'Notes',
    zh: '备注',
    ja: '備考',
  },
  contact: {
    en: 'Contact',
    zh: '联系方式',
    ja: '連絡先',
  },
};

const PLACEHOLDER_TRANSLATIONS: Record<string, Record<LanguageCode, string>> = {
  'event.details': {
    en: 'Enter event details...',
    zh: '输入活动详情...',
    ja: 'イベント詳細を入力...',
  },
  'venue.facilities': {
    en: 'Enter venue facilities information...',
    zh: '输入场馆设施信息...',
    ja: '会場施設情報を入力...',
  },
};

const COMMON_PLACEHOLDER_TRANSLATIONS: Record<
  string,
  Record<LanguageCode, string>
> = {
  description: {
    en: 'Enter description...',
    zh: '输入描述...',
    ja: '説明を入力...',
  },
  notes: {
    en: 'Enter notes...',
    zh: '输入备注...',
    ja: '備考を入力...',
  },
};

const ENTITY_NAMES: Record<EntityType, Record<LanguageCode, string>> = {
  event: {
    en: 'event ',
    zh: '活动',
    ja: 'イベント',
  },
  venue: {
    en: 'venue ',
    zh: '场馆',
    ja: '会場',
  },
};

const ACTION_TEXTS = {
  enter: {
    en: 'Enter ',
    zh: '输入',
    ja: '',
  },
};
