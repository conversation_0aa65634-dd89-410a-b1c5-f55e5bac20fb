import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';

import {
  toggleCircleBookmark,
  listUserBookmarks,
  checkBookmarkStatus,
  getUserBookmarkStats,
  batchUserBookmarks,
} from './controller';
import {
  bookmarkListQuery,
  bookmarkListResponse,
  bookmarkStatusResponse,
  bookmarkStatsResponse,
  bookmarkStatsQuery,
  bookmarkBatchRequest,
  bookmarkBatchResponse,
} from './schema';
import { roleGuard } from '@/middlewares/roleGuard';
import { successResponse, errorResponse } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const circleBookmarkRoutes = new OpenAPIHono<HonoApp>();
const userBookmarkRoutes = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI ----------
const toggleBookmarkRoute = createRoute({
  method: 'post',
  path: '/{circleId}/bookmark',
  summary: '切换收藏状态',
  tags: ['Bookmarks'],
  request: {
    params: z.object({
      circleId: z.string().openapi({ example: 'circle-uuid' }),
    }),
  },
  responses: {
    200: {
      description: '收藏状态已切换',
      content: {
        'application/json': {
          schema: successResponse.extend({
            data: z.object({ isBookmarked: z.boolean() }),
          }),
        },
      },
    },
    401: {
      description: '未登录',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// 获取用户收藏列表
const listBookmarksRoute = createRoute({
  method: 'get',
  path: '/user/bookmarks',
  summary: '获取用户收藏列表',
  tags: ['Bookmarks'],
  request: {
    query: bookmarkListQuery,
  },
  responses: {
    200: {
      description: '获取收藏列表成功',
      content: {
        'application/json': {
          schema: successResponse.extend({
            data: bookmarkListResponse,
          }),
        },
      },
    },
    401: {
      description: '未登录',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// 检查收藏状态
const checkBookmarkStatusRoute = createRoute({
  method: 'get',
  path: '/{circleId}/bookmark/status',
  summary: '检查收藏状态',
  tags: ['Bookmarks'],
  request: {
    params: z.object({
      circleId: z.string().openapi({ example: 'circle-uuid' }),
    }),
  },
  responses: {
    200: {
      description: '获取收藏状态成功',
      content: {
        'application/json': {
          schema: successResponse.extend({
            data: bookmarkStatusResponse,
          }),
        },
      },
    },
    401: {
      description: '未登录',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// 获取收藏统计
const getBookmarkStatsRoute = createRoute({
  method: 'get',
  path: '/user/bookmarks/stats',
  summary: '获取收藏统计',
  description:
    '获取用户收藏统计信息，可选择包含收藏的社团ID列表用于前端批量状态检查优化',
  tags: ['Bookmarks'],
  request: {
    query: bookmarkStatsQuery,
  },
  responses: {
    200: {
      description: '获取收藏统计成功',
      content: {
        'application/json': {
          schema: successResponse.extend({
            data: bookmarkStatsResponse,
          }),
        },
      },
    },
    401: {
      description: '未登录',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// 批量操作收藏
const batchBookmarksRoute = createRoute({
  method: 'post',
  path: '/user/bookmarks/batch',
  summary: '批量操作收藏',
  tags: ['Bookmarks'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: bookmarkBatchRequest,
        },
      },
    },
  },
  responses: {
    200: {
      description: '批量操作成功',
      content: {
        'application/json': {
          schema: successResponse.extend({
            data: bookmarkBatchResponse,
          }),
        },
      },
    },
    401: {
      description: '未登录',
      content: { 'application/json': { schema: errorResponse } },
    },
    400: {
      description: '请求参数错误',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

// 为所有路由添加认证中间件
circleBookmarkRoutes.use('/*', roleGuard());
userBookmarkRoutes.use('/user/*', roleGuard());

// 注册circle相关的bookmark路由
registerOpenApiRoute(
  circleBookmarkRoutes,
  toggleBookmarkRoute,
  toggleCircleBookmark
);
registerOpenApiRoute(
  circleBookmarkRoutes,
  checkBookmarkStatusRoute,
  checkBookmarkStatus
);

// 注册用户相关的bookmark路由
registerOpenApiRoute(userBookmarkRoutes, listBookmarksRoute, listUserBookmarks);
registerOpenApiRoute(
  userBookmarkRoutes,
  getBookmarkStatsRoute,
  getUserBookmarkStats
);
registerOpenApiRoute(
  userBookmarkRoutes,
  batchBookmarksRoute,
  batchUserBookmarks
);

export { circleBookmarkRoutes, userBookmarkRoutes };
