// D1Database 类型现在在全局作用域中可用

import { D1StatsRepository } from '@/infrastructure/db/statsRepository';

export interface StatsTotals {
  circles: number;
  artists: number;
  events: number;
}

export interface EventsByMonth {
  month: string; // '01' ~ '12'
  count: number;
}

export interface StatsResult {
  totals: StatsTotals;
  year: number;
  eventsByMonth: EventsByMonth[];
}

export interface StatsRepository {
  getStats(year?: number): Promise<StatsResult>;
}

/**
 * 工厂：返回指定实现
 */
export function createStatsRepository(db: D1Database): StatsRepository {
  return new D1StatsRepository(db);
}
