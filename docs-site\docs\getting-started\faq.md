﻿# FAQ

> 本节收集安装与运行过程中常见问题。

### Q1: `wrangler: command not found`

请先执行 `pnpm i -g wrangler` 或使用 `corepack enable` / `corepack prepare`。

### Q2: 数据库执行 `schema.sql` 报错 `unknown column`

确保使用最新 `db/schema.sql`，并已在对应数据库中删除旧表或执行迁移脚本 `db/migrations/*`。

### Q3: `ECONNREFUSED 8787` 启动失败

端口被占用，可通过 `pnpm dev --port 8788` 指定新端口，或释放占用进程。

### Q4: `TypeError [ERR_MODULE_NOT_FOUND]` 运行单测

使用 Vitest ESM，需要 Node 20+ 并确保 `tsconfig.json` 中 `module` 为 `ESNext`。

### Q5: 生成 OpenAPI 后 `git diff` 失败

脚本会自动写入 `package.json.version`。如不想提交 version 变更，可手动回退或在 CI 中使用临时 commit。

若未找到答案，可在 Discussions 新建话题或 Issue。
