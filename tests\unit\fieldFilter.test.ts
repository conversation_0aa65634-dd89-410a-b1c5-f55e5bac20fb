import { describe, it, expect, vi } from 'vitest';

import { applyFieldFilter } from '@/utils/fieldFilter';

type FakeCtx = {
  req: { query: (key: string) => string | undefined };
  json: (obj: unknown) => unknown;
  header: (name: string, value: string) => void;
};

describe('applyFieldFilter', () => {
  const createCtx = (fields?: string): FakeCtx => {
    return {
      req: {
        query: (key: string) => (key === 'fields' ? fields : undefined),
      },
      json: (obj: unknown) => obj,
      header: vi.fn(),
    };
  };

  it('should return original data when no fields parameter', () => {
    const ctx = createCtx(undefined);
    const data = [{ id: 1, name: 'foo', age: 18 }];
    const res = applyFieldFilter(ctx as any, data);
    expect(res).toEqual(data);
  });

  it('should filter array of objects', () => {
    const ctx = createCtx('id,name');
    const data = [
      { id: 1, name: '<PERSON>', age: 20 },
      { id: 2, name: '<PERSON>', age: 25 },
    ];
    const res = applyFieldFilter(ctx as any, data) as Array<
      Record<string, unknown>
    >;
    expect(res).toEqual([
      { id: 1, name: 'Alice' },
      { id: 2, name: 'Bob' },
    ]);
  });

  it('should filter paginated items only', () => {
    const ctx = createCtx('id');
    const paginated = {
      items: [
        { id: 'a', name: 'Alice' },
        { id: 'b', name: 'Bob' },
      ],
      total: 2,
      page: 1,
      pageSize: 10,
    };
    const res = applyFieldFilter(ctx as any, paginated) as typeof paginated;
    expect(res.items).toEqual([{ id: 'a' }, { id: 'b' }]);
    // meta should stay untouched
    expect(res.total).toBe(2);
  });

  it('should return error when fields parameter is empty', () => {
    const ctx = createCtx('');
    const data = { id: 1, name: 'foo' };
    const res: any = applyFieldFilter(ctx as any, data);
    expect(res.code).toBe(30001);
    expect(res.message).toMatch(/cannot be empty/);
  });

  it('should return error when unknown field requested', () => {
    const ctx = createCtx('unknown');
    const data = { id: 1, name: 'foo' };
    const res: any = applyFieldFilter(ctx as any, data);
    expect(res.code).toBe(30002);
    expect(res.message).toMatch(/Unknown field/);
  });

  it('should return error when unknown field requested in paginated data', () => {
    const ctx = createCtx('unknown');
    const paginated = {
      items: [{ id: 'x', name: 'xx' }],
      total: 1,
      page: 1,
      pageSize: 10,
    };
    const res: any = applyFieldFilter(ctx as any, paginated);
    expect(res.code).toBe(30002);
  });
});
