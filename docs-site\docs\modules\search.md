# 搜索模块 (Search Module)

## 概述

搜索模块提供多语言搜索功能，支持对事件(events)和社团(circles)进行全文搜索。

## 功能特性

- ✅ 多语言搜索支持 (en, zh, ja)
- ✅ 事件和社团混合搜索
- ✅ 相关性评分排序
- ✅ 多语言缓存隔离
- ✅ 搜索结果限制和性能优化

## API端点

### GET /search

**功能**: 多语言搜索事件和社团

**查询参数**:

- `q` (必需) - 搜索关键词
- `type` (可选) - 搜索类型：`all`、`events`、`circles`，默认 `all`
- `page` (可选) - 页码，默认 `1` (暂未实现分页)
- `limit` (可选) - 每页数量，默认 `20` (暂未实现分页)

**请求头**:

- `X-Locale` (推荐) - 指定搜索语言
- `Accept-Language` - 标准语言头部
- `Cookie: locale=zh` - 语言偏好设置

**响应格式**:

```json
{
  "success": true,
  "data": [
    {
      "type": "event",
      "id": "event-123",
      "name": "Comiket 103",
      "description": "世界最大的同人志即卖会",
      "venue_name": "东京国际展示场",
      "start_date": "2024-12-30T10:00:00Z",
      "image_url": "https://example.com/comiket103.jpg",
      "rank": 0.8567
    },
    {
      "type": "circle",
      "id": "circle-456",
      "name": "某某工作室",
      "description": "专注于原创漫画创作",
      "venue_name": null,
      "start_date": null,
      "image_url": null,
      "rank": 0.7234
    }
  ],
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "meta": {
    "total": 15,
    "query": "Comiket",
    "type": "all"
  }
}
```

## 搜索算法

### 相关性评分

搜索结果按相关性评分排序：

**事件搜索**:

- 名称匹配: 1.0分
- 场馆名称匹配: 0.6分
- 其他匹配: 0.4分

**社团搜索**:

- 名称匹配: 1.0分
- 描述匹配: 0.8分
- 其他匹配: 0.4分

### 结果限制

- 每种类型最多返回25条结果
- 总结果数限制在50条以内
- 按相关性评分降序排列

## 缓存策略

### 缓存键格式

```
search:{locale}:{type}:{query}
```

### 示例

```
search:zh:events:Comiket
search:en:all:anime
search:ja:circles:同人
```

### 缓存时间

- TTL: 5分钟
- 自动失效: 相关数据更新时

## 使用示例

### JavaScript/TypeScript

```typescript
// 基础搜索
const response = await fetch('/api/search?q=Comiket&type=events', {
  headers: { 'X-Locale': 'zh' },
});
const data = await response.json();

// 使用openapi-typescript-fetch
import { createClient } from '@/lib/api/client';
const api = createClient();

const { data: results } = await api.GET('/search', {
  params: {
    query: { q: 'anime', type: 'all' },
  },
  headers: { 'X-Locale': 'ja' },
});
```

### cURL

```bash
# 搜索事件
curl -H "X-Locale: zh" \
  "https://api.example.com/search?q=Comiket&type=events"

# 搜索所有内容
curl -H "Accept-Language: ja-JP,ja;q=0.9" \
  "https://api.example.com/search?q=同人"
```

## 性能优化

### 数据库优化

- 使用LIKE查询进行模糊匹配
- 限制查询结果数量
- 为多语言字段建立索引

### 缓存优化

- 语言隔离缓存，避免语言切换时的缓存失效
- 5分钟TTL平衡实时性和性能
- 缓存键包含查询参数，提高命中率

## 限制和注意事项

1. **分页功能**: 当前版本暂未实现真正的分页，page和limit参数被忽略
2. **搜索精度**: 使用简单的LIKE匹配，未来可考虑全文搜索引擎
3. **语言支持**: 仅支持en、zh、ja三种语言
4. **结果数量**: 为保证性能，限制最大返回50条结果

## 未来规划

- 🔄 实现真正的分页功能
- 🔄 引入全文搜索引擎(如Elasticsearch)
- 🔄 支持更多搜索过滤条件
- 🔄 优化搜索相关性算法
- 🔄 添加搜索建议和自动补全功能

## 相关文档

- [API规范](../api/request-spec.md#51-搜索api)
- [i18n指南](../development/i18n.md)
- [性能指南](../development/performance.md)
- [Feed模块](./feed.md)
