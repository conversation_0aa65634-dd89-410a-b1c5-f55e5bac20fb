import { describe, it, expect, vi } from 'vitest';

import app from '@/app';

// 全局 Mock，避免 bcryptjs / uuid 对集成测试产生影响
vi.mock('bcryptjs', () => ({
  default: {
    hash: async () => 'hashed_pwd',
  },
}));

vi.mock('uuid', () => ({ v4: () => 'u1' }));

// @ts-ignore
const Request = globalThis.Request;

function createMockDB(role: 'admin' | 'viewer' = 'admin') {
  return {
    // 批量执行，直接返回成功
    batch: async (_stmts: any[]) => ({ success: true }),
    prepare: (query: string) => {
      const upper = query.toUpperCase();
      const buildResponse = () => ({
        all: async () => {
          if (upper.includes('FROM SESSION') && upper.includes('JOIN USER')) {
            return {
              results: [
                {
                  session_id: 'session-123',
                  user_id: 'u1',
                  expires_at: Math.floor(Date.now() / 1000) + 3600,
                  id: 'u1',
                  email: '<EMAIL>',
                  name: 'Tester',
                  username: 'tester',
                  role,
                },
              ],
            };
          }
          return { results: [] };
        },
        first: async () => {
          if (upper.includes('SELECT ID') && upper.includes('FROM USER')) {
            return { id: 'u1', username: 'alice', role: role };
          }
          if (upper.includes('FROM SESSION') && upper.includes('JOIN USER')) {
            return {
              session_id: 'session-123',
              user_id: 'u1',
              expires_at: Math.floor(Date.now() / 1000) + 3600,
              id: 'u1',
              email: '<EMAIL>',
              name: 'Tester',
              username: 'tester',
              role,
            };
          }
          return null;
        },
        run: async () => ({ success: true }),
      });
      return {
        ...buildResponse(),
        bind: (..._args: any[]) => buildResponse(),
      };
    },
  };
}

function fetchWithEnv(req: Request, env: any) {
  return app.fetch(req, env);
}

describe('jsonSuccess success message & header', () => {
  it('should return message field and X-Success-Message header', async () => {
    const body = { username: 'alice', password: 'password123' };
    const req = new Request('http://localhost/admin/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer session-123',
      },
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB('admin') });
    expect(res.status).toBe(201);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('用户创建成功')
    );
    const data = (await res.json()) as any;
    expect(data.code).toBe(0);
    expect(data.message).toBe('用户创建成功');
  });
});
