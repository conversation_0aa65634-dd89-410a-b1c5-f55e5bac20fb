import { describe, it, expect } from 'vitest';

import app from '@/app';

// @ts-ignore
const Request = globalThis.Request;

function withEnv(url: string, init?: RequestInit, env: any = {}) {
  const base = url.startsWith('http') ? url : `http://localhost${url}`;
  return app.fetch(new Request(base, init), env);
}

describe('Rate Limiting', () => {
  it('should block after exceeding auth limit', async () => {
    const url = '/auth/register';
    for (let i = 0; i < 5; i++) {
      const res = await withEnv(url);
      // 前 5 次应通过（返回 404，因为路由不存在，但不应429）
      expect(res.status).not.toBe(429);
    }

    const blocked = await withEnv(url);
    expect(blocked.status).toBe(429);
  });
});
