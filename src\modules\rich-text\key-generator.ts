/**
 * 智能 Key 生成器
 * 为富文本标签页配置生成语义化且唯一的 key
 */

import type { EntityType, LanguageCode } from './schema';

// 常见词汇映射表（中文 -> 英文）
const LABEL_TO_KEY_MAP: Record<string, string> = {
  // 通用
  介绍: 'introduction',
  概览: 'overview',
  详情: 'details',
  说明: 'description',

  // 活动相关
  活动介绍: 'introduction',
  活动详情: 'details',
  活动说明: 'description',
  日程: 'schedule',
  时间表: 'schedule',
  议程: 'agenda',
  流程: 'process',
  安排: 'arrangement',
  通知: 'notice',
  公告: 'announcement',
  须知: 'guidelines',
  注意事项: 'precautions',
  规则: 'rules',
  联系方式: 'contact',
  联系: 'contact',
  地址: 'address',
  位置: 'location',
  交通: 'transportation',
  路线: 'directions',
  费用: 'pricing',
  价格: 'pricing',
  门票: 'tickets',
  报名: 'registration',
  参与: 'participation',
  嘉宾: 'guests',
  演讲者: 'speakers',
  主办方: 'organizers',
  赞助商: 'sponsors',
  合作伙伴: 'partners',
  媒体: 'media',
  新闻: 'news',
  更新: 'updates',
  常见问题: 'faq',
  FAQ: 'faq',
  帮助: 'help',
  支持: 'support',

  // 场馆相关
  场馆介绍: 'overview',
  场馆概览: 'overview',
  设施: 'facilities',
  服务: 'services',
  容量: 'capacity',
  楼层: 'floors',
  区域: 'areas',
  房间: 'rooms',
  会议室: 'meeting_rooms',
  展厅: 'exhibition_halls',
  停车: 'parking',
  餐饮: 'dining',
  住宿: 'accommodation',
  周边: 'nearby',
  环境: 'environment',
  历史: 'history',
  特色: 'features',

  // 英文直接映射
  introduction: 'introduction',
  overview: 'overview',
  details: 'details',
  description: 'description',
  schedule: 'schedule',
  agenda: 'agenda',
  notice: 'notice',
  contact: 'contact',
  location: 'location',
  pricing: 'pricing',
  faq: 'faq',
  facilities: 'facilities',
  services: 'services',
};

/**
 * 将中文标签转换为英文 key
 */
function labelToKey(label: string): string {
  // 1. 直接映射
  const directMatch = LABEL_TO_KEY_MAP[label.toLowerCase().trim()];
  if (directMatch) {
    return directMatch;
  }

  // 2. 部分匹配
  for (const [chineseLabel, englishKey] of Object.entries(LABEL_TO_KEY_MAP)) {
    if (label.includes(chineseLabel)) {
      return englishKey;
    }
  }

  // 3. 拼音转换（简化版）
  return pinyinConvert(label);
}

/**
 * 简化的拼音转换
 * 实际项目中可以使用 pinyin 库
 */
function pinyinConvert(text: string): string {
  // 简化的字符映射
  const pinyinMap: Record<string, string> = {
    通知: 'tongzhi',
    公告: 'gonggao',
    活动: 'huodong',
    会议: 'huiyi',
    展览: 'zhanlan',
    演出: 'yanchu',
    比赛: 'bisai',
    培训: 'peixun',
    讲座: 'jiangzuo',
    研讨: 'yantao',
    论坛: 'luntan',
    峰会: 'fenghui',
    发布: 'fabu',
    启动: 'qidong',
    开幕: 'kaimu',
    闭幕: 'bimu',
    颁奖: 'banjiang',
    庆典: 'qingdian',
    仪式: 'yishi',
    典礼: 'dianli',
  };

  // 尝试映射
  for (const [chinese, pinyin] of Object.entries(pinyinMap)) {
    if (text.includes(chinese)) {
      return pinyin;
    }
  }

  // 如果都没有匹配，使用简化处理
  return text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '') // 移除特殊字符
    .slice(0, 20) // 限制长度
    .replace(/\s+/g, '_'); // 空格转下划线
}

/**
 * 强制后端生成唯一的 key - 实体关联 + UUID 方案
 */
export async function generateUniqueKey(
  label: string,
  entityType: EntityType,
  languageCode: LanguageCode,
  existingKeys: string[],
  _customKey?: string, // 保留参数兼容性，但不使用
  entityId?: string
): Promise<string> {
  // 使用实体关联 + UUID 方案强制生成
  const timestamp = Date.now().toString(36).slice(-6);
  const randomId = Math.random().toString(36).slice(2, 8);

  let baseKey: string;
  if (entityId) {
    // 提取实体 ID 的前8位作为标识
    const entityIdShort = entityId.replace(/-/g, '').slice(0, 8);
    baseKey = `${entityType}_${entityIdShort}_${randomId}`;
  } else {
    baseKey = `${entityType}_${timestamp}_${randomId}`;
  }

  // 确保生成的 key 不与现有的冲突（理论上不会冲突，但保险起见）
  let finalKey = baseKey;
  let counter = 1;

  while (existingKeys.includes(finalKey)) {
    finalKey = `${baseKey}_${counter}`;
    counter++;

    // 防止无限循环
    if (counter > 999) {
      finalKey = `${baseKey}_${Date.now()}`;
      break;
    }
  }

  return finalKey;
}

/**
 * 生成基于实体的 key（最佳实践方案）
 * 格式：{entityType}_{entityIdShort}_{uuid}
 */
function generateEntityBasedKey(
  entityType: EntityType,
  entityId?: string
): string {
  // 生成短 UUID（8位）
  const shortUuid = generateShortUuid();

  if (entityId) {
    // 提取实体 ID 的前8位作为标识
    const entityIdShort = entityId.replace(/-/g, '').slice(0, 8);
    return `${entityType}_${entityIdShort}_${shortUuid}`;
  } else {
    // 如果没有实体 ID，使用时间戳
    const timestamp = Date.now().toString(36).slice(-6);
    return `${entityType}_${timestamp}_${shortUuid}`;
  }
}

/**
 * 生成短 UUID（8位字符）
 */
function generateShortUuid(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成语义化 key（备选方案）
 * 仅在用户明确要求语义化时使用
 */
export async function generateSemanticKey(
  label: string,
  entityType: EntityType,
  languageCode: LanguageCode,
  existingKeys: string[],
  customKey?: string
): Promise<string> {
  // 1. 如果提供了自定义 key，验证并返回
  if (customKey) {
    const normalizedKey = customKey.toLowerCase().trim();

    // 验证格式
    if (!/^[a-zA-Z0-9_-]+$/.test(normalizedKey)) {
      throw new Error('自定义 key 只能包含字母、数字、下划线、连字符');
    }

    // 检查长度
    if (normalizedKey.length < 2 || normalizedKey.length > 50) {
      throw new Error('自定义 key 长度必须在 2-50 个字符之间');
    }

    // 检查唯一性
    if (existingKeys.includes(normalizedKey)) {
      throw new Error(`Key "${normalizedKey}" 已存在`);
    }

    return normalizedKey;
  }

  // 2. 自动生成语义化 key
  let baseKey = labelToKey(label);

  // 确保基础 key 符合格式要求
  baseKey = baseKey
    .toLowerCase()
    .replace(/[^a-zA-Z0-9_-]/g, '_') // 替换无效字符
    .replace(/_{2,}/g, '_') // 合并多个下划线
    .replace(/^_+|_+$/g, '') // 移除首尾下划线
    .slice(0, 40); // 预留空间给后缀

  // 如果为空，使用默认值
  if (!baseKey) {
    baseKey = 'custom_tab';
  }

  // 3. 检查唯一性，如果重复则添加后缀
  let finalKey = baseKey;
  let counter = 1;

  while (existingKeys.includes(finalKey)) {
    finalKey = `${baseKey}_${counter}`;
    counter++;

    // 防止无限循环
    if (counter > 999) {
      finalKey = `${baseKey}_${Date.now()}`;
      break;
    }
  }

  return finalKey;
}

/**
 * 验证 key 的有效性
 */
export function validateKey(key: string): { isValid: boolean; error?: string } {
  if (!key) {
    return { isValid: false, error: 'Key 不能为空' };
  }

  if (key.length < 2) {
    return { isValid: false, error: 'Key 长度至少 2 个字符' };
  }

  if (key.length > 50) {
    return { isValid: false, error: 'Key 长度最多 50 个字符' };
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
    return { isValid: false, error: 'Key 只能包含字母、数字、下划线、连字符' };
  }

  return { isValid: true };
}

/**
 * 建议的 key 列表
 */
export function suggestKeys(label: string, entityType: EntityType): string[] {
  const baseKey = labelToKey(label);
  const suggestions: string[] = [baseKey];

  // 根据实体类型添加建议
  if (entityType === 'event') {
    const eventSuggestions = [
      'introduction',
      'schedule',
      'agenda',
      'speakers',
      'location',
      'registration',
      'pricing',
      'contact',
      'faq',
      'updates',
    ];
    suggestions.push(...eventSuggestions.filter((s) => s !== baseKey));
  } else if (entityType === 'venue') {
    const venueSuggestions = [
      'overview',
      'facilities',
      'services',
      'location',
      'capacity',
      'parking',
      'dining',
      'contact',
      'directions',
      'features',
    ];
    suggestions.push(...venueSuggestions.filter((s) => s !== baseKey));
  }

  return [...new Set(suggestions)].slice(0, 10); // 去重并限制数量
}
