import bcrypt from 'bcryptjs';
import { v4 as uuid } from 'uuid';

/**
 * 用法：
 *   pnpm ts-node scripts/seed_admin.ts <username> <password>
 *   # 如果未传递参数，默认用户名 admin，密码 admin123
 *
 * 输出：INSERT INTO ... 语句，可直接重定向到 SQL 文件，或通过 wrangler d1 execute 执行。
 */

async function main() {
  const [, , usernameArg, passwordArg] = process.argv;
  const username = usernameArg || 'ayaya';
  const password = passwordArg || 'ayapw';

  const id = uuid();
  const passwordHash = await bcrypt.hash(password, 10);

  const sql = `INSERT INTO users (id, username, password_hash, role) VALUES (\n  '${id}',\n  '${username}',\n  '${passwordHash}',\n  'admin'\n);\n`;

  console.log(sql);
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
