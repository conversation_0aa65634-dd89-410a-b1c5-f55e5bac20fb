// KVNamespace 类型现在在全局作用域中可用
import type { MiddlewareHandler } from 'hono';

import {
  ConsoleLogger,
  D1Logger,
  KVCache,
  MemoryCache,
} from '../infrastructure';

/**
 * 依赖注入中间件：logger & cache
 */
export const injectDependencies: MiddlewareHandler = async (c, next) => {
  // Logger: 若已注入则跳过
  if (!c.get('logger')) {
    const logger = c.env?.DB ? new D1Logger(c) : new ConsoleLogger();
    c.set('logger', logger);
  }

  // Cache: 优先使用 KV，否则使用 MemoryCache
  if (!c.get('cache')) {
    const kv = (c.env as { CACHE?: KVNamespace } | undefined)?.CACHE;
    const cache = kv ? new KVCache(kv) : new MemoryCache();
    c.set('cache', cache);
  }

  await next();
};
