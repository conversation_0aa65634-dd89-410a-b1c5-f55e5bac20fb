import type { D1Database } from '@cloudflare/workers-types';

import type { Log } from './schema';
import { D1LogRepository } from '@/infrastructure/db/logRepository';

// ---------- 类型定义 ----------
export interface QueryOptions {
  page: number;
  pageSize: number;
  user_id?: string | null;
  action?: string | null;
  target_type?: string | null;
}

export interface PaginatedLogs {
  items: Log[];
  total: number;
  page: number;
  pageSize: number;
}

export interface LogRepository {
  /** 查询日志 */
  query(options: QueryOptions): Promise<PaginatedLogs>;
}

// ---------- 工厂函数 ----------
export function createLogRepository(db: D1Database): LogRepository {
  return new D1LogRepository(db);
}
