import { describe, it, expect, vi, beforeEach } from 'vitest';
import type { D1Database } from '@cloudflare/workers-types';
import type { Cache, Logger } from '@/infrastructure';
import { searchContent } from '@/modules/search/service';

// Mock D1 Database
const createMockD1Database = () => {
  const mockPreparedStatement = {
    bind: vi.fn().mockReturnThis(),
    all: vi.fn(),
  };

  return {
    prepare: vi.fn().mockReturnValue(mockPreparedStatement),
    _mockPreparedStatement: mockPreparedStatement,
  } as unknown as D1Database & { _mockPreparedStatement: any };
};

// Mock Cache
const createMockCache = () => ({
  get: vi.fn(),
  set: vi.fn(),
});

// Mock Logger
const createMockLogger = () => ({
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
});

describe('search/service', () => {
  describe('searchContent', () => {
    let mockDb: D1Database & { _mockPreparedStatement: any };
    let mockCache: Cache;
    let mockLogger: Logger;

    beforeEach(() => {
      mockDb = createMockD1Database();
      mockCache = createMockCache();
      mockLogger = createMockLogger();
      vi.clearAllMocks();
    });

    it('should return empty array for empty query', async () => {
      const result = await searchContent(mockDb, '', 'all', 'en');

      expect(result).toEqual([]);
      expect(mockDb.prepare).not.toHaveBeenCalled();
    });

    it('should return empty array for whitespace-only query', async () => {
      const result = await searchContent(mockDb, '   ', 'all', 'en');

      expect(result).toEqual([]);
      expect(mockDb.prepare).not.toHaveBeenCalled();
    });

    it('should return cached results when cache hit', async () => {
      const cachedResults: any[] = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      (mockCache.get as any).mockResolvedValue(cachedResults);

      const result = await searchContent(
        mockDb,
        'test',
        'all',
        'en',
        mockCache,
        mockLogger
      );

      expect(result).toEqual(cachedResults);
      expect(mockCache.get).toHaveBeenCalledWith('search:en:all:test');
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'searchContent: hit cache',
        {
          key: 'search:en:all:test',
        }
      );
      expect(mockDb.prepare).not.toHaveBeenCalled();
    });

    it('should search events when type is "events"', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      const result = await searchContent(mockDb, 'test', 'events', 'en');

      expect(result).toEqual(mockEventResults);
      expect(mockDb.prepare).toHaveBeenCalledTimes(1);
      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        '%test%',
        '%test%',
        '%test%',
        '%test%'
      );
    });

    it('should search circles when type is "circles"', async () => {
      const mockCircleResults = [
        {
          type: 'circle',
          id: '1',
          name: 'Test Circle',
          description: 'Test Description',
          venue_name: null,
          start_date: null,
          image_url: null,
          rank: 1.0,
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockCircleResults,
      });

      const result = await searchContent(mockDb, 'test', 'circles', 'zh');

      expect(result).toEqual(mockCircleResults);
      expect(mockDb.prepare).toHaveBeenCalledTimes(1);

      // Verify that the query uses the correct locale
      const prepareCall = mockDb.prepare.mock.calls[0][0];
      expect(prepareCall).toContain('name_zh');
      expect(prepareCall).toContain('description_zh');
    });

    it('should search both events and circles when type is "all"', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      const mockCircleResults = [
        {
          type: 'circle',
          id: '1',
          name: 'Test Circle',
          description: 'Test Description',
          venue_name: null,
          start_date: null,
          image_url: null,
          rank: 0.8,
        },
      ];

      // Mock multiple calls to all() method
      mockDb._mockPreparedStatement.all
        .mockResolvedValueOnce({ results: mockEventResults })
        .mockResolvedValueOnce({ results: mockCircleResults });

      const result = await searchContent(mockDb, 'test', 'all', 'en');

      expect(result).toHaveLength(2);
      // Should be sorted by rank DESC (event first with rank 1.0, then circle with rank 0.8)
      expect(result[0].type).toBe('event');
      expect(result[0].rank).toBe(1.0);
      expect(result[1].type).toBe('circle');
      expect(result[1].rank).toBe(0.8);
      expect(mockDb.prepare).toHaveBeenCalledTimes(2);
    });

    it('should trim query string', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      await searchContent(mockDb, '  test query  ', 'events', 'en');

      expect(mockDb._mockPreparedStatement.bind).toHaveBeenCalledWith(
        '%test query%',
        '%test query%',
        '%test query%',
        '%test query%'
      );
    });

    it('should sort results by rank in descending order', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Low Rank Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 0.4,
        },
        {
          type: 'event',
          id: '2',
          name: 'High Rank Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      const result = await searchContent(mockDb, 'test', 'events', 'en');

      expect(result[0].rank).toBe(1.0);
      expect(result[1].rank).toBe(0.4);
    });

    it('should limit results to 50 items', async () => {
      // Create 60 mock results
      const mockEventResults = Array.from({ length: 60 }, (_, i) => ({
        type: 'event' as const,
        id: `${i + 1}`,
        name: `Test Event ${i + 1}`,
        description: null,
        venue_name: 'Test Venue',
        start_date: '2024-01-01T00:00:00Z',
        image_url: 'test.jpg',
        rank: 1.0 - i * 0.01, // Decreasing rank
      }));

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      const result = await searchContent(mockDb, 'test', 'events', 'en');

      expect(result).toHaveLength(50);
    });

    it('should cache results when cache is provided', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      (mockCache.get as any).mockResolvedValue(null); // Cache miss
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      const result = await searchContent(
        mockDb,
        'test',
        'events',
        'en',
        mockCache,
        mockLogger
      );

      expect(mockCache.set).toHaveBeenCalledWith(
        'search:en:events:test',
        result,
        300
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'searchContent: cached results',
        {
          key: 'search:en:events:test',
          count: 1,
        }
      );
    });

    it('should work without cache and logger', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      const result = await searchContent(mockDb, 'test', 'events', 'en');

      expect(result).toEqual(mockEventResults);
    });

    it('should handle different locales in queries', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      await searchContent(mockDb, 'Test', 'events', 'ja');

      // Verify that the query uses the correct locale
      const prepareCall = mockDb.prepare.mock.calls[0][0];
      expect(prepareCall).toContain('name_ja');
      expect(prepareCall).toContain('venue_name_ja');
      expect(prepareCall).toContain('date_ja');
    });

    it('should handle empty search results', async () => {
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: [],
      });

      const result = await searchContent(mockDb, 'nonexistent', 'all', 'en');

      expect(result).toEqual([]);
    });

    it('should generate correct cache key with special characters', async () => {
      const mockEventResults = [
        {
          type: 'event',
          id: '1',
          name: 'Test Event',
          description: null,
          venue_name: 'Test Venue',
          start_date: '2024-01-01T00:00:00Z',
          image_url: 'test.jpg',
          rank: 1.0,
        },
      ];

      (mockCache.get as any).mockResolvedValue(null);
      mockDb._mockPreparedStatement.all.mockResolvedValue({
        results: mockEventResults,
      });

      await searchContent(mockDb, 'test@#$%', 'events', 'en', mockCache);

      expect(mockCache.get).toHaveBeenCalledWith('search:en:events:test@#$%');
      expect(mockCache.set).toHaveBeenCalledWith(
        'search:en:events:test@#$%',
        expect.any(Array),
        300
      );
    });
  });
});
