# 变更记录（Changelog）

## 0.4.2.5 - 2025-07-28

### 文档系统全面更新

#### 文档重构

- **项目文档全面更新**：
  - 更新主 README.md，反映当前的模块化架构
  - 修正项目结构描述，从旧的 controllers/services 结构更新为 modules 结构
  - 更新所有 API 示例和功能描述

- **API 文档优化**：
  - 更新 OpenAPI 文档路径引用（docs-site/static/openapi.json）
  - 完善 API 文档索引和使用指南
  - 更新代码示例以反映模块化架构

- **架构文档重写**：
  - 重写系统设计文档，展示 Feature-First 模块化架构
  - 更新代码示例以反映当前的模块结构
  - 完善模块内部结构和依赖关系说明

- **模块文档补全**：
  - 新增 artists.md 和 appearances.md 模块文档
  - 更新模块索引，标记所有模块为已实现状态
  - 完善各模块的 API 端点和使用示例

- **开发指南更新**：
  - 大幅更新贡献指南，包含完整的开发流程
  - 更新安装指南中的文件路径
  - 完善测试要求和代码规范说明

#### 文档站点优化

- 统一文档更新时间戳
- 修正所有过期的文件路径引用
- 完善文档间的交叉引用链接

## 0.4.3 - 2025-07-28

### 多语言缓存与新API端点

#### 新功能

- **多语言支持增强**：
  - 增强 `localeMiddleware`，支持 `X-Locale` 请求头（最高优先级）
  - 语言检测优先级：`X-Locale` > Cookie > `Accept-Language` > 默认语言
  - 添加 `X-Response-Locale` 响应头和更新 `Vary` 头部

- **新增API端点**：
  - `/search` - 多语言搜索API，支持事件和社团搜索
  - `/feed` - 多语言内容流API，支持分页和类型过滤

- **标准化响应格式**：
  - 所有新API采用统一格式：`{ success, data, locale, timestamp, meta }`
  - 创建响应格式化工具 `src/utils/responseFormatter.ts`

#### 缓存优化

- **多语言缓存隔离**：
  - 所有缓存键包含语言参数，确保不同语言的缓存完全分离
  - 缓存键格式：`search:${locale}:${type}:${query}`、`feed:${locale}:${type}:page:${page}`
  - 更新 circle service 支持 locale 参数

#### 数据库改进

- **翻译表支持**：
  - 新增 `circle_translations` 和 `artist_translations` 表的 migration
  - 支持多语言名称、描述和标签
  - 优化多语言查询索引

#### 测试完善

- 更新所有相关测试以支持新的多语言功能
- 修复 locale middleware 测试，支持新的优先级
- 更新 infrastructure 测试以传递 locale 参数

## 0.4.2 - 2025-07-20

### Feature-First Reorganization

- 逐步演进到 **feature-first** 目录结构（以 bookmark 模块试点）
- `tsconfig.json` 添加 `@/modules/*` path alias，简化导入
- 更新 ESLint import-order 与 dependency-cruiser 规则，防止跨模块直接依赖
- 为每个模块聚合 controller / service / schema / repository / test
- 编写迁移脚本、CI 检查与文档《模块化迁移指南》
- 完成后删除旧平级目录或设为只读；跟进 hono/zod-openapi 类型完善后移除 `@ts-expect-error`

## ******* - 2025-07-20

### BREAKING

- **路由重构**：公开业务路由全部迁移至 `src/modules/*/routes.ts`，后台聚合路由集中于 `src/modules/admin`。
- **OpenAPI 输出路径调整**：`scripts/generate-openapi.ts` 现生成 `docs-site/static/openapi.json`，并在写入时自动递增 `package.json.version`（第四位）。
- **弃用旧接口**：`/appearances?event_id=` 兼容调用将在四周后删除，已在响应 Header 中添加 `Deprecation: true` 提示。

### 新功能

- **Bookmarks 模块**：新增收藏社团接口及其 `controller / service / repository / schema` 与集成测试。
- **Admin 聚合路由**：统一后台 Circles / Events / Users / Logs / Stats 路由挂载。

### 改进

- OpenAPI 文档补充全局 `servers` 与 `securitySchemes`，并为每个 Operation 自动生成唯一 `operationId`。
- `src/app.ts` 统一切换至 `OpenAPIHono`，调整中间件与限流器配置。
- 单元 / 集成测试同步适配新路由结构。

### DevOps

- 更新 CI Diff 路径，改为比较 `docs-site/static/openapi.json` 与 `src/api-types.d.ts`。
- `pnpm gen:api` 脚本同步更新前端类型。

## 0.4.1 - 2025-07-17

### DevOps & CI/CD

- **CI & Pre-commit**：配置 `pre-commit` 钩子执行 `pnpm test && pnpm lints`，并调整 `lint-staged` ESLint / Prettier 规则。
- **GitHub Actions**：新增工作流自动发布 `docs/openapi.json` 到 gh-pages，并部署文档站点（Redocly / Stoplight）。
- **Release 流程**：新增 `pnpm run release` 脚本，自动生成 changelog 与 SemVer tag。
- **OpenAPI 规范整改**：
  - 统一所有路由 `tags`，采用 `Admin.Circles` 点号分隔格式。
  - 新增公共 Schema：`SuccessResponse<T>`、`ErrorResponse`、`PaginatedResult<T>`、`ValidationError`、`ErrorCode`（含枚举描述）。
  - 全量替换写操作、分页 / 列表、错误响应为 `$ref` 引用，删除冗余内联定义。
  - 列表接口统一返回 `PaginatedResult<T>` 结构。
  - 更新 `scripts/generate-openapi.ts` 支持公共 Schema 与全局 `tags` 合并，并重新生成 `openapi.json` / `api-types.d.ts`。
  - 修复受影响的测试与前端类型引用。

## 0.4.0 - 2025-07-15

### 重大改进

- **分层改造全面完成**：重构目录至 `controllers/` · `services/` · `schemas/`，Circles 试点推广至所有模块。
- **Service 去耦合**：所有业务逻辑抽离为纯函数，单元测试友好。
- **Controller 统一化**：路由层仅负责挂载与权限。
- **Infrastructure 抽象**：抽象 Logger / Cache / ThirdParty，并通过 `injectDependencies` 自动注入。
- **Zod ↔︎ OpenAPI**：Schema 自动生成接口文档，`openapi-typescript` 反向生成 TS 类型；CI 校验一致性。
- **后台路由描述完善**：所有 `/admin/*` 与 `/auth/*` 核心路由使用 `createRoute` + `.openapi()`。
- **自动化脚本**：新增 `scripts/generate-openapi.ts`，统一版本号与全局文档合并；ValidationError 422、状态码规范化。
- **Bookmarks 功能**：新增收藏社团接口、权限校验、OpenAPI 描述与测试。

### 工具链

- 升级 `@hono/zod-openapi`，完善 `.openapi()` 扩展能力。
- 引入 `tsx` 替换 `ts-node` 以提升启动速度。

### 其他

- 所有单元与集成测试通过，CI 绿色。

## 0.3.0 - 2025-07-12

### 新增

- **RBAC 权限体系**：完善角色鉴权与 `roleGuard` 中间件，所有 `/admin/*` 路由强制 `admin` 角色。
- **请求频率限制**：接入 `hono-rate-limit`，限制 `/auth/*` 5/min，`/admin/*` 60/min。
- **认证改造收尾**：实现 `/auth/register` 路由，默认分配 `viewer` 角色。
- **GET 接口动态字段筛选**：支持 `?fields=id,name` 查询参数。
- **操作审计日志**：记录后台增删改操作，字段包含操作者、动作、目标实体。
- **后台日志查询 API**：`GET /admin/logs`，支持分页与多条件过滤。
- **数据统计接口**：提供核心实体统计信息，如社团总数、展会数量等。

### 工具链

- 引入 **Zod ↔︎ OpenAPI** 自动生成脚本 `scripts/generate-openapi.ts`，并集成 `openapi-typescript` 生成前端类型。

### 其他

- 完成单元测试覆盖，CI 绿色通过。
