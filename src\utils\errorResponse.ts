import { Context } from 'hono';
import { v4 as uuidv4 } from 'uuid';

export interface ApiErrorDetail {
  [key: string]: any;
}

export interface ApiErrorResponse {
  code: number | string;
  message: string;
  detail?: ApiErrorDetail;
  requestId: string;
}

/**
 * 统一构造错误响应
 *
 * @param c        Hono Context
 * @param code     业务错误码
 * @param message  面向终端用户的文案
 * @param status   HTTP 状态码（4xx / 5xx）
 * @param detail   可选，附加调试信息
 */
export const jsonError = (
  c: Context,
  code: number | string,
  message: string,
  status: number,
  detail?: ApiErrorDetail
) => {
  // 复用上游传入的 requestId，若无则生成
  let requestId: string | undefined;

  if (typeof (c as any).get === 'function') {
    requestId = (c as any).get('requestId') as string | undefined;
  }

  if (!requestId) {
    requestId = uuidv4();
    // 测试桩对象可能不存在 set 方法，故做兼容检查
    if (typeof (c as any).set === 'function') {
      (c as any).set('requestId', requestId);
    }
  }

  // 将 requestId 写入响应头（FakeCtx 已提供 header 方法）
  if (typeof (c as any).header === 'function') {
    c.header('X-Request-Id', requestId);
  }

  const body: ApiErrorResponse = { code, message, requestId };
  if (detail !== undefined) body.detail = detail;

  // 使用类型断言简化 HTTP 状态码泛型约束
  return c.json(body, status as any);
};

export const validationError = (
  c: Context,
  fieldErrors: Record<string, string>,
  message = '参数无效',
  code = 10001
) => {
  return jsonError(c, code, message, 422, { fieldErrors });
};

export const jsonSuccess = (
  c: Context,
  message: string,
  data?: any,
  status = 200
) => {
  const response: any = { code: 0, message };
  // 将成功信息写入响应头，便于客户端无需解析 Body 即可读取
  c.header('X-Success-Message', encodeURIComponent(message));
  // 若为 204 No Content，RFC 规定不可携带响应体，单独返回空响应
  if (status === 204) {
    // 使用 Hono Context.body() 保留已设置 Header
    return c.body(null, 204 as any);
  }
  if (data !== undefined) response.data = data;
  return c.json(response, status as any);
};
