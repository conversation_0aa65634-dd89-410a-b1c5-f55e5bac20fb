# Ayafeed 任务看板

> 本文件采用「看板 (Kanban)」视图持续跟踪任务状态，并辅以工作日志与历史归档。
>
> **维护约定**：
>
> 1. 任务在各状态列之间移动，一次只处理少量“进行中”任务 (WIP Limit: 2)。
> 2. 任务标题应清晰、可执行，并带有唯一 ID (如 `AUTH-1`) 和责任标签 (`[FE]`/`[BE]`/`[Test]`)。
> 3. 已完成任务应在 PR 合并时关联 PR 链接，并定期归档至 CHANGELOG。

---

## 任务看板

### 🚧 进行中 (In Progress)

_暂无任务，请从“待办”中拖拽任务至此。_

### 🔍 待审核/测试 (Review / Testing)

_（暂无）_

### 📝 待办 (Backlog)

- **Epic: `#auth` 认证与授权**
  - [x] `AUTH-1`: [FE] 创建登录/注册页面 UI 与表单
  - [x] `AUTH-2`: [FE] 实现 RoleGuard 组件以保护前端路由
  - [ ] `AUTH-9`: [FE] 在前端实现 Token 刷新逻辑的占位处理
  - [ ] `AUTH-10`: [FE] 完善认证流程中的错误处理与 Toast 提示
  - [ ] `AUTH-11`: [Test] 为认证流程编写单元及 E2E 测试

- **Epic: `#feed` 首页情报 Feed**
  - [ ] `FEED-1`: [FE] 实现事件流卡片列表 UI (展会名称、日期、缩略图等)
  - [ ] `FEED-3`: [FE] 实现用户关注 Feed 的 UI 与数据获取
  - [ ] `FEED-4`: [FE] 实现 Tab 筛选功能 (全部/展会/社团/关注)
  - [ ] `FEED-5`: [FE] 为 Feed 列表实现无限滚动 (Infinite Scroll) 和骨架屏 (Skeleton)
  - [ ] `FEED-7`: [FE] 为 Feed 模块添加错误边界 (Error Boundary) 与 Toast 提示
  - [ ] `FEED-8`: [Test] 为 Feed 组件编写单元测试

- **Epic: `#search` 全局搜索与筛选**
  - [ ] `SEARCH-2`: [FE] 开发全局搜索框组件及结果弹窗
  - [ ] `SEARCH-3`: [FE] 开发搜索结果列表 UI (含高亮、分页)
  - [ ] `SEARCH-4`: [FE] 实现高级筛选与排序功能
  - [ ] `SEARCH-5`: [Test] 为搜索功能编写单元测试

- **Epic: `#subscription` 用户订阅**
  - [ ] `SUB-2`: [FE] 开发订阅/取消订阅按钮组件
  - [ ] `SUB-3`: [FE] 开发用户个人订阅列表页面
  - [ ] `SUB-5`: [Test] 为订阅功能编写单元测试

- **Epic: `#perf` 性能与监控**
  - [ ] `PERF-1`: [DevOps] 使用 Webpack Bundle Analyzer 分析并优化包体积
  - [ ] `PERF-2`: [FE] 在长列表页面应用懒加载或虚拟列表技术
  - [ ] `PERF-3`: [DevOps] 接入 Sentry 并上报 Web Vitals
  - [ ] `PERF-4`: [DevOps] 搭建性能监控 Dashboard (如 Grafana)
  - [ ] `PERF-5`: [Test] 建立性能回归测试基线

- **Epic: `#i18n` 国际化与主题**
  - [ ] `I18N-1`: [FE] 接入国际化框架 (如 next-intl)
  - [ ] `I18N-2`: [FE] 抽取项目中的硬编码字符串至翻译文件
  - [ ] `I18N-3`: [FE] 实现深/浅色主题切换功能
  - [ ] `I18N-4`: [Test] 为 i18n 和主题切换编写测试

- **Epic: `#tests` 测试覆盖率**
  - [ ] `TEST-1`: [Test] 补齐核心 hooks 和组件的单元测试，目标覆盖率 ≥ 80%
  - [ ] `TEST-2`: [Test] 编写关键用户流程的 E2E 测试脚本 (登录、搜索、订阅)
  - [ ] `TEST-3`: [DevOps] 在 CI 流程中配置覆盖率报告与门禁

- **Epic: `#responsive` 响应式设计**
  - [ ] `RESP-1`: [FE] 定义全局响应式断点与样式
  - [ ] `RESP-2`: [FE] 适配核心页面 (列表/详情页) 的移动端布局
  - [ ] `RESP-3`: [Test] 在多种设备尺寸下进行浏览器兼容性测试

- **Epic: `#pwa` PWA 支持**
  - [ ] `PWA-1`: [FE] 配置 Service Worker 与离线缓存策略
  - [ ] `PWA-2`: [FE] 配置 `manifest.json` 与应用安装提示
  - [ ] `PWA-3`: [FE] 创建离线状态下的回退页面
  - [ ] `PWA-4`: [Test] 通过 Lighthouse PWA 审核并修复问题

- **Epic: `#ops` 生产运维**
  - [ ] `OPS-1`: [DevOps] 配置生产环境日志收集 (如 Logtail)
  - [ ] `OPS-2`: [DevOps] 配置生产环境错误告警 (如 Sentry -> Slack)
  - [ ] `OPS-3`: [DevOps] 搭建基础业务与性能监控 (如 Grafana)

### 🧊 Icebox（后续版本）

- [ ] `ICE-1`: [Refactor] 评估使用 tanstack-query + HydrationBoundary 统一 SSR/CSR 缓存策略
- [ ] `ICE-2`: [Feature] 实现 MapViewer 的楼层切换与缩放状态持久化

### ✅ 已完成

> 已完成任务请定期移至 CHANGELOG ，避免看板膨胀。

### 🗄️ Archive

> 已全部迁移至 CHANGELOG

## 🚀 前端正式上线前工作量估算 (2025-07-20)

| 任务                   | 预计人天 |
| ---------------------- | -------- |
| 认证授权流程           | 3        |
| 统一 API 错误处理      | 2        |
| 国际化支持             | 2        |
| 测试覆盖率提升 ≥ 80%   | 3        |
| 响应式兼容性适配       | 2        |
| 性能优化（代码拆分等） | 2        |
| PWA / 离线缓存         | 2        |
| 生产环境配置与监控     | 1        |
| 代码审查与安全审计     | 1        |
| **合计**               | **18**   |

> 备注：最乐观 12 人天，最保守 24 人天；两名开发者并行投入，约 1.5–2.5 周可完成。

---

## 工作日志

### 2025-07-18

- 初始任务导入至 Kanban 看板

### 2025-07-19

- `openapi-fetch` 集成完毕：
  - 生成 26 个 React Query hooks 与 4 个辅助类型文件（≈3.2K LOC）
  - 在 `src/lib/http.ts` 注入统一 401/Toast 拦截逻辑
  - 删除手写 API 模块 3 个 (`events.ts` / `circles.json` / `data.tsx`)，清理冗余代码 1.1K LOC
  - 移除 `genErrorEnum` 脚本，将错误码表迁移至 OpenAPI `components.schemas.ErrorCodes`
- 文档更新：
  - `docs/api/README.md` 新增生成流程说明（+285 LOC）
  - `docs/guides/contribution.md` 更新贡献流程章节（+64 LOC）
- 看板维护：
  - 移动 `#openapi P2 openapi-fetch` & `#docs 更新 README` 至已完成列
  - 清空进行中列，保持看板整洁
  - 完成 `test-toast-success`：新增集成测试 `mutation-success-toast`
  - 基础架构改进：OpenAPI 类型同步 P0 / P1 —— 同步类型、抽象 API 调用、预处理 Zod 模型
  - 管理端单元测试 `test-admin-form`：验证表单校验与提交流程
  - Circles/Events 表单字段补全 (`admin-circle-form-fields`)：category、twitter、pixiv、web

### 2025-07-20

- 5 人天
- 后台写操作成功提示统一化（`admin-logic-toast`）：
  - 新增 `showSuccessToast` 方法，封装通用成功提示
  - 替换后台 Circles / Events 等 8 处写操作调用，移除重复实现
- ESLint 严格度提升（`lint-fix`）：
  - 升级 `eslint`、`@typescript-eslint/*`、`eslint-config-next` 等依赖至最新版本
  - 调整规则：关闭 `react/display-name`，启用 `@typescript-eslint/consistent-type-imports`
  - 执行 `pnpm lint --fix`（115 文件，+2017 / −1344 行）
- 看板维护：
  - 将 `admin-logic-toast`、`lint-fix` 移至已完成列
  - 更新「前端正式上线前工作量估算」表，确认 18 人天评估

### 2025-07-21

- 完成三个主要功能模块的开发与测试：
  - `feature-events`：会展列表与详情页开发完成，包含列表展示、详情信息、参展社团等功能
  - `feature-circle-detail`：社团详情页开发完成，实现基本信息展示、参展记录、动态元数据等功能
  - `optimization-api-error-handling`：统一 API 错误捕获与提示，实现全局 Toast 与 Fallback UI
- 所有功能均已完成单元测试与文档更新
- 相关任务已迁移至 CHANGELOG
- 工时统计：
  - `feature-events`：3 人天（列表页 1.5 天，详情页 1.5 天）
  - `feature-circle-detail`：2 人天（基础页面 1 天，参展记录与测试 1 天）
  - `optimization-api-error-handling`：2 人天（错误处理 1 天，UI 组件 1 天）
  - 总计：7 人天，符合原定估算（18 人天）的进度

---
