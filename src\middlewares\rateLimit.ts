import type { Context, MiddlewareHandler } from 'hono';
import { rateLimiter } from 'hono-rate-limiter';

// 默认的 key 生成器：优先使用 Cloudflare 的真实 IP 头部，其次 X-Forwarded-For，最后回退到固定字符串
const defaultKeyGenerator = (c: Context) =>
  c.req.header('cf-connecting-ip') ||
  c.req.header('x-forwarded-for') ||
  'anonymous';

// 基于用户角色的 key 生成器
const roleBasedKeyGenerator = (c: Context) => {
  const baseKey = defaultKeyGenerator(c);
  const user = c.get('user');

  if (user?.role) {
    return `${baseKey}:${user.role}`;
  }
  return `${baseKey}:anonymous`;
};

// 角色限制配置
interface RoleLimits {
  admin: number;
  editor: number;
  viewer: number;
  anonymous: number;
}

/**
 * 创建限流中间件。
 * 为避免在 Cloudflare Workers 全局作用域执行异步操作，
 * 每次请求时动态创建 limiter 实例。
 */
export const createRateLimit = (
  limit: number,
  windowMs: number
): MiddlewareHandler => {
  let limiter: MiddlewareHandler | null = null;

  return (c, next) => {
    if (!limiter) {
      limiter = rateLimiter({
        windowMs,
        limit,
        standardHeaders: 'draft-6',
        keyGenerator: defaultKeyGenerator,
      });
    }
    return (limiter as MiddlewareHandler)(c, next);
  };
};

/**
 * 创建基于用户角色的动态速率限制中间件
 * 不同角色享有不同的速率限制额度
 */
export const createRoleBasedRateLimit = (
  roleLimits: RoleLimits,
  windowMs: number
): MiddlewareHandler => {
  const limiters = new Map<string, MiddlewareHandler>();

  return (c, next) => {
    const user = c.get('user');
    const userRole = user?.role || 'anonymous';
    const limit =
      roleLimits[userRole as keyof RoleLimits] || roleLimits.anonymous;

    const limiterKey = `${userRole}:${limit}:${windowMs}`;

    if (!limiters.has(limiterKey)) {
      const limiter = rateLimiter({
        windowMs,
        limit,
        standardHeaders: 'draft-6',
        keyGenerator: roleBasedKeyGenerator,
      });
      limiters.set(limiterKey, limiter);
    }

    const limiter = limiters.get(limiterKey)!;
    return limiter(c, next);
  };
};
