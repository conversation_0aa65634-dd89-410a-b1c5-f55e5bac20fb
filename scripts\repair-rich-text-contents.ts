#!/usr/bin/env tsx

/**
 * 修复现有实体的富文本内容
 * 为已存在但缺少富文本内容的 events 和 venues 创建内容记录
 */

import { drizzle } from 'drizzle-orm/d1';
import { 
  initializeRichTextContents, 
  checkRichTextContentIntegrity,
  repairRichTextContents 
} from '../src/modules/rich-text/content-initializer';

interface CloudflareBindings {
  DB: D1Database;
}

// 模拟 Cloudflare 环境
const mockEnv: CloudflareBindings = {
  DB: {} as D1Database, // 这将在运行时被实际的数据库连接替换
};

async function repairAllRichTextContents() {
  console.log('🔧 开始修复富文本内容...\n');

  try {
    const db = mockEnv.DB;
    
    // 1. 获取所有 events
    console.log('📋 检查 Events...');
    const eventsResult = await db.prepare('SELECT id FROM events ORDER BY created_at DESC').all();
    const events = eventsResult.results as Array<{ id: string }>;
    
    let eventRepaired = 0;
    let eventErrors: string[] = [];
    
    for (const event of events) {
      try {
        const integrity = await checkRichTextContentIntegrity(db, 'event', event.id);
        
        if (!integrity.isComplete) {
          console.log(`  🔧 修复 Event ${event.id}: 缺少 ${integrity.missing.length} 条内容`);
          const result = await repairRichTextContents(db, 'event', event.id);
          eventRepaired += result.repaired;
          eventErrors.push(...result.errors);
        } else {
          console.log(`  ✅ Event ${event.id}: 内容完整`);
        }
      } catch (error) {
        const errorMsg = `Event ${event.id} 检查失败: ${error}`;
        eventErrors.push(errorMsg);
        console.error(`  ❌ ${errorMsg}`);
      }
    }
    
    // 2. 获取所有 venues
    console.log('\n📋 检查 Venues...');
    const venuesResult = await db.prepare('SELECT id FROM venues ORDER BY created_at DESC').all();
    const venues = venuesResult.results as Array<{ id: string }>;
    
    let venueRepaired = 0;
    let venueErrors: string[] = [];
    
    for (const venue of venues) {
      try {
        const integrity = await checkRichTextContentIntegrity(db, 'venue', venue.id);
        
        if (!integrity.isComplete) {
          console.log(`  🔧 修复 Venue ${venue.id}: 缺少 ${integrity.missing.length} 条内容`);
          const result = await repairRichTextContents(db, 'venue', venue.id);
          venueRepaired += result.repaired;
          venueErrors.push(...result.errors);
        } else {
          console.log(`  ✅ Venue ${venue.id}: 内容完整`);
        }
      } catch (error) {
        const errorMsg = `Venue ${venue.id} 检查失败: ${error}`;
        venueErrors.push(errorMsg);
        console.error(`  ❌ ${errorMsg}`);
      }
    }
    
    // 3. 输出总结
    console.log('\n📊 修复总结:');
    console.log(`  Events: 检查 ${events.length} 个，修复 ${eventRepaired} 条内容`);
    console.log(`  Venues: 检查 ${venues.length} 个，修复 ${venueRepaired} 条内容`);
    console.log(`  总计修复: ${eventRepaired + venueRepaired} 条内容`);
    
    if (eventErrors.length > 0 || venueErrors.length > 0) {
      console.log('\n⚠️  错误列表:');
      [...eventErrors, ...venueErrors].forEach(error => {
        console.log(`  - ${error}`);
      });
    }
    
    console.log('\n✅ 富文本内容修复完成!');
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
    process.exit(1);
  }
}

async function checkSpecificEntity() {
  const entityType = process.argv[3] as 'event' | 'venue';
  const entityId = process.argv[4];
  
  if (!entityType || !entityId) {
    console.log('用法: tsx scripts/repair-rich-text-contents.ts check <event|venue> <entity_id>');
    process.exit(1);
  }
  
  try {
    const db = mockEnv.DB;
    const integrity = await checkRichTextContentIntegrity(db, entityType, entityId);
    
    console.log(`📋 ${entityType.toUpperCase()} ${entityId} 内容完整性检查:`);
    console.log(`  预期内容数量: ${integrity.expectedCount}`);
    console.log(`  实际内容数量: ${integrity.actualCount}`);
    console.log(`  是否完整: ${integrity.isComplete ? '✅ 是' : '❌ 否'}`);
    
    if (!integrity.isComplete) {
      console.log(`  缺少的内容:`);
      integrity.missing.forEach(missing => {
        console.log(`    - ${missing.language_code}/${missing.content_type}`);
      });
      
      console.log('\n🔧 开始修复...');
      const result = await repairRichTextContents(db, entityType, entityId);
      console.log(`✅ 修复完成: 创建 ${result.repaired} 条内容`);
      
      if (result.errors.length > 0) {
        console.log('⚠️  修复错误:');
        result.errors.forEach(error => console.log(`  - ${error}`));
      }
    }
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
    process.exit(1);
  }
}

// 主函数
async function main() {
  const command = process.argv[2];
  
  if (command === 'check') {
    await checkSpecificEntity();
  } else if (command === 'repair' || !command) {
    await repairAllRichTextContents();
  } else {
    console.log('用法:');
    console.log('  tsx scripts/repair-rich-text-contents.ts                    # 修复所有实体');
    console.log('  tsx scripts/repair-rich-text-contents.ts repair             # 修复所有实体');
    console.log('  tsx scripts/repair-rich-text-contents.ts check <type> <id>  # 检查特定实体');
    console.log('');
    console.log('示例:');
    console.log('  tsx scripts/repair-rich-text-contents.ts check event 562fdb76-027d-4f13-8655-a9e16832b08e');
    process.exit(1);
  }
}

// 注意：这个脚本需要在有数据库连接的环境中运行
// 在实际使用时，需要确保 mockEnv.DB 被正确的数据库连接替换
if (require.main === module) {
  console.log('⚠️  注意: 这个脚本需要在有数据库连接的环境中运行');
  console.log('请在 Cloudflare Workers 环境或本地开发环境中使用');
  // main().catch(console.error);
}
