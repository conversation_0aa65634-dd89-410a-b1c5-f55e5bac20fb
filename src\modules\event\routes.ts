import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import type { Context } from 'hono';

import { eventSchema } from './schema';
import * as eventService from './service';
import { getDB } from '@/infrastructure';
import type { <PERSON><PERSON>, Logger } from '@/infrastructure';
import { paginatedResult } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { jsonWithFields } from '@/utils/fieldFilter';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const pubEvents = new OpenAPIHono<HonoApp>();

// OpenAPI route definitions (copied from old controller)
const listEventsRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '展会列表（公开）',
  tags: ['Events'],
  request: {
    query: z.object({
      page: z.string().optional().openapi({ example: '1' }),
      pageSize: z.string().optional().openapi({ example: '50' }),
      keyword: z.string().optional().openapi({ example: 'Reitaisai' }),
      date_from: z.string().optional().openapi({ example: '20250101' }),
      date_to: z.string().optional().openapi({ example: '20251231' }),
    }),
  },
  responses: {
    200: {
      description: '展会列表',
      content: { 'application/json': { schema: paginatedResult(eventSchema) } },
    },
  },
});

const getEventRoute = createRoute({
  method: 'get',
  path: '/{id}',
  summary: '展会详情',
  tags: ['Events'],
  request: {
    params: z.object({ id: z.string().openapi({ example: 'uuid-123' }) }),
  },
  responses: {
    200: {
      description: '展会详情',
      content: { 'application/json': { schema: eventSchema } },
    },
    404: { description: 'Not Found' },
  },
});

const getEventCirclesRoute = createRoute({
  method: 'get',
  path: '/{id}/circles',
  summary: '展会社团列表',
  tags: ['Events'],
  request: { params: z.object({ id: z.string() }) },
  responses: {
    200: {
      description: '社团列表',
      content: {
        'application/json': {
          schema: z.array(
            eventSchema.extend({
              booth_id: z
                .string()
                .nullable()
                .optional()
                .openapi({ example: 'あ01a' }),
            })
          ),
        },
      },
    },
  },
});

const getEventAppearancesRoute = createRoute({
  method: 'get',
  path: '/{id}/appearances',
  summary: '展会参展记录',
  tags: ['Events'],
  request: { params: z.object({ id: z.string() }) },
  responses: {
    200: {
      description: '参展记录分页',
      content: { 'application/json': { schema: paginatedResult(z.any()) } },
    },
  },
});

// Handlers
async function listEventsHandler(c: Context) {
  const db = getDB(c);
  const cache: Cache | undefined = c.get('cache');
  const logger: Logger | undefined = c.get('logger');
  const locale = (c.get('locale') as 'en' | 'ja' | 'zh') || 'en';
  const data = await eventService.listEvents(
    db,
    new URL(c.req.url).searchParams,
    locale,
    cache,
    logger
  );
  return jsonWithFields(c, data);
}

// (create/update/delete 管理端动作已在其他路由实现，这里不暴露)

async function getEventHandler(c: Context) {
  const db = getDB(c);
  const locale = (c.get('locale') as 'en' | 'ja' | 'zh') || 'en';
  const id = c.req.param('id');
  const event = await eventService.getEvent(db, id, locale);
  if (!event) return jsonError(c, 10002, '资源不存在', 404);
  return jsonWithFields(c, event);
}

async function getEventCirclesHandler(c: Context) {
  const db = getDB(c);
  const items = await eventService.listCirclesByEvent(db, c.req.param('id'));
  return c.json(items);
}

async function getEventAppearancesHandler(c: Context) {
  const db = getDB(c);
  const res = await eventService.listAppearances(
    db,
    c.req.param('id'),
    new URL(c.req.url).searchParams
  );
  return c.json(res);
}

// Register
registerOpenApiRoute(pubEvents, listEventsRoute, listEventsHandler);
registerOpenApiRoute(pubEvents, getEventRoute, getEventHandler);
registerOpenApiRoute(pubEvents, getEventCirclesRoute, getEventCirclesHandler);
registerOpenApiRoute(
  pubEvents,
  getEventAppearancesRoute,
  getEventAppearancesHandler
);

export { pubEvents, pubEvents as routes };
