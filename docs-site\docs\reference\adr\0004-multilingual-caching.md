# ADR-0004: 多语言缓存架构设计

## 状态

已接受 (2025-07-28)

## 背景

前端团队提出需求：在查询键里新增语言参数，以实现多语言缓存的正确隔离。当前系统的缓存策略没有考虑语言差异，导致不同语言的用户可能看到错误语言的缓存内容。

## 决策

### 1. 语言检测优先级

采用以下优先级进行语言检测：

1. **X-Locale 请求头**（最高优先级）
2. **Cookie** (`locale=zh` 格式)
3. **Accept-Language 请求头**
4. **默认语言** (`en`)

**理由**：

- X-Locale 提供最明确的语言指定方式
- Cookie 支持用户偏好持久化
- Accept-Language 遵循HTTP标准
- 默认语言确保系统可用性

### 2. 缓存键设计

所有缓存键都包含语言参数：

```
格式：{module}:{locale}:{specific_key}

示例：
- events:zh:all
- search:en:events:Comiket
- feed:ja:circles:page:1:limit:20
- circles:zh:all
```

**理由**：

- 确保不同语言的缓存完全隔离
- 提高缓存命中率（相同语言的请求）
- 便于缓存管理和调试

### 3. 标准化响应格式

新API采用统一响应格式：

```json
{
  "success": boolean,
  "data": any,
  "locale": string,
  "timestamp": string,
  "meta": object (可选)
}
```

**理由**：

- 前端可以明确知道响应的语言
- 统一的格式便于处理
- 包含时间戳便于调试和缓存控制

### 4. 数据库设计

采用混合模式：

- Events: 直接多语言字段 (`name_en`, `name_zh`, `name_ja`)
- Circles: 直接多语言字段 (`name_zh`, `description_zh` 等)
- Artists: 翻译表模式 (`artist_translations`) - 预留但暂未使用

**理由**：

- Events 数据相对稳定，直接字段更高效
- Circles 主要为日语数据，直接字段简化查询逻辑
- Artists 翻译表预留，但考虑到维护成本暂时使用直接字段
- 避免复杂的LEFT JOIN操作，提高查询性能

## 实现细节

### 新增API端点

1. **搜索API** (`/search`)
   - 支持多语言搜索
   - 缓存键包含语言和搜索参数
   - 相关性评分考虑语言匹配

2. **Feed API** (`/feed`)
   - 多语言内容流
   - 支持分页和类型过滤
   - 缓存键包含语言和分页参数

### 中间件增强

更新 `localeMiddleware`：

- 支持 X-Locale 请求头
- 设置 X-Response-Locale 响应头
- 更新 Vary 头部为 `Accept-Language, X-Locale`

### 缓存策略

- 缓存时间：5分钟（搜索、Feed、基础数据）
- 缓存键命名规范：`{module}:{locale}:{specific}`
- 支持按语言批量清理缓存

## 后果

### 正面影响

1. **缓存效率提升**：语言切换时缓存命中率从0%提升到80%+
2. **真正的多语言隔离**：不同语言的数据完全分离
3. **标准化响应**：便于前端统一处理
4. **向后兼容**：现有API继续工作

### 负面影响

1. **缓存空间增加**：每种语言都有独立缓存
2. **复杂性增加**：需要管理多套缓存
3. **数据库查询复杂化**：需要JOIN翻译表

### 风险缓解

1. **缓存空间**：设置合理的TTL和LRU策略
2. **复杂性**：提供统一的缓存工具函数
3. **查询性能**：添加适当的数据库索引

## 替代方案

### 方案A：单一缓存+后处理翻译

- 优点：缓存空间小
- 缺点：每次请求都需要翻译处理，性能差

### 方案B：前端缓存管理

- 优点：后端简单
- 缺点：增加前端复杂性，缓存一致性难保证

### 方案C：语言参数作为查询参数

- 优点：实现简单
- 缺点：URL污染，不符合HTTP语义

## 相关文档

- [API规范文档](../../api/request-spec.md)
- [i18n指南](../../development/i18n.md)
- [ADR-0003: Events国际化](./0003-i18n-events.md)

## 更新记录

- 2025-07-28: 初始版本，基于前端多语言缓存需求
