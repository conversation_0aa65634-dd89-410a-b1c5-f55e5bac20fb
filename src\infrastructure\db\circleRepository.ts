// D1Database 类型现在在全局作用域中可用
import { v4 as uuidv4 } from 'uuid';

import type { CircleRepository } from '../../modules/circle/repository';
import {
  Circle,
  CircleCreateInput,
  CircleUpdateInput,
  circleSchema,
} from '../../modules/circle/schema';

/**
 * 基于 Cloudflare D1 的 CircleRepository 实现
 */
export class D1CircleRepository implements CircleRepository {
  constructor(private readonly db: D1Database) {}

  async list(): Promise<Circle[]> {
    const { results } = await this.db
      .prepare('SELECT * FROM circles ORDER BY name ASC')
      .all();
    return circleSchema.array().parse(results);
  }

  async findById(id: string): Promise<Circle | null> {
    const raw = await this.db
      .prepare('SELECT * FROM circles WHERE id = ?')
      .bind(id)
      .first();
    if (!raw) return null;
    return circleSchema.parse(raw);
  }

  async create(input: CircleCreateInput): Promise<Circle> {
    const id = uuidv4();
    const urls = JSON.stringify({
      author: input.author ?? null,
      twitter_url: input.twitter_url ?? null,
      pixiv_url: input.pixiv_url ?? null,
      web_url: input.web_url ?? null,
    });

    await this.db
      .prepare('INSERT INTO circles (id, name, urls) VALUES (?, ?, ?)')
      .bind(id, input.name, urls)
      .run();

    return (await this.findById(id)) as Circle;
  }

  async update(id: string, input: CircleUpdateInput): Promise<Circle | null> {
    const fields: string[] = [];
    const params: (string | null)[] = [];

    if (input.name !== undefined) {
      fields.push('name = ?');
      params.push(input.name);
    }
    if (
      input.author !== undefined ||
      input.twitter_url !== undefined ||
      input.pixiv_url !== undefined ||
      input.web_url !== undefined
    ) {
      const circle = await this.findById(id);
      if (!circle) return null;

      const currentUrls = circle.urls
        ? JSON.parse(circle.urls)
        : {
            author: null,
            twitter_url: null,
            pixiv_url: null,
            web_url: null,
          };

      const urls = JSON.stringify({
        ...currentUrls,
        author: input.author ?? currentUrls.author,
        twitter_url: input.twitter_url ?? currentUrls.twitter_url,
        pixiv_url: input.pixiv_url ?? currentUrls.pixiv_url,
        web_url: input.web_url ?? currentUrls.web_url,
      });
      fields.push('urls = ?');
      params.push(urls);
    }

    if (fields.length) {
      params.push(id);
      await this.db
        .prepare(`UPDATE circles SET ${fields.join(', ')} WHERE id = ?`)
        .bind(...params)
        .run();
    }

    return this.findById(id);
  }

  async delete(id: string): Promise<void> {
    await this.db.prepare('DELETE FROM circles WHERE id = ?').bind(id).run();
  }
}
