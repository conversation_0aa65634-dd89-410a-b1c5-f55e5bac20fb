﻿# 环境准备（Prerequisites）

本项目依赖如下工具与服务，确保在开始之前已安装或准备完毕。

## 操作系统

- **macOS 12+**（Apple Silicon 与 Intel 已验证）
- **Windows 10 21H2+**（推荐 WSL2）
- **Ubuntu 22.04+** 或其他现代 Linux 发行版

## 运行时与包管理

| 工具    | 版本（及以上） | 检查命令        |
| ------- | -------------- | --------------- |
| Node.js | 20.x LTS       | `node -v`       |
| pnpm    | 8.x            | `pnpm -v`       |
| Git     | 2.40           | `git --version` |

> Node.js 与 pnpm 需加入 `$PATH`。如使用 **nvm**，执行 `nvm use 20`。

## Cloudflare 生态

| 工具               | 用途                        | 版本 | 备注                              |
| ------------------ | --------------------------- | ---- | --------------------------------- |
| Wrangler           | Cloudflare Workers & D1 CLI | 4.x  | `pnpm i -g wrangler@latest`       |
| Cloudflare Account | 部署 & 数据库               | -    | 需拥有 `D1:Edit` 权限的 API Token |

## 数据库

- **Cloudflare D1**：生产与预发环境使用
- **SQLite (in-memory)**：单元测试自动使用，无需额外设置

## 可选工具

- **SQLite CLI**：调试本地 D1 snapshot
- **mkcert**：本地 HTTPS 测试
- **jq**：脚本处理 JSON

## 环境变量

复制 `wrangler.example.jsonc` 为 `wrangler.jsonc`，并根据实际账号信息填写：

```jsonc
{
  "d1_databases": [
    { "binding": "DB", "database_id": "...", "database_name": "ayafeed-dev" },
  ],
  "account_id": "your-account-id",
  "zone_id": "optional",
}
```

更多变量与默认值见 `src/types.ts` 的 `CloudflareBindings` 接口声明。
