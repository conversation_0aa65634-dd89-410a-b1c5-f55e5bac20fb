import { z } from '@hono/zod-openapi';

// ========================================
// 基础枚举定义
// ========================================

// 实体类型枚举 - 仅支持 Events 和 Venues
export const EntityTypeEnum = z.enum(['event', 'venue']);
export type EntityType = z.infer<typeof EntityTypeEnum>;

// 语言代码枚举 - 支持三语言
export const LanguageCodeEnum = z.enum(['en', 'zh', 'ja']);
export type LanguageCode = z.infer<typeof LanguageCodeEnum>;

// 标签页键值验证 - 字母、数字、下划线、连字符
export const TabKeySchema = z
  .string()
  .min(2, '标签页键值至少2个字符')
  .max(50, '标签页键值最多50个字符')
  .regex(/^[a-zA-Z0-9_-]+$/, '标签页键值只能包含字母、数字、下划线、连字符');

// ========================================
// 内容类型配置相关 Schema
// ========================================

// 内容类型配置实体 Schema
export const contentTypeConfigSchema = z.object({
  id: z.string().openapi({ example: 'config-uuid-123' }),
  entity_type: EntityTypeEnum.openapi({ example: 'event' }),
  language_code: LanguageCodeEnum.openapi({ example: 'en' }),
  key: TabKeySchema.openapi({ example: 'introduction' }),
  label: z.string().min(1).max(100).openapi({ example: 'Introduction' }),
  placeholder: z
    .string()
    .optional()
    .openapi({ example: 'Enter introduction...' }),
  icon: z.string().optional().openapi({ example: 'info' }),
  sort_order: z.number().int().min(0).openapi({ example: 0 }),
  is_active: z.boolean().openapi({ example: true }),
  is_preset: z.boolean().openapi({ example: false }),
  deleted_at: z.string().optional(),
  deleted_by: z.string().optional(),
  created_at: z.string().openapi({ example: '2025-01-04T10:00:00.000Z' }),
  updated_at: z.string().openapi({ example: '2025-01-04T10:00:00.000Z' }),
});

export type ContentTypeConfig = z.infer<typeof contentTypeConfigSchema>;

// 富文本内容实体 Schema
export const richTextContentSchema = z.object({
  id: z.string().openapi({ example: 'content-uuid-123' }),
  entity_type: EntityTypeEnum.openapi({ example: 'event' }),
  entity_id: z.string().openapi({ example: 'reitaisai-22' }),
  language_code: LanguageCodeEnum.openapi({ example: 'en' }),
  content_type: z.string().openapi({ example: 'introduction' }),
  content: z.string().openapi({
    example:
      '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]}',
  }),
  created_at: z.string().openapi({ example: '2025-01-04T10:00:00.000Z' }),
  updated_at: z.string().openapi({ example: '2025-01-04T10:00:00.000Z' }),
});

export type RichTextContent = z.infer<typeof richTextContentSchema>;

// ========================================
// 请求和响应 Schema
// ========================================

// 创建配置请求 Schema - 强制后端生成 key
export const createConfigRequest = z.object({
  entity_type: EntityTypeEnum,
  language_code: LanguageCodeEnum,
  // key 字段移除，完全由后端生成
  label: z
    .string()
    .min(1, '标签名称不能为空')
    .max(100, '标签名称最多100个字符'),
  placeholder: z.string().max(200, '提示文本最多200个字符').optional(),
  icon: z.string().max(50, '图标名称最多50个字符').optional(),
  sort_order: z.number().int().min(0, '排序值不能为负数').optional(),
  is_active: z.boolean().optional(),
  is_preset: z.boolean().optional(),
});

export type CreateConfigRequest = z.infer<typeof createConfigRequest>;

// 更新配置请求 Schema
export const updateConfigRequest = z.object({
  label: z.string().min(1).max(100).optional(),
  placeholder: z.string().max(200).optional(),
  icon: z.string().max(50).optional(),
  sort_order: z.number().int().min(0).optional(),
  is_active: z.boolean().optional(),
});

export type UpdateConfigRequest = z.infer<typeof updateConfigRequest>;

// 创建内容请求 Schema
export const createContentRequest = z.object({
  entity_type: EntityTypeEnum,
  entity_id: z.string().min(1, '实体ID不能为空'),
  language_code: LanguageCodeEnum,
  content_type: z.string().min(1, '内容类型不能为空'),
  content: z.string().max(1000000, '内容长度不能超过1MB'),
});

export type CreateContentRequest = z.infer<typeof createContentRequest>;

// 更新内容请求 Schema
export const updateContentRequest = z.object({
  content: z.string().max(1000000, '内容长度不能超过1MB'),
});

export type UpdateContentRequest = z.infer<typeof updateContentRequest>;

// 批量内容请求 Schema
export const batchContentRequest = z.object({
  contents: z.array(createContentRequest).min(1, '至少提供一个内容项'),
});

export type BatchContentRequest = z.infer<typeof batchContentRequest>;

// 内容类型枚举 - 动态内容类型
export const ContentTypeEnum = z.string().min(1, '内容类型不能为空');
export type ContentType = z.infer<typeof ContentTypeEnum>;

// 批量配置排序请求 Schema
export const reorderConfigsRequest = z.object({
  entity_type: EntityTypeEnum,
  language_code: LanguageCodeEnum,
  orders: z.array(
    z.object({
      id: z.string(),
      sort_order: z.number().int().min(0),
    })
  ),
});

export type ReorderConfigsRequest = z.infer<typeof reorderConfigsRequest>;

// 批量状态更新请求 Schema
export const batchUpdateStatusRequest = z.object({
  ids: z.array(z.string()).min(1, '至少选择一个配置'),
  is_active: z.boolean(),
});

export type BatchUpdateStatusRequest = z.infer<typeof batchUpdateStatusRequest>;

// 软删除请求 Schema
export const softDeleteRequest = z.object({
  deleted_by: z.string().min(1, '删除操作者不能为空'),
});

export type SoftDeleteRequest = z.infer<typeof softDeleteRequest>;

// ========================================
// 响应 Schema
// ========================================

// 配置列表响应 Schema
export const configListResponse = z.array(contentTypeConfigSchema);
export type ConfigListResponse = z.infer<typeof configListResponse>;

// 内容列表响应 Schema
export const contentListResponse = z.array(richTextContentSchema);
export type ContentListResponse = z.infer<typeof contentListResponse>;

// 单个内容响应 Schema
export const contentResponse = richTextContentSchema;
export type ContentResponse = z.infer<typeof contentResponse>;

// 标签页配置和内容组合响应 Schema
export const tabWithContentSchema = z.object({
  config: contentTypeConfigSchema,
  content: richTextContentSchema.optional(),
});

export type TabWithContent = z.infer<typeof tabWithContentSchema>;

// 实体标签页完整响应 Schema
export const entityTabsResponse = z.object({
  entity_type: EntityTypeEnum,
  entity_id: z.string(),
  language_code: LanguageCodeEnum,
  tabs: z.array(tabWithContentSchema),
});

export type EntityTabsResponse = z.infer<typeof entityTabsResponse>;

// 操作结果响应 Schema
export const operationResultResponse = z.object({
  success: z.boolean(),
  message: z.string(),
  affected_count: z.number().optional(),
  data: z.any().optional(),
});

export type OperationResultResponse = z.infer<typeof operationResultResponse>;
