import { describe, it, expect } from 'vitest';
import { Hono } from 'hono';
import { localeMiddleware } from '../locale';

function buildApp() {
  const app = new Hono();
  app.use(localeMiddleware);
  // 类型跳过：c.get 返回 any
  app.get('/', (c) => c.json({ locale: (c as any).get('locale') }));
  return app;
}

describe('locale middleware', () => {
  it('X-Locale 头部优先级最高', async () => {
    const app = buildApp();
    const res = await app.request('/', {
      headers: {
        'x-locale': 'zh',
        cookie: 'locale=ja',
        'accept-language': 'en-US,en;q=0.9',
      },
    });
    expect(res.status).toBe(200);
    expect(await res.json()).toEqual({ locale: 'zh' });
    expect(res.headers.get('content-language')).toBe('zh');
    expect(res.headers.get('x-response-locale')).toBe('zh');
    expect(res.headers.get('vary')).toBe('Accept-Language, X-Locale');
  });

  it('cookie 优先于 Accept-Language', async () => {
    const app = buildApp();
    const res = await app.request('/', {
      headers: {
        cookie: 'locale=ja',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
      },
    });
    expect(res.status).toBe(200);
    expect(await res.json()).toEqual({ locale: 'ja' });
    expect(res.headers.get('content-language')).toBe('ja');
    expect(res.headers.get('set-cookie')).toBeNull();
  });

  it('Accept-Language 命中 zh', async () => {
    const app = buildApp();
    const res = await app.request('/', {
      headers: { 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8' },
    });
    expect(await res.json()).toEqual({ locale: 'zh' });
    expect(res.headers.get('content-language')).toBe('zh');
    expect(res.headers.get('set-cookie')).toMatch(/locale=zh/);
  });

  it('Accept-Language 未命中，降级 en', async () => {
    const app = buildApp();
    const res = await app.request('/', {
      headers: { 'accept-language': 'fr-FR,fr;q=0.9' },
    });
    expect(await res.json()).toEqual({ locale: 'en' });
    expect(res.headers.get('content-language')).toBe('en');
    expect(res.headers.get('set-cookie')).toMatch(/locale=en/);
  });

  it('无头无 cookie，默认 en', async () => {
    const app = buildApp();
    const res = await app.request('/');
    expect(await res.json()).toEqual({ locale: 'en' });
    expect(res.headers.get('content-language')).toBe('en');
    expect(res.headers.get('set-cookie')).toMatch(/locale=en/);
  });

  it('X-Locale 无效值时降级到 cookie', async () => {
    const app = buildApp();
    const res = await app.request('/', {
      headers: {
        'x-locale': 'invalid',
        cookie: 'locale=ja',
      },
    });
    expect(await res.json()).toEqual({ locale: 'ja' });
    expect(res.headers.get('content-language')).toBe('ja');
  });
});
