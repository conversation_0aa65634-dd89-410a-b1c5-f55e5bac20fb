import type { EntityType, LanguageCode } from './schema';

/**
 * 富文本标签页审计日志管理器
 * 记录和查询用户操作历史
 */
export class RichTextAuditManager {
  // ========================================
  // 审计日志记录
  // ========================================

  /**
   * 记录配置操作
   */
  static async logConfigOperation(
    operation: ConfigAuditOperation,
    details: ConfigOperationDetails,
    context: AuditContext
  ): Promise<void> {
    const auditLog: AuditLog = {
      id: this.generateAuditId(),
      timestamp: new Date().toISOString(),
      user_id: context.userId,
      user_ip: context.userIp,
      user_agent: context.userAgent,
      operation_type: 'CONFIG',
      operation: operation,
      entity_type: details.entityType,
      entity_id: details.configId,
      language_code: details.languageCode,
      details: {
        config_key: details.configKey,
        config_label: details.configLabel,
        is_preset: details.isPreset,
        changes: details.changes,
        old_values: details.oldValues,
        new_values: details.newValues,
      },
      metadata: {
        request_id: context.requestId,
        session_id: context.sessionId,
        api_version: context.apiVersion,
      },
    };

    await this.saveAuditLog(auditLog);
  }

  /**
   * 记录内容操作
   */
  static async logContentOperation(
    operation: ContentAuditOperation,
    details: ContentOperationDetails,
    context: AuditContext
  ): Promise<void> {
    const auditLog: AuditLog = {
      id: this.generateAuditId(),
      timestamp: new Date().toISOString(),
      user_id: context.userId,
      user_ip: context.userIp,
      user_agent: context.userAgent,
      operation_type: 'CONTENT',
      operation: operation,
      entity_type: details.entityType,
      entity_id: details.entityId,
      language_code: details.languageCode,
      details: {
        content_type: details.contentType,
        content_length: details.contentLength,
        content_hash: details.contentHash,
        changes: details.changes,
      },
      metadata: {
        request_id: context.requestId,
        session_id: context.sessionId,
        api_version: context.apiVersion,
      },
    };

    await this.saveAuditLog(auditLog);
  }

  /**
   * 记录批量操作
   */
  static async logBatchOperation(
    operation: BatchAuditOperation,
    details: BatchOperationDetails,
    context: AuditContext
  ): Promise<void> {
    const auditLog: AuditLog = {
      id: this.generateAuditId(),
      timestamp: new Date().toISOString(),
      user_id: context.userId,
      user_ip: context.userIp,
      user_agent: context.userAgent,
      operation_type: 'BATCH',
      operation: operation,
      entity_type: details.entityType,
      entity_id: 'batch',
      language_code: details.languageCode,
      details: {
        batch_size: details.batchSize,
        success_count: details.successCount,
        failed_count: details.failedCount,
        affected_items: details.affectedItems,
        operation_summary: details.operationSummary,
      },
      metadata: {
        request_id: context.requestId,
        session_id: context.sessionId,
        api_version: context.apiVersion,
        execution_time_ms: details.executionTimeMs,
      },
    };

    await this.saveAuditLog(auditLog);
  }

  // ========================================
  // 审计日志查询
  // ========================================

  /**
   * 查询用户操作历史
   */
  static async getUserOperationHistory(
    userId: string,
    options: QueryOptions = {}
  ): Promise<AuditQueryResult> {
    const filters: AuditFilter[] = [
      { field: 'user_id', operator: 'eq', value: userId },
    ];

    return this.queryAuditLogs(filters, options);
  }

  /**
   * 查询实体操作历史
   */
  static async getEntityOperationHistory(
    entityType: EntityType,
    entityId: string,
    options: QueryOptions = {}
  ): Promise<AuditQueryResult> {
    const filters: AuditFilter[] = [
      { field: 'entity_type', operator: 'eq', value: entityType },
      { field: 'entity_id', operator: 'eq', value: entityId },
    ];

    return this.queryAuditLogs(filters, options);
  }

  /**
   * 查询配置变更历史
   */
  static async getConfigChangeHistory(
    entityType: EntityType,
    languageCode: LanguageCode,
    configKey?: string,
    options: QueryOptions = {}
  ): Promise<AuditQueryResult> {
    const filters: AuditFilter[] = [
      { field: 'operation_type', operator: 'eq', value: 'CONFIG' },
      { field: 'entity_type', operator: 'eq', value: entityType },
      { field: 'language_code', operator: 'eq', value: languageCode },
    ];

    if (configKey) {
      filters.push({
        field: 'details.config_key',
        operator: 'eq',
        value: configKey,
      });
    }

    return this.queryAuditLogs(filters, options);
  }

  /**
   * 查询内容变更历史
   */
  static async getContentChangeHistory(
    entityType: EntityType,
    entityId: string,
    languageCode: LanguageCode,
    contentType?: string,
    options: QueryOptions = {}
  ): Promise<AuditQueryResult> {
    const filters: AuditFilter[] = [
      { field: 'operation_type', operator: 'eq', value: 'CONTENT' },
      { field: 'entity_type', operator: 'eq', value: entityType },
      { field: 'entity_id', operator: 'eq', value: entityId },
      { field: 'language_code', operator: 'eq', value: languageCode },
    ];

    if (contentType) {
      filters.push({
        field: 'details.content_type',
        operator: 'eq',
        value: contentType,
      });
    }

    return this.queryAuditLogs(filters, options);
  }

  /**
   * 查询安全相关操作
   */
  static async getSecurityOperations(
    options: QueryOptions = {}
  ): Promise<AuditQueryResult> {
    const securityOperations = [
      'DELETE_CONFIG',
      'RESTORE_CONFIG',
      'BATCH_DELETE',
      'BATCH_IMPORT',
      'PRESET_MODIFY',
    ];

    const filters: AuditFilter[] = [
      { field: 'operation', operator: 'in', value: securityOperations },
    ];

    return this.queryAuditLogs(filters, options);
  }

  // ========================================
  // 统计分析
  // ========================================

  /**
   * 获取操作统计
   */
  static async getOperationStatistics(
    timeRange: TimeRange,
    groupBy: 'user' | 'operation' | 'entity_type' = 'operation'
  ): Promise<OperationStatistics> {
    const filters: AuditFilter[] = [
      { field: 'timestamp', operator: 'gte', value: timeRange.start },
      { field: 'timestamp', operator: 'lte', value: timeRange.end },
    ];

    const logs = await this.queryAuditLogs(filters, { limit: 10000 });

    return this.aggregateStatistics(logs.items, groupBy);
  }

  /**
   * 获取用户活跃度统计
   */
  static async getUserActivityStatistics(
    timeRange: TimeRange
  ): Promise<UserActivityStatistics> {
    const filters: AuditFilter[] = [
      { field: 'timestamp', operator: 'gte', value: timeRange.start },
      { field: 'timestamp', operator: 'lte', value: timeRange.end },
    ];

    const logs = await this.queryAuditLogs(filters, { limit: 10000 });

    return this.aggregateUserActivity(logs.items);
  }

  // ========================================
  // 辅助方法
  // ========================================

  private static generateAuditId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static async saveAuditLog(auditLog: AuditLog): Promise<void> {
    // 这里应该实现实际的数据库保存逻辑
    // 可以保存到专门的审计日志表或外部日志系统
    console.log('Audit log saved:', auditLog);
  }

  private static async queryAuditLogs(
    filters: AuditFilter[],
    options: QueryOptions
  ): Promise<AuditQueryResult> {
    // 这里应该实现实际的数据库查询逻辑
    // 返回模拟数据
    return {
      items: [],
      total: 0,
      page: options.page || 1,
      limit: options.limit || 50,
      hasMore: false,
    };
  }

  private static aggregateStatistics(
    logs: AuditLog[],
    groupBy: string
  ): OperationStatistics {
    const stats: Record<string, number> = {};

    for (const log of logs) {
      let key: string;

      switch (groupBy) {
        case 'user':
          key = log.user_id;
          break;
        case 'entity_type':
          key = log.entity_type || 'unknown';
          break;
        default:
          key = log.operation;
      }

      stats[key] = (stats[key] || 0) + 1;
    }

    return {
      total_operations: logs.length,
      breakdown: stats,
      time_range: {
        start: logs[0]?.timestamp || '',
        end: logs[logs.length - 1]?.timestamp || '',
      },
    };
  }

  private static aggregateUserActivity(
    logs: AuditLog[]
  ): UserActivityStatistics {
    const userStats: Record<string, UserActivity> = {};

    for (const log of logs) {
      if (!userStats[log.user_id]) {
        userStats[log.user_id] = {
          user_id: log.user_id,
          total_operations: 0,
          operations_by_type: {},
          first_activity: log.timestamp,
          last_activity: log.timestamp,
        };
      }

      const userStat = userStats[log.user_id]!;
      userStat.total_operations++;
      userStat.operations_by_type[log.operation] =
        (userStat.operations_by_type[log.operation] || 0) + 1;

      if (log.timestamp < userStat.first_activity) {
        userStat.first_activity = log.timestamp;
      }

      if (log.timestamp > userStat.last_activity) {
        userStat.last_activity = log.timestamp;
      }
    }

    return {
      active_users: Object.keys(userStats).length,
      user_activities: Object.values(userStats),
    };
  }
}

// ========================================
// 类型定义
// ========================================

type ConfigAuditOperation =
  | 'CREATE_CONFIG'
  | 'UPDATE_CONFIG'
  | 'DELETE_CONFIG'
  | 'RESTORE_CONFIG'
  | 'REORDER_CONFIGS'
  | 'BATCH_UPDATE_STATUS';

type ContentAuditOperation =
  | 'CREATE_CONTENT'
  | 'UPDATE_CONTENT'
  | 'DELETE_CONTENT'
  | 'BATCH_UPDATE_CONTENT';

type BatchAuditOperation =
  | 'BATCH_IMPORT'
  | 'BATCH_EXPORT'
  | 'BATCH_SYNC'
  | 'BATCH_COPY';

interface AuditLog {
  id: string;
  timestamp: string;
  user_id: string;
  user_ip?: string;
  user_agent?: string;
  operation_type: 'CONFIG' | 'CONTENT' | 'BATCH';
  operation: string;
  entity_type?: EntityType;
  entity_id?: string;
  language_code?: LanguageCode;
  details: Record<string, any>;
  metadata?: Record<string, any>;
}

interface AuditContext {
  userId: string;
  userIp?: string;
  userAgent?: string;
  requestId?: string;
  sessionId?: string;
  apiVersion?: string;
}

interface ConfigOperationDetails {
  entityType: EntityType;
  languageCode: LanguageCode;
  configId: string;
  configKey: string;
  configLabel: string;
  isPreset: boolean;
  changes?: string[];
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
}

interface ContentOperationDetails {
  entityType: EntityType;
  entityId: string;
  languageCode: LanguageCode;
  contentType: string;
  contentLength?: number;
  contentHash?: string;
  changes?: string[];
}

interface BatchOperationDetails {
  entityType?: EntityType;
  languageCode?: LanguageCode;
  batchSize: number;
  successCount: number;
  failedCount: number;
  affectedItems: string[];
  operationSummary: string;
  executionTimeMs?: number;
}

interface AuditFilter {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'like';
  value: any;
}

interface QueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  includeDetails?: boolean;
}

interface AuditQueryResult {
  items: AuditLog[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface TimeRange {
  start: string;
  end: string;
}

interface OperationStatistics {
  total_operations: number;
  breakdown: Record<string, number>;
  time_range: TimeRange;
}

interface UserActivity {
  user_id: string;
  total_operations: number;
  operations_by_type: Record<string, number>;
  first_activity: string;
  last_activity: string;
}

interface UserActivityStatistics {
  active_users: number;
  user_activities: UserActivity[];
}
